import path from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    include: ['./**/*.test.ts'],
    reporters: process.env.CI ? ['dot', 'github-actions'] : ['default'],
    environment: 'happy-dom',
    coverage: {
      provider: 'istanbul',
      reporter: ['lcov', 'text-summary', 'json'],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
    },
  },
});
