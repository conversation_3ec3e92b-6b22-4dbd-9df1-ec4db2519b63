import { getRequestConfig } from 'next-intl/server';
import { headers } from 'next/headers';

// Define the locales your application supports
const locales = ['en', 'zh-tw', 'zh-cn'];

async function getPreferredLocale(): Promise<string> {
  // Try to get the Accept-Language header
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language');

  if (!acceptLanguage) return 'en';

  // Parse the Accept-Language header
  // Example: "en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7"
  const preferredLocales = acceptLanguage
    .split(',')
    .map((item) => {
      const [lang, weight] = item.trim().split(';q=');
      return {
        tag: lang.toLowerCase(),
        q: weight ? parseFloat(weight) : 1.0,
      };
    })
    .sort((a, b) => b.q - a.q);

  // Match against supported locales
  for (const preference of preferredLocales) {
    // Direct match
    if (locales.includes(preference.tag)) {
      return preference.tag;
    }

    // Handle specific language variants
    if (preference.tag.startsWith('zh-tw') || preference.tag.startsWith('zh-hant')) {
      return 'zh-tw';
    } else if (preference.tag.startsWith('zh-cn') || preference.tag.startsWith('zh-hans') || preference.tag === 'zh') {
      return 'zh-cn';
    } else if (preference.tag.startsWith('en')) {
      return 'en';
    }
  }

  // Default fallback
  return 'en';
}

export default getRequestConfig(async () => {
  const preferredLocale = await getPreferredLocale();

  return {
    locale: preferredLocale,
    messages: (await import(`../messages/${preferredLocale}.json`)).default,
  };
});
