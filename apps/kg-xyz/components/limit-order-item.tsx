'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { SOL_ADDRESS, USDC_ADDRESS } from '@/lib/chains';
import { formatNumber } from '@/lib/format';
import { cancelJupiterOrder } from '@/lib/jupiter';
import { getTokenInfo } from '@/lib/okx';
import { processTransactionData } from '@/lib/sign-transaction';
import { JupiterLimitedOrder } from '@/types/jupiter';
import { Button } from '@kryptogo/2b';
import * as Sentry from '@sentry/nextjs';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { useQuery } from '@tanstack/react-query';

import { CopyButton } from './copy-button';

interface LimitOrderItemProps {
  order: JupiterLimitedOrder;
}

export function LimitOrderItem({ order }: LimitOrderItemProps) {
  const { prices, registerToken } = usePrices();
  const { address: publicKey } = useDebugWallet();
  const { sendTransaction } = useWallet();
  const { connection } = useConnection();
  const t = useTranslations();
  const { toast } = useToast();

  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 0;

  const isSellOrder = order.outputMint === SOL_ADDRESS || order.outputMint === USDC_ADDRESS;
  const isBuy = !isSellOrder;
  const isSOL = order.outputMint === SOL_ADDRESS || order.inputMint === SOL_ADDRESS;

  const [tokenDecimals, setTokenDecimals] = useState(6);

  const tokenAddress = isSellOrder ? order.inputMint : order.outputMint;

  // Calculate partial fill status
  const isPartialFill = order.trades.length > 0 && order.status !== 'Completed';
  const remainingAmount = isBuy
    ? parseFloat(order.rawRemainingTakingAmount || '0') / 10 ** tokenDecimals
    : parseFloat(order.rawRemainingMakingAmount || '0') / 10 ** tokenDecimals;

  useEffect(() => {
    registerToken(tokenAddress);
    registerToken(SOL_ADDRESS);
  }, [registerToken, tokenAddress]);

  useEffect(() => {
    if (!connection) return;
    connection.getAccountInfo(new PublicKey(tokenAddress)).then((res) => setTokenDecimals(res?.data?.at(44) || 0));
  }, [connection, tokenAddress]);

  const { data: token } = useQuery({
    queryKey: ['token', tokenAddress],
    queryFn: () => getTokenInfo(tokenAddress, '501'),
  });

  const tokenAmount = isBuy
    ? parseFloat(order.rawTakingAmount) / 10 ** tokenDecimals
    : parseFloat(order.rawMakingAmount) / 10 ** tokenDecimals;
  const baseTokenAmount = isBuy
    ? parseFloat(order.rawMakingAmount) / (isSOL ? 1e9 : 1e6)
    : parseFloat(order.rawTakingAmount) / (isSOL ? 1e9 : 1e6);

  const handleCancelOrder = (orderKey: string) => {
    console.log('Canceling order:', orderKey);

    if (publicKey) {
      cancelJupiterOrder(orderKey, publicKey)
        .then(async (x) => {
          if (x.error) throw new Error(x.error.name, { cause: x.error });
          const unSignedTransaction = processTransactionData(x.transaction);
          if (!unSignedTransaction) {
            Sentry.captureException('Jupiter Swap Error');
            toast({
              variant: 'destructive',
              title: t('limit-orders.request-error'),
              description: t('limit-orders.jupiter-swap-error'),
            });
            return;
          }
          return sendTransaction(unSignedTransaction, connection);
        })
        .catch((error: Error) =>
          toast({
            variant: 'destructive',
            title: `${t('limit-orders.request-error')}: ${error.message}`,
            description: JSON.stringify(error.cause),
          }),
        );
    }
  };

  return (
    <div className="mb-4 mt-2 border-b border-neutral-800 px-4 py-2 pb-6 last:mb-0 last:border-b-0 last:pb-0">
      <div className="mb-2 flex items-center justify-between">
        <Link href={`/token/501/${tokenAddress}`} className="flex items-center gap-2">
          <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-zinc-800">
            <img src={token?.tokenLogoUrl} alt={token?.tokenSymbol || 'token'} className="h-full w-full object-cover" />
          </div>
          <div>
            <div className="text-lg font-bold">
              {/* This would be the token name/symbol - using placeholder */}
              {token?.tokenSymbol}
            </div>
            <div className="flex items-center gap-0">
              <span className="text-xs text-zinc-500">
                {tokenAddress.slice(0, 7)}...{tokenAddress.slice(-5)}
              </span>
              <CopyButton text={tokenAddress} />
            </div>
          </div>
        </Link>
        <div className="text-right">
          <div
            className={`mb-1 inline-block rounded px-2 py-0.5 text-xs ${
              isBuy ? 'bg-emerald-500/10 text-[#00B38C]' : 'bg-pink-500/10 text-pink-500'
            }`}
          >
            {isBuy ? 'Buy Dip' : 'Auto Sell'}
            {isPartialFill && (
              <div className="inline-block rounded bg-blue-950 px-2 py-0.5 text-xs text-blue-400">
                {t('limit-orders.partial-fill', { default: 'Partial Fill' })}
              </div>
            )}
          </div>
          <div className={`mt-1 text-lg font-bold ${isBuy ? 'text-[#00B38C]' : 'text-pink-500'}`}>
            {formatNumber(baseTokenAmount, {
              maximumFractionDigits: 2,
            })}{' '}
            {isSOL ? 'SOL' : 'USDC'}
          </div>
        </div>
      </div>

      <div className="mt-4 grid grid-cols-3 gap-4">
        <div>
          <div className="text-[0.75rem] text-zinc-400">
            {t('limit-orders.total-usd', { defaultMessage: 'Total USD' })}
          </div>
          <div className={`text-sm font-medium`}>
            $
            {formatNumber(baseTokenAmount * +(isSOL ? solPrice : 1), {
              maximumFractionDigits: 2,
            })}
          </div>
        </div>
        <div>
          <div className="text-[0.75rem] text-zinc-400">{t('limit-orders.amount', { defaultMessage: 'Amount' })}</div>
          <div className="text-sm font-medium text-white">
            {formatNumber(tokenAmount, {
              maximumFractionDigits: 2,
            })}
            {isPartialFill && (
              <>
                <br />
                <span>({((1 - remainingAmount / tokenAmount) * 100).toFixed(2)}%)</span>
              </>
            )}
          </div>
        </div>
        <div>
          <div className="text-[0.75rem] text-zinc-400">{t('my-activities.price', { defaultMessage: 'Price' })}</div>
          <div className={`text-sm font-medium`}>
            $
            {formatNumber((baseTokenAmount * +(isSOL ? solPrice : 1)) / tokenAmount, {
              maximumFractionDigits: 6,
            })}
          </div>
        </div>
      </div>

      <div className="mt-6 flex items-center justify-between">
        <div className="text-[0.75rem] text-zinc-400">
          {new Date(order.createdAt).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}
          {' • '}
          {new Date(order.createdAt).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
          })}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCancelOrder(order.orderKey)}
          className="rounded-lg bg-zinc-800/50 px-4 py-1.5 text-xs text-white hover:bg-zinc-700"
        >
          {t('limit-orders.cancel', { defaultMessage: 'Cancel' })}
        </Button>
      </div>
    </div>
  );
}
