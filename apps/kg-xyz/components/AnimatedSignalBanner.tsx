'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { FlexibleTextContainer } from '@/components/flexible-text-container';
import { useTokenInfoWithOptionalAddressQuery } from '@/hooks/api-queries';
import { cn } from '@/lib/utils';

/**
 * AnimatedSignalBanner component
 *
 * @param {AnimatedSignalBannerProps} props
 * @returns {JSX.Element}
 */
type AnimatedSignalBannerProps = {
  signalStatus?: {
    last_7d_2x_ratio?: number;
    last_7d_2x_avg_gain?: number;
    today_top_signal?: {
      gain?: number;
      highest_gain?: number;
      tokenSymbol?: string;
      token_address?: string;
      tokenImage?: string;
    };
  };
  formatNumber?: (value: number, options?: any) => string;
};

export function AnimatedSignalBanner({ signalStatus, formatNumber }: AnimatedSignalBannerProps) {
  const [index, setIndex] = useState(0);
  const t = useTranslations('signal-banner');
  const router = useRouter();

  // Function to navigate to token page
  const navigateToToken = () => {
    if (token?.chainName && token?.tokenContractAddress) {
      const network = token.chainName.toLowerCase() === 'solana' ? 'sol' : 'eth';
      router.push(`/token/${network}/${token.tokenContractAddress}`);
    }
  };

  // 查詢 token info（需放在 TEXTS 宣告之前，避免變數提升錯誤）
  const tokenAddress = signalStatus?.today_top_signal?.token_address;

  // Use the centralized token info query

  const { data: token } = useTokenInfoWithOptionalAddressQuery({
    variables: { tokenAddress: tokenAddress || '' },
    enabled: !!tokenAddress,
  });

  const TEXTS = [
    <>
      {t('last-7d-2x-accuracy')}
      <span className="ml-3 align-middle text-base font-bold text-yellow-400 md:text-lg">
        {formatNumber
          ? `${formatNumber((signalStatus?.last_7d_2x_ratio ?? 0) * 100 || 65, { maximumFractionDigits: 0 })}%`
          : '65%'}
      </span>
    </>,
    <>
      <span className="flex items-center">
        {t('avg-max-gain')}
        <span className="ml-3 align-middle text-base font-bold text-yellow-400 md:text-lg">
          {formatNumber
            ? `+${formatNumber((signalStatus?.last_7d_2x_avg_gain ?? 0) * 100 || 288, { maximumFractionDigits: 0 })}%`
            : '+288%'}
        </span>
      </span>
    </>,
    <>
      <span className="flex items-center">
        <span className="whitespace-nowrap">{t('todays-top-signal')}</span>
        {/* Token info：與標題同一行，並隨動畫移動 */}
        <FlexibleTextContainer className="ml-3 flex flex-1 items-center gap-2" minFontSize={10} maxFontSize={16}>
          <div className="flex w-full cursor-pointer items-center gap-2" onClick={navigateToToken}>
            {token?.tokenLogoUrl && (
              <Image
                src={token.tokenLogoUrl}
                alt={token.tokenSymbol || 'token'}
                className="h-5 w-5 flex-shrink-0 rounded-full bg-zinc-800 object-contain md:h-6 md:w-6"
                width={24}
                height={24}
              />
            )}
            <span className="truncate font-bold uppercase text-white">{token?.tokenSymbol || '-'}</span>
            {/* Data */}
            <span className="ml-auto whitespace-nowrap font-bold text-yellow-400">
              {formatNumber
                ? `+${formatNumber((signalStatus?.today_top_signal?.highest_gain ?? 0) * 100, { maximumFractionDigits: 0 })}%`
                : '+0%'}
            </span>
          </div>
        </FlexibleTextContainer>
      </span>
    </>,
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setIndex((prev) => (prev + 1) % TEXTS.length);
    }, 2500);
    return () => clearInterval(timer);
  }, [TEXTS.length]);

  return (
    <div className="relative flex h-10 w-[24rem] max-w-xl overflow-hidden rounded-none">
      {/* Left color bar & circle */}
      {/* <div className="flex items-center bg-[#ff972e] w-24 h-full justify-center">
        <div className="bg-zinc-900 rounded-full" style={{ width: 20, height: 20 }} />
      </div> */}
      {/* Animated text */}
      <div className="relative flex h-full flex-1 items-center justify-center overflow-hidden">
        <div className="flex h-full w-full items-center justify-center">
          <div className="relative flex h-10 w-full items-center justify-center overflow-hidden">
            <div
              className="absolute left-0 top-0 w-full transition-transform duration-700"
              style={{ transform: `translateY(-${index * 2.5}rem)` }}
            >
              {TEXTS.map((text, i) => (
                <div
                  key={i}
                  className={cn(
                    'font-base flex h-10 w-full items-center justify-center text-xs uppercase text-white transition-all md:text-sm',
                    index === i ? 'scale-100 opacity-100' : 'scale-95 opacity-0',
                    i === 2 && 'cursor-pointer',
                  )}
                  style={{ transition: 'opacity 0.7s, transform 0.7s' }}
                  onClick={i === 2 ? navigateToToken : undefined}
                >
                  {text}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
