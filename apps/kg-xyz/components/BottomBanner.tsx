'use client';

import { GraduationCap } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React from 'react';

import { usePrices } from '@/contexts/price-context';
import { SOL_ADDRESS } from '@/lib/chains';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import type { SignalStatusResponse } from '@/types/kg-api';
import { Button } from '@kryptogo/2b';

import { AnimatedSignalBanner } from './AnimatedSignalBanner';

export function BottomBanner({ signalStatus }: { signalStatus: SignalStatusResponse }) {
  const t = useTranslations('bottom-banner');
  const TELEGRAM_LINK = 'https://t.me/pump_hound_signal';

  const { prices, registerToken } = usePrices();
  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 0;

  React.useEffect(() => {
    registerToken(SOL_ADDRESS);
  }, [registerToken]);

  const handleClick = () => {
    window.open(TELEGRAM_LINK, '_blank');
  };

  return (
    <div
      className={cn(
        'fixed bottom-0 left-0 z-50 flex h-8 w-full items-center justify-center border-t border-zinc-800 bg-zinc-900/40 px-4 text-base text-white backdrop-blur-2xl md:justify-end',
        'shadow-[0_-2px_16px_0_rgba(0,0,0,0.24)]',
      )}
    >
      <div className="flex items-center">
        {solPrice > 0 && (
          <div className="mx-2 hidden items-center gap-1.5 rounded px-2 py-1 text-sm font-medium text-[#00B38C] md:flex">
            <Image
              src="/chain-icons/icon-solana.png"
              alt="SOL"
              width={14}
              height={14}
              className="h-4 w-4 rounded-full"
            />
            <span>${solPrice.toFixed(2)}</span>
          </div>
        )}
        {/* 直線分隔 */}
        <div className="mx-2 hidden h-4 w-px bg-zinc-700 md:block" />
        {/* 教育 */}
        <Button
          className="hidden items-center gap-1.5 rounded px-2 py-2 text-sm font-normal text-zinc-400 hover:bg-zinc-800 md:flex"
          onClick={() =>
            window.open('https://www.notion.so/kryptogo/KryptoGO-xyz-1e33499de8a2808291d7c98dc0703f6f', '_blank')
          }
        >
          <GraduationCap className="h-3.5 w-3.5 text-white" />
          {t('tutorial')}
        </Button>
        {/* 直線分隔 */}
        <div className="mx-2 hidden h-4 w-px bg-zinc-700 md:block" />
        <Button
          className="hidden items-center gap-1.5 rounded px-2 py-2 text-sm font-normal text-zinc-400 hover:bg-zinc-800 md:flex"
          onClick={handleClick}
        >
          <Image src="/social-icon/telegram.svg" alt="Telegram" width={12} height={12} className="mr-0" />
          {t('get-instant-signal')}
        </Button>
        {/* 直線分隔 */}
        <div className="mx-2 hidden h-4 w-px bg-zinc-700 md:block" />
        {/* App Button */}
        <Button
          className="hidden items-center gap-1.5 rounded px-2 py-2 text-sm font-normal text-zinc-400 hover:bg-zinc-800 md:flex"
          onClick={() => window.open('https://www.kryptogo.com/products/wallet', '_blank')}
        >
          <Image src="/favicon.ico" alt="App" width={14} height={14} className="h-4 w-4 rounded" />
          {t('app')}
        </Button>
        {/* 直線分隔 */}
        <div className="mx-2 hidden h-4 w-px bg-zinc-700 md:block" />
        {/* 動態訊號 */}
        <AnimatedSignalBanner signalStatus={signalStatus} formatNumber={formatNumber} />
      </div>
    </div>
  );
}
