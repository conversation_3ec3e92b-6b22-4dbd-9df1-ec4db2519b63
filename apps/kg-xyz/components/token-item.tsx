'use client';

import { X } from 'lucide-react';
import MotionNumber from 'motion-number';
import * as motion from 'motion/react-client';
import { useTranslations } from 'next-intl';
import Link from 'next/dist/client/link';
import React, { useEffect, useMemo, useState } from 'react';

import { TradingSection } from '@/app/token/[network]/[address]/components/trading-section';
import { Button } from '@/components/ui/button';
import { usePrices } from '@/contexts/price-context';
import { useTransactions } from '@/hooks/use-transactions';
import { formatNumber } from '@/lib/format';
import { getTokenInfo } from '@/lib/okx';
import { calAveragePositionCost, cn } from '@/lib/utils';
import type { SellSignalResponse, SignalResponse } from '@/types/kg-api';
import { OKXAssetData } from '@/types/okx';
import { Skeleton } from '@kryptogo/2b';

import { BuyButton } from './buy-button';
import { CopyButton } from './copy-button';
import { getSignalRank } from './signal-card';
import { SignalTooltipContent } from './signal-tooltip-content';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Drawer, DrawerContent, DrawerClose } from './ui/drawer';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

/* eslint-disable @next/next/no-img-element */
/* eslint-disable jsx-a11y/alt-text */

interface TokenItemProps {
  token: OKXAssetData;
  sellSignal?: SellSignalResponse;
  smallSellSignal?: SignalResponse;
  useMotion?: boolean;
  updateUnrealizedPnl?: (unrealizedPnl: [string, number]) => void;
}

export function TokenItem({
  token,
  sellSignal,
  smallSellSignal,
  updateUnrealizedPnl,
  useMotion = true,
}: TokenItemProps) {
  const t = useTranslations('token-item');
  const tPanel = useTranslations('trading-stats-panel');
  const [logoUrl, setLogoUrl] = useState('');
  const [logoLoaded, setLogoLoaded] = useState(false);
  const [isTradeModalOpen, setIsTradeModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Listen to window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup listener
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const { prices } = usePrices();

  useEffect(() => {
    setLogoLoaded(false);
    getTokenInfo(token.tokenContractAddress, '501').then((x) => {
      setLogoUrl(x.tokenLogoUrl);
    });
  }, [token.tokenContractAddress]);

  // Generate a random refetch interval between 5-15 seconds to avoid 429 errors
  const randomRefetchInterval = useMemo(() => Math.floor(Math.random() * 10000) + 5000, []);

  const { transactions } = useTransactions({
    tokenAddress: token.tokenContractAddress,
    refetchInterval: randomRefetchInterval,
  });

  const averagePositionCost = useMemo(() => calAveragePositionCost(transactions), [transactions]);
  const price = prices?.[`sol:${token.tokenContractAddress}`] ?? token.tokenPrice ?? 0;

  const unrealizedPnl = +token.balance * price - +token.balance * averagePositionCost;

  useEffect(() => {
    averagePositionCost !== 0 && updateUnrealizedPnl?.([token.tokenContractAddress, unrealizedPnl]);
  }, [updateUnrealizedPnl, unrealizedPnl, token.tokenContractAddress, averagePositionCost]);

  const positiveGain = unrealizedPnl >= 0;

  // if (averagePositionCost === 0) {
  //   return null;
  // }
  const { buy_entry_price = 0, win_rate = 0, average_holding = 0, average_win_rate = 0 } = smallSellSignal || {};

  const signalRank = getSignalRank(win_rate || 0);
  const averageHoldingRank = getSignalRank(average_holding || 0);
  const averageWinRateRank = getSignalRank(average_win_rate || 0);
  return (
    <div className="mb-4 mt-4 border-b border-neutral-800 px-3 py-2 pb-6 last:mb-0 last:border-b-0 last:pb-0">
      <TooltipProvider>
        <Tooltip delayDuration={200}>
          {sellSignal && (
            <div className="mb-2 flex h-10 w-full items-center justify-center bg-pink-500/10 text-pink-600">
              {tPanel('smart-money-exit')}
            </div>
          )}
          <TooltipTrigger asChild>
            {smallSellSignal && (
              <div className="mb-2 flex h-10 w-full items-center justify-center bg-orange-500/15 text-orange-500">
                {tPanel('reduce-position')}
              </div>
            )}
          </TooltipTrigger>
          <TooltipContent side="top" className="w-dvw border-none p-0 sm:w-[600px]">
            <SignalTooltipContent
              signalRank={signalRank}
              averageHoldingRank={averageHoldingRank}
              averageWinRateRank={averageWinRateRank}
              average_holding={average_holding}
              average_win_rate={average_win_rate}
              token_price={price == 0.0 ? +(token?.tokenPrice || 0.0) : price}
              buy_entry_price={+buy_entry_price}
            />
          </TooltipContent>
        </Tooltip>
        <motion.div
          variants={{
            visible: { opacity: 1, x: 0, transition: { duration: 0.2 } },
            hidden: { opacity: 0, x: 50, transition: { duration: 0.2 } },
          }}
          className="border-zinc-800"
        >
          <div className="flex items-center justify-between">
            <div className="flex w-full items-center gap-2">
              <Link
                href={`/token/501/${token.tokenContractAddress}`}
                className="relative h-8 w-8 flex-shrink-0 overflow-hidden rounded-full bg-zinc-800"
              >
                <div className="relative h-full w-full overflow-hidden rounded-full bg-zinc-800 duration-500">
                  {logoUrl ? (
                    <>
                      <img
                        src={logoUrl}
                        alt={token.symbol}
                        className="h-full w-full object-cover"
                        onLoad={() => setLogoLoaded(true)}
                        style={{ display: logoLoaded ? 'block' : 'none' }}
                      />
                      {!logoLoaded && <Skeleton className="absolute left-0 top-0 h-full w-full rounded-full" />}
                    </>
                  ) : (
                    <Skeleton className="h-full w-full rounded-full" />
                  )}
                </div>
              </Link>
              <div className="flex flex-1 flex-col">
                <Link href={`/token/501/${token.tokenContractAddress}`}>
                  <div className="flex items-center gap-2">
                    <span className="text-base font-bold text-white">{token.symbol}</span>
                  </div>
                </Link>
                <div className="flex items-center gap-0">
                  <span className="text-[0.65rem] text-zinc-500">
                    {token.tokenContractAddress.slice(0, 7)}...{token.tokenContractAddress.slice(-5)}
                  </span>
                  <CopyButton text={token.tokenContractAddress} />
                </div>
              </div>
              <div className="flex flex-col items-end">
                <span className="text-center text-[0.75rem] font-medium leading-none text-neutral-400">
                  {tPanel('unrealized-roi')}
                </span>
                <div
                  className={cn(
                    'mt-1 text-center text-lg font-bold',
                    positiveGain ? 'text-[#00B38C]' : 'text-pink-600',
                  )}
                >
                  <span className="inline-flex items-center justify-center">
                    {positiveGain ? '+' : ''}
                    {useMotion ? (
                      <MotionNumber
                        value={((price - averagePositionCost) / averagePositionCost) * 100}
                        format={{
                          style: 'decimal',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 2,
                          useGrouping: true,
                        }}
                      />
                    ) : (
                      formatNumber(((price - averagePositionCost) / averagePositionCost) * 100, {
                        maximumFractionDigits: 2,
                      })
                    )}
                    %
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="m-1 my-6 flex justify-between">
            <div>
              <div className="text-xs text-zinc-400">{tPanel('balance-usd')}</div>
              <div className="text-sm text-white">
                {formatNumber(+token.balance * price, {
                  notation: 'compact',
                  maximumFractionDigits: 2,
                })}
              </div>
            </div>
            <div className="text-right">
              <div className="text-xs text-zinc-400">{tPanel('unrealized-pnl')}</div>
              <div className={cn('text-sm', positiveGain ? 'text-[#00B38C]' : 'text-pink-600')}>
                {averagePositionCost
                  ? `${positiveGain ? '+' : ''}${formatNumber(unrealizedPnl, { notation: 'compact', maximumFractionDigits: 6 })}`
                  : '-'}
              </div>
            </div>
          </div>
          <span className="text-[0.65rem] text-zinc-400">{tPanel('sell')}</span>
          <div className="mt-1 grid grid-cols-4 gap-1">
            <BuyButton
              tokenAddress={token.tokenContractAddress}
              label="25%"
              rawAmount={+token.rawBalance * 0.25}
              sellMode
            />
            <BuyButton
              tokenAddress={token.tokenContractAddress}
              label="50%"
              rawAmount={+token.rawBalance * 0.5}
              sellMode
            />
            <BuyButton
              tokenAddress={token.tokenContractAddress}
              label="75%"
              rawAmount={+token.rawBalance * 0.75}
              sellMode
            />
            <BuyButton tokenAddress={token.tokenContractAddress} label="100%" rawAmount={+token.rawBalance} sellMode />
          </div>
          <div className="mt-2 text-xs">
            or{' '}
            <button onClick={() => setIsTradeModalOpen(true)} className="mt-2 text-xs text-yellow-400">
              {tPanel('set-auto-sell')}
            </button>
          </div>
          {/* Trading Modal - Desktop */}
          {!isMobile && (
            <Dialog open={isTradeModalOpen} onOpenChange={setIsTradeModalOpen}>
              <DialogContent className="z-[40] border border-zinc-800/50 bg-[#0C0C0C] sm:max-w-md">
                <DialogHeader className="flex flex-row items-center justify-between">
                  <DialogTitle className="text-xl font-bold text-white">
                    {token?.symbol} {t('trading')}
                  </DialogTitle>
                  <Button variant="ghost" className="h-8 w-8 p-0" onClick={() => setIsTradeModalOpen(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </DialogHeader>
                <TradingSection
                  tokenAddress={token.tokenContractAddress}
                  tokenSymbol={token?.symbol || ''}
                  afterTrade={() => setIsTradeModalOpen(false)}
                  defaultTradingMode={'sell'}
                  defaultLimitOrderMode
                />
              </DialogContent>
            </Dialog>
          )}

          {/* Trading Modal - Mobile (Bottom Sheet) */}
          {isMobile && (
            <Drawer open={isTradeModalOpen} onOpenChange={setIsTradeModalOpen}>
              <DrawerContent className="h-[90vh] max-h-[90vh] border-t border-zinc-800/50 bg-zinc-950/95 backdrop-blur-md before:hidden">
                <div className="sticky top-0 z-10 hidden items-center justify-between border-b border-zinc-800/50 bg-zinc-950 px-4 py-4 md:flex">
                  <div className="w-10"></div>
                  <h3 className="text-base font-medium text-white">
                    {token?.symbol} {t('trading')}
                  </h3>
                  <DrawerClose className="flex h-10 w-10 items-center justify-center rounded-full text-zinc-400 hover:bg-zinc-800/50 hover:text-white">
                    <X className="h-5 w-5" />
                  </DrawerClose>
                </div>
                <div className="h-[calc(100vh-60px)] overflow-y-auto p-4">
                  <TradingSection
                    tokenAddress={token.tokenContractAddress}
                    tokenSymbol={token?.symbol || ''}
                    afterTrade={() => setIsTradeModalOpen(false)}
                    defaultTradingMode={'sell'}
                    defaultLimitOrderMode
                  />
                </div>
              </DrawerContent>
            </Drawer>
          )}
        </motion.div>
      </TooltipProvider>
    </div>
  );
}

export const FoldedTokenItem = ({ token, sellSignal }: TokenItemProps) => {
  const [logoUrl, setLogoUrl] = useState('');
  const [logoLoaded, setLogoLoaded] = useState(false);
  const { prices } = usePrices();

  useEffect(() => {
    setLogoLoaded(false);
    getTokenInfo(token.tokenContractAddress, '501').then((x) => {
      setLogoUrl(x.tokenLogoUrl);
    });
  }, [token.tokenContractAddress]);

  // Generate a random refetch interval between 5-15 seconds to avoid 429 errors
  const randomRefetchInterval = useMemo(() => Math.floor(Math.random() * 10000) + 5000, []);

  const { transactions } = useTransactions({
    tokenAddress: token.tokenContractAddress,
    refetchInterval: randomRefetchInterval,
  });

  const averagePositionCost = useMemo(() => calAveragePositionCost(transactions), [transactions]);

  const price = prices?.[`sol:${token.tokenContractAddress}`] ?? token.tokenPrice ?? 0;
  const positiveGain = +price - averagePositionCost > 0;

  if (averagePositionCost === 0) {
    return null;
  }
  const roiPercent = ((+price - averagePositionCost) / averagePositionCost) * 100;

  return (
    <motion.div
      variants={{
        visible: { opacity: 1, x: 0 },
        hidden: { opacity: 0, x: '100%' },
      }}
    >
      {' '}
      <div>
        {sellSignal && (
          <div className="m-auto flex h-6 w-6 items-center justify-center rounded-full bg-pink-900/30">🚨</div>
        )}
        <div className="relative m-auto my-1 h-10 w-10">
          <Link
            href={`/token/501/${token.tokenContractAddress}`}
            className="relative h-12 w-12 overflow-hidden rounded-full bg-zinc-800"
          >
            <div className="h-full w-full overflow-hidden rounded-full duration-500">
              {logoUrl ? (
                <>
                  <img
                    src={logoUrl}
                    className="h-full w-full object-cover"
                    onLoad={() => setLogoLoaded(true)}
                    style={{ display: logoLoaded ? 'block' : 'none' }}
                  />
                  {!logoLoaded && <Skeleton className="absolute left-0 top-0 h-full w-full rounded-full" />}
                </>
              ) : (
                <Skeleton className="h-full w-full rounded-full" />
              )}
            </div>
          </Link>
          <div className="border-surface-primary absolute bottom-0 right-0 h-4 w-4 overflow-hidden rounded-full border bg-white">
            <img src={`/chain-icons/icon-solana.png`} className="h-full w-full object-cover" />
          </div>
        </div>
        <div className={cn('flex items-center', positiveGain ? 'text-[#00B38C]' : 'text-pink-500')}>
          <span>{positiveGain ? '+' : ''}</span>
          {averagePositionCost && (
            <span className="flex items-center">
              {isNaN(roiPercent) ? (
                '-'
              ) : (
                <>
                  <MotionNumber
                    value={roiPercent}
                    format={{
                      style: 'decimal',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 2,
                      useGrouping: true,
                    }}
                  />
                  %
                </>
              )}
            </span>
          )}
        </div>
      </div>
      <hr className="my-2 w-full border border-white/10 last:hidden" />
    </motion.div>
  );
};
