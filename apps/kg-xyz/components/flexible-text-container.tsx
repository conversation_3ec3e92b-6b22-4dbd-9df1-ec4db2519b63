'use client';

import React, { useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/utils';

interface FlexibleTextContainerProps {
  children: React.ReactNode;
  className?: string;
  minFontSize?: number; // 最小字體大小 (px)
  maxFontSize?: number; // 最大字體大小 (px)
  containerWidth?: number; // 容器固定寬度 (px)，不指定則使用父元素寬度
}

/**
 * 自動調整文字大小的容器元件
 * 當文字內容過長時，會自動縮小字體大小以避免換行
 */
export function FlexibleTextContainer({
  children,
  className,
  minFontSize = 12,
  maxFontSize = 16,
  containerWidth,
}: FlexibleTextContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [fontSize, setFontSize] = useState(maxFontSize);

  useEffect(() => {
    const adjustTextSize = () => {
      const container = containerRef.current;
      const text = textRef.current;

      if (!container || !text) return;

      // 重置字體大小以獲取原始寬度
      text.style.fontSize = `${maxFontSize}px`;

      // 獲取容器和文字的寬度
      const containerWidth = container.clientWidth;
      const textWidth = text.scrollWidth;

      // 如果文字寬度大於容器寬度，計算適當的字體大小
      if (textWidth > containerWidth) {
        const ratio = containerWidth / textWidth;
        const newSize = Math.max(minFontSize, Math.floor(maxFontSize * ratio));
        setFontSize(newSize);
      } else {
        setFontSize(maxFontSize);
      }
    };

    // 初始調整
    adjustTextSize();

    // 監聽視窗大小變化
    window.addEventListener('resize', adjustTextSize);

    return () => {
      window.removeEventListener('resize', adjustTextSize);
    };
  }, [children, minFontSize, maxFontSize]);

  return (
    <div
      ref={containerRef}
      className={cn('overflow-hidden whitespace-nowrap', className)}
      style={containerWidth ? { width: `${containerWidth}px` } : undefined}
    >
      <div ref={textRef} style={{ fontSize: `${fontSize}px` }} className="transition-all duration-200 ease-in-out">
        {children}
      </div>
    </div>
  );
}
