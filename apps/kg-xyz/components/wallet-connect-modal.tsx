'use client';

import { ChevronLeft } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import useIsMobile from '@/hooks/useIsMobile';
import { isBitgetDAppBrowser, isOKXDAppBrowser, isPhantomDAppBrowser, partition } from '@/lib/utils';
import * as Sentry from '@sentry/nextjs';
import { useWallet } from '@solana/wallet-adapter-react';

export enum WalletReadyState {
  Installed = 'Installed',
  NotDetected = 'NotDetected',
  Loadable = 'Loadable',
  Unsupported = 'Unsupported',
}

interface WalletConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
}
type Wallet = ReturnType<typeof useWallet>['wallet'];

interface WalletButtonProps {
  wallet: NonNullable<Wallet>;
  onSelect: (wallet: NonNullable<Wallet>) => void;
}

const WalletButton = ({ wallet, onSelect }: WalletButtonProps) => (
  <button onClick={() => onSelect(wallet)} className="flex w-full items-center p-2 transition-colors hover:bg-gray-50">
    <div className="mr-3 h-7 w-7 flex-shrink-0">
      <Image
        src={wallet.adapter.icon}
        alt={wallet.adapter.name}
        width={24}
        height={24}
        className="h-full w-full object-contain"
      />
    </div>
    <span className="text-sm font-bold text-gray-900">{wallet.adapter.name}</span>
  </button>
);

export function WalletConnectModal({ isOpen, onClose }: WalletConnectModalProps) {
  const t = useTranslations('wallet-connect-modal');
  const { wallets, select, connect, wallet, connected } = useWallet();
  const isMobile = useIsMobile();
  const [dummy, setDummy] = useState(0);

  const handleWalletSelect = (wallet: NonNullable<Wallet>) => {
    select(wallet.adapter.name);
    setDummy((prev) => prev + 1);
  };

  const handleOpenWalletUrl = (wallet: NonNullable<Wallet>) => {
    if (!isPhantomDAppBrowser() && wallet.adapter.name.includes('Phantom')) {
      // Create a deeplink to open the current page in Phantom
      const currentUrl = window.location.href;
      const phantomDeeplink = `https://phantom.app/ul/browse/${encodeURIComponent(currentUrl)}?ref=${encodeURIComponent(window.location.origin)}`;
      window.location.href = phantomDeeplink;
      return;
    }
    if (!isOKXDAppBrowser() && wallet.adapter.name.includes('OKX')) {
      // Create a deeplink to open the current page in OKX
      const encodedUrl =
        'https://www.okx.com/download?deeplink=' +
        encodeURIComponent('okx://wallet/dapp/url?dappUrl=' + encodeURIComponent(location.href));
      window.location.href = encodedUrl;
      return;
    }
    if (!isBitgetDAppBrowser() && wallet.adapter.name.includes('Bitget')) {
      // Create a deeplink to open the current page in Bitget
      const bitgetDeeplink = `bitkeep://bkconnect?action=dapp&url=${encodeURIComponent(location.href)}`;
      window.location.href = bitgetDeeplink;
      return;
    }
    window.open(wallet.adapter.url, '_blank');
  };

  useEffect(() => {
    setDummy((prev) => prev + 1);
    switch (true) {
      case isOKXDAppBrowser():
        const okxWallet = wallets.find((wallet) => wallet.adapter.name.includes('OKX'));
        okxWallet && select(okxWallet.adapter.name);
        return;
      case isBitgetDAppBrowser():
        const bitgetWallet = wallets.find((wallet) => wallet.adapter.name === 'Bitget');
        bitgetWallet && select(bitgetWallet.adapter.name);
        return;
      case isMobile:
        select(wallets[0].adapter.name);
        return;
      default:
        break;
    }
  }, [wallets, select, isMobile]);

  const params = useParams();
  const addressParam = typeof params.address === 'string' ? params.address : '';

  const handleDeepLink = useCallback(() => {
    const env = typeof window !== 'undefined' ? (window.location.href.includes('dev') ? 'dev' : 'prod') : 'prod';
    const address = addressParam;
    const prefix = env === 'dev' ? 'kryptogodev://' : 'kryptogo://';
    const deepLink = `${prefix}token/501/${address}`;
    const newWindow = window.open(deepLink, '_blank');

    if (!newWindow) {
      setTimeout(() => {
        window.location.href = deepLink;
      }, 500);
    }
  }, [addressParam]);

  useEffect(() => {
    console.log(dummy);
    // Only attempt to connect if we have selected a wallet
    if (wallet && wallet.adapter.name !== 'Mobile Wallet Adapter') {
      // Connect to the selected wallet
      connect().catch((error) => {
        console.error('Failed to connect to wallet:', error);
        Sentry.captureException(error);
      });
    }
  }, [dummy, connect, wallet, onClose]);

  useEffect(() => {
    if (connected) {
      onClose();
    }
  }, [connected, onClose]);

  const [installedWallets, recommendedWallets] = partition(
    wallets.filter((wallet) =>
      wallets.map((wallet) => wallet.adapter.name).includes('Bitget Wallet' as any)
        ? wallet.adapter.name !== 'Bitget'
        : true,
    ),
    (wallet) => wallet.readyState === WalletReadyState.Installed,
  );

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-sm overflow-hidden bg-white p-0 data-[state=open]:rounded-3xl">
        <div className="flex flex-col text-black">
          <div className="relative flex items-center justify-center border-b border-gray-100 p-4">
            <button
              onClick={onClose}
              className="absolute left-4 -ml-1 p-1 text-gray-400 transition-colors hover:text-gray-600"
            >
              <ChevronLeft strokeWidth={3} color="black" className="h-5 w-5" />
            </button>
            <h2 className="text-xl font-bold text-gray-900">{t('connect-wallet')}</h2>
          </div>
          {/* Wallet List */}
          <div className="p-4">
            {installedWallets.length > 0 && (
              <>
                <div className="mb-2 text-xs font-medium text-yellow-500">{t('installed')}</div>
                {installedWallets.map((wallet) => (
                  <WalletButton key={wallet.adapter.name} wallet={wallet} onSelect={handleWalletSelect} />
                ))}
              </>
            )}
            {recommendedWallets.length > 0 && (
              <>
                <div className="mb-2 text-xs font-medium text-gray-500">{t('supported')}</div>
                <div className="space-y-2">
                  {recommendedWallets.map((wallet) => (
                    <WalletButton key={wallet.adapter.name} wallet={wallet} onSelect={handleOpenWalletUrl} />
                  ))}
                </div>
              </>
            )}
          </div>
          {isMobile && addressParam && (
            <div className="relative">
              <div className="relative m-4 mt-0">
                <div className="absolute inset-0 flex items-center">
                  <div className="h-px w-full bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
                </div>
                <div className="relative flex justify-center">
                  <div className="flex items-center space-x-2 bg-white px-4 text-xs text-zinc-400">OR</div>
                </div>
              </div>
              <div className="px-4 pb-4">
                <div className="mb-3">
                  <button
                    onClick={handleDeepLink}
                    className="flex w-full flex-row items-center justify-center rounded-xl bg-yellow-400 py-3 text-sm font-semibold text-white transition-colors hover:bg-yellow-500"
                  >
                    <Image
                      src="/round-logo.svg"
                      alt="KryptoGO"
                      width={16}
                      height={16}
                      className="mr-1 h-4 w-4 rounded-full bg-[#12275a]"
                    />
                    {t('trade-button')}
                  </button>
                </div>

                <div className="mb-3">
                  <button
                    onClick={() => {
                      window.open('https://kryptogo.com/download', '_blank');
                    }}
                    className="w-full rounded-xl bg-gray-100 py-3 text-sm font-semibold text-gray-500 transition-colors"
                  >
                    {t('download-button')}
                  </button>
                </div>
                <div className="flex flex-col items-center">
                  <div className="mb-1 text-[10px] text-gray-400">{t('powered-by')}</div>
                  <Image src="/small-long-logo.svg" alt="KryptoGO" width={60} height={12} className="h-3 w-auto" />
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
