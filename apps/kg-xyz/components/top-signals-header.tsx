'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';

import { getTokenInfo } from '@/lib/okx';
import { Marquee } from '@devnomic/marquee';
import '@devnomic/marquee/dist/index.css';
import { useQuery } from '@tanstack/react-query';

interface SignalItemProps {
  token_address: string;
  highest_gain: number;
}

function SignalItem({ token_address, highest_gain }: SignalItemProps) {
  const { data: token } = useQuery({
    queryKey: ['token', token_address],
    queryFn: () => getTokenInfo(token_address, '501'),
  });

  return (
    <Link
      href={`/token/sol/${token_address}`}
      className="mx-4 inline-flex cursor-pointer items-center rounded-md px-2 py-1 text-sm font-medium transition-colors hover:bg-zinc-800/50"
    >
      <span className="mr-1 inline-flex h-5 w-5 items-center justify-center">
        {token?.tokenLogoUrl ? (
          <Image
            src={token.tokenLogoUrl}
            alt={token?.tokenSymbol || ''}
            className="h-full w-full rounded-full object-cover"
            height={24}
            width={24}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-zinc-800">
            <span className="animate-pulse text-xs text-zinc-400">...</span>
          </div>
        )}
      </span>
      <span className="mx-1 text-xs md:text-sm">
        {token?.tokenSymbol || <span className="h-4 w-16 animate-pulse rounded bg-zinc-700 opacity-70"></span>}
      </span>
      <span className="ml-1 text-xs text-[#00B38C]">
        +{parseInt((highest_gain * 100).toFixed(0)).toLocaleString()}%
      </span>
    </Link>
  );
}

export function TopSignalsHeader({
  pastTopSignals = [],
}: {
  pastTopSignals?: Array<{ token_address: string; highest_gain: number }>;
}) {
  const t = useTranslations('top-signals');

  return (
    <div className="left-0 top-[64px] z-40 flex w-full items-center gap-2 border-b border-zinc-800 px-2 py-0 shadow-lg md:px-4">
      <Image
        src="/frog-icon.svg"
        alt="Frog Icon"
        width={60}
        height={60}
        className="h-8 w-8 object-cover md:h-10 md:w-10"
      />
      <div className="mr-2 flex flex-col">
        <span className="text-xs font-medium text-white md:text-xs">{t('too-late-now')}</span>
        <span className="hidden text-[0.6rem] text-gray-400 md:block md:text-[0.7rem]">
          {t('recent-top-performing')}
        </span>
      </div>
      <div className="min-w-0 flex-1 overflow-hidden">
        <div className="flex h-[34px] items-center">
          <Marquee pauseOnHover={true}>
            {pastTopSignals.map((signal, index) => (
              <SignalItem
                key={`signal-${index}`}
                token_address={signal.token_address}
                highest_gain={signal.highest_gain}
              />
            ))}
          </Marquee>
        </div>
      </div>
    </div>
  );
}
