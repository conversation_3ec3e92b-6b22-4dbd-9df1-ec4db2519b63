'use client';

import Image from 'next/image';
import QRCodeUtil from 'qrcode';
import { useEffect, useState } from 'react';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { APPLE_APP_STORE_URL, GOOGLE_PLAY_URL } from '@kryptogo/utils';

interface TradeQRModalProps {
  isOpen: boolean;
  onClose: () => void;
  url: string;
}

export function TradeQRModal({ isOpen, onClose, url }: TradeQRModalProps) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');

  useEffect(() => {
    if (url) {
      QRCodeUtil.toDataURL(url, {
        width: 200,
        margin: 0,
        errorCorrectionLevel: 'M',
      })
        .then((dataUrl) => {
          setQrCodeDataUrl(dataUrl);
        })
        .catch((err) => {
          console.error('Error generating QR code:', err);
        });
    }
  }, [url]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md rounded-xl bg-white">
        <div className="flex flex-col items-center gap-6">
          <h2 className="text-2xl font-bold text-black">Trade in KryptoGO</h2>
          <p className="text-center text-lg text-black">Use KryptoGO app to scan and trade in seconds.</p>

          <div className="rounded-lg bg-white p-4">
            {qrCodeDataUrl && <Image src={qrCodeDataUrl} alt="QR Code" width={200} height={200} unoptimized />}
          </div>

          <div className="flex gap-4">
            <a href={APPLE_APP_STORE_URL} target="_blank" rel="noopener noreferrer">
              <Image src="/app-store-badge.svg" alt="Download on the App Store" width={156} height={48} />
            </a>
            <a href={GOOGLE_PLAY_URL} target="_blank" rel="noopener noreferrer">
              <Image src="/google-play-badge.svg" alt="Get it on Google Play" width={156} height={48} />
            </a>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
