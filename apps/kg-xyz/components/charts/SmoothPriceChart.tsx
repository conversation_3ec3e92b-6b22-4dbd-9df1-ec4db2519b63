import React from 'react';

interface SmoothPriceChartProps {
  prices: number[];
  width?: number;
  height?: number;
  padding?: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWidth?: number;
  showTopIndicator?: boolean;
}

export const SmoothPriceChart: React.FC<SmoothPriceChartProps> = ({
  prices,
  width = 526,
  height = 147,
  padding = 20,
  strokeColor = 'green',
  strokeOpacity = 0.8,
  strokeWidth = 7.5,
  showTopIndicator = true,
}) => {
  // Ensure all prices are valid numbers
  const validPrices = prices.map((p) => (typeof p === 'number' ? p : parseFloat(String(p)))).filter((p) => !isNaN(p));

  const scaleY = (price: number) => {
    const minPrice = Math.min(...validPrices);
    const maxPrice = Math.max(...validPrices);
    return height - padding - ((price - minPrice) / (maxPrice - minPrice)) * (height - 2 * padding);
  };

  const scaleX = (index: number) => {
    return padding + (index / (validPrices.length - 1)) * (width - 2 * padding);
  };

  // Find top point
  const maxPrice = Math.max(...validPrices);
  const maxPriceIndex = validPrices.indexOf(maxPrice);
  const isLeftHalf = maxPriceIndex < validPrices.length / 2;
  const topPoint = {
    x: scaleX(maxPriceIndex),
    y: scaleY(maxPrice),
  };

  // Calculate indicator line
  const indicatorLineLength = 200;
  const indicatorEndX = isLeftHalf ? topPoint.x + indicatorLineLength : topPoint.x - indicatorLineLength;

  // Generate smooth curve using cubic Bezier
  const generateSmoothPath = (points: Array<{ x: number; y: number }>) => {
    if (points.length < 2) return '';

    const firstPoint = points[0];
    let path = `M ${firstPoint.x},${firstPoint.y}`;

    for (let i = 0; i < points.length - 1; i++) {
      const current = points[i];
      const next = points[i + 1];

      // Control points for smooth curve
      const cp1x = current.x + (next.x - current.x) / 3;
      const cp1y = current.y;
      const cp2x = next.x - (next.x - current.x) / 3;
      const cp2y = next.y;

      path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${next.x},${next.y}`;
    }
    return path;
  };

  const points = validPrices.map((price, index) => ({
    x: scaleX(index),
    y: scaleY(price),
  }));

  const pathData = generateSmoothPath(points);

  if (validPrices.length < 2) {
    return null;
  }

  return (
    <div style={{ display: 'flex', width, height, position: 'relative' }}>
      <svg
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        fill="none"
        style={{ display: 'block', width: '100%', height: '100%' }}
      >
        <path
          d={pathData}
          stroke={strokeColor}
          strokeOpacity={strokeOpacity}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          style={{ display: 'block' }}
        />
        {showTopIndicator ? (
          <g>
            {/* Indicator line */}
            <line
              x1={topPoint.x}
              y1={topPoint.y}
              x2={indicatorEndX}
              y2={topPoint.y}
              stroke={'white'}
              strokeOpacity={strokeOpacity}
              strokeWidth={2}
              strokeDasharray="4,4"
            />

            {/* Price label background */}
            <rect
              x={isLeftHalf ? indicatorEndX + 5 : indicatorEndX - 165}
              y={topPoint.y - 60}
              width={160}
              height={80}
              rx={4}
              fill={'white'}
              fillOpacity={0.1}
            />
          </g>
        ) : null}
      </svg>
      {showTopIndicator && (
        <div
          style={{
            position: 'absolute',
            left: isLeftHalf ? indicatorEndX + 5 : indicatorEndX - 165,
            top: topPoint.y - 40,
            width: 160,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '40px',
            fontFamily: 'sans-serif',
            fontWeight: 500,
          }}
        >
          ${typeof maxPrice === 'number' ? maxPrice.toFixed(4) : '0.0000'}
        </div>
      )}
    </div>
  );
};
