'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

import { TRADING_CONFIG } from '@/config/trading';
import { useCreditPurchase } from '@/hooks/use-credit-purchase';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { useTokenAnalysisCredits } from '@/hooks/use-token-analysis-credits';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import * as Sentry from '@sentry/nextjs';

interface CreditDropdownProps {
  onCreditUpdate?: () => void;
  isDrawerContext?: boolean; // Added for mobile drawer context
}

interface CreditOption {
  id: string;
  credits: number;
  solAmount: number;
  label: string;
}

export function CreditDropdown({ onCreditUpdate, isDrawerContext }: CreditDropdownProps) {
  const t = useTranslations('credit-system');
  const { address: publicKey } = useDebugWallet();
  const { data: { credits, tradingVolume } = { credits: 0, tradingVolume: 0 } } = useTokenAnalysisCredits(publicKey);
  const { toast } = useToast();
  const { purchaseCredits } = useCreditPurchase();
  const [loading, setLoading] = useState<string | null>(null);

  // Mock trading volume data - this would come from your API
  const tradingVolumeSetting = {
    current: tradingVolume % 200,
    target: 200,
    remaining: 200 - (tradingVolume % 200),
  };

  const creditOptions: CreditOption[] = [
    {
      id: 'small',
      credits: 1,
      solAmount: TRADING_CONFIG.creditSinglePrice,
      label: `1 ${t('credits')}`,
    },
    {
      id: 'medium',
      credits: 10,
      solAmount: TRADING_CONFIG.credit10PackagePrice * 10,
      label: `10 ${t('credits')}`,
    },
    {
      id: 'large',
      credits: 50,
      solAmount: TRADING_CONFIG.credit50PackagePrice * 50,
      label: `50 ${t('credits')}`,
    },
  ];

  const handleDailyCredit = async () => {
    if (!publicKey) return;

    setLoading('daily-free');
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_analysis/free_credit/${publicKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to claim daily credit');
      }

      toast({
        title: t('daily-credit-claimed'),
        description: t('daily-credit-success'),
      });

      onCreditUpdate?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('error'),
        description: (error as Error).message || t('daily-credit-error'),
      });
    } finally {
      setLoading(null);
    }
  };

  const handleCreditPurchase = async (option: CreditOption) => {
    if (!publicKey) return;

    setLoading(option.id);
    try {
      const success = await purchaseCredits({
        credits: option.credits,
        solAmount: option.solAmount,
        walletAddress: publicKey,
      });

      if (success) {
        onCreditUpdate?.();
      }
    } catch (error) {
      Sentry.captureException(error);
    } finally {
      setLoading(null);
    }
  };

  const handleContactSupport = () => {
    window.open('https://t.me/crypto_godfather_zh', '_blank');
  };

  if (!publicKey) {
    return null;
  }

  const dropdownContentJSX = (
    <div className="space-y-6">
      {/* Credits Info */}
      <div className="flex flex-col items-center gap-6">
        <div className="flex flex-col items-center gap-0.5 p-4">
          <div className="text-5xl font-semibold text-white">
            {credits !== undefined ? formatNumber(credits, { maximumFractionDigits: 0 }) : '...'}
          </div>
          <div className="text-sm font-medium text-zinc-400">{t('available-credits')}</div>
        </div>

        {/* Divider */}
        <div className="h-px w-full bg-white/10" />
      </div>

      {/* Ways to Get Credits */}
      <div className="space-y-5">
        <div className="text-xs font-medium text-white">{t('ways-to-get-credits')}</div>

        <div className="space-y-4">
          {/* Daily Free Credit */}
          <div className="flex items-center justify-between rounded bg-white/10 p-3">
            <div className="flex flex-col gap-1">
              <div className="text-sm font-medium text-white">{t('daily-free-1-credit')}</div>
            </div>
            <button
              onClick={handleDailyCredit}
              disabled={loading === 'daily-free'}
              className={cn(
                'rounded-lg bg-yellow-400 px-4 py-2 text-xs font-bold text-black transition-colors',
                loading === 'daily-free' && 'opacity-50',
                'hover:bg-yellow-300',
              )}
            >
              {loading === 'daily-free' ? t('claiming') : t('claim')}
            </button>
          </div>

          {/* Tx Volume Progress */}
          <div className="space-y-3 rounded bg-white/10 p-3">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-white">{t('tx-volume')}</div>
              <div className="text-xs text-zinc-400">{t('tx-volume-description')}</div>
            </div>

            <div className="space-y-1">
              {/* Progress Bar */}
              <div className="h-1 w-full rounded-full bg-white/10">
                <div
                  className="h-1 rounded-full bg-yellow-400"
                  style={{ width: `${(tradingVolumeSetting.current / tradingVolumeSetting.target) * 100}%` }}
                />
              </div>

              {/* Progress Details */}
              <div className="flex items-center justify-between pt-1">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-white">
                    {formatNumber(tradingVolumeSetting.current, {
                      maximumFractionDigits: 2,
                    })}{' '}
                    / {tradingVolumeSetting.target}
                  </span>
                  <span className="text-xs font-medium text-white">USD</span>
                  {/* <ArrowLeftRight size={12} className="text-white" /> */}
                </div>
                <div className="text-xs font-medium text-white">
                  {formatNumber(tradingVolumeSetting.remaining, { maximumFractionDigits: 2 })} {t('usd-left')}
                </div>
              </div>
            </div>
          </div>

          {/* Instant Purchase */}
          <div className="space-y-3 rounded bg-white/10 p-3">
            <div className="text-sm font-medium text-white">{t('instant-purchase')}</div>

            <div className="grid grid-cols-3 gap-3">
              {creditOptions.map((option, index) => (
                <div key={option.id} className="relative flex flex-col items-center gap-2">
                  {index === 1 && (
                    <div className="absolute -right-1 -top-4 z-10 rounded-md bg-purple-600 px-1.5 py-0.5 text-xs font-bold text-white">
                      20%
                    </div>
                  )}
                  {index === 2 && (
                    <div className="absolute -right-1 -top-4 z-10 rounded-md bg-purple-600 px-1.5 py-0.5 text-xs font-bold text-white">
                      35%
                    </div>
                  )}
                  <button
                    onClick={() => handleCreditPurchase(option)}
                    disabled={loading === option.id}
                    className={cn(
                      'flex w-full items-center justify-center rounded-lg bg-yellow-400/15 px-3 py-2 text-xs font-bold text-yellow-300 transition-colors',
                      loading === option.id && 'opacity-50',
                      'hover:bg-yellow-400/25',
                    )}
                  >
                    {loading === option.id ? (
                      t('loading')
                    ) : (
                      <>
                        <Image
                          src="/chain-icons/icon-solana.png"
                          alt="SOL"
                          width={16}
                          height={16}
                          className="mr-1 inline-block rounded-full"
                        />
                        {option.solAmount}
                      </>
                    )}
                  </button>
                  <div className="text-center text-xs font-medium text-white">{option.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Support */}
          <div className="flex items-center justify-between rounded bg-white/10 p-3">
            <div className="mr-3 flex min-w-0 flex-1 flex-col gap-1">
              <div className="text-sm font-medium text-white">{t('contact-customer-support')}</div>
              <div className="text-xs font-medium text-zinc-400">{t('support-description')}</div>
            </div>
            <button
              onClick={handleContactSupport}
              className="flex flex-shrink-0 items-center gap-1 rounded-lg border border-white/10 bg-white/10 px-3 py-2 text-xs font-bold text-white transition-colors hover:bg-white/20"
            >
              <Image src="/social-icon/telegram.svg" alt="Telegram" width={14} height={14} />
              Telegram
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (!publicKey) {
    return null;
  }

  if (isDrawerContext) {
    return (
      <div
        // onPointerDown handler removed - DrawerContent will handle event stopping
        className="mb-6 h-full overflow-y-auto bg-zinc-950/90 p-5 backdrop-blur-3xl"
      >
        {dropdownContentJSX}
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: -10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: -10 }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
      style={{ transformOrigin: 'top center' }}
      className="mt-1 w-[380px] rounded-lg border border-white/10 bg-transparent p-5 shadow-xl shadow-black backdrop-blur-3xl"
    >
      {dropdownContentJSX}
    </motion.div>
  );
}
