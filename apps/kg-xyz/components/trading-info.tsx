'use client';

import { useTranslations } from 'next-intl';

import { useTradingWebSocket } from '@/contexts/trading-websocket-context';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';

export function TradingInfo({ className }: { className?: string }) {
  const t = useTranslations('trading-info');
  const { tradingData, isLoading } = useTradingWebSocket();
  const { volumeBuy, volumeSell, txSell, txBuy, tradeNumSell, tradeNumBuy } = tradingData;

  // Calculate percentages safely
  const calculatePercentage = (value: string | null, total: string | null) => {
    if (value === null || total === null) return '0%';
    const numValue = parseFloat(value);
    const numTotal = parseFloat(total) + numValue;
    if (numTotal === 0) return '0%';
    return `${(numValue / numTotal) * 100}%`;
  };

  if (isLoading) {
    return (
      <div className={cn('space-y-5 rounded-lg md:px-3', className)}>
        <div className="space-y-1">
          <div className="mb-1 text-xs">{t('transactions').replace(' (1hr)', '')}</div>
          <div className="flex items-center justify-between text-xs font-medium">
            <div className="h-3 w-20 animate-pulse rounded bg-zinc-700"></div>
            <div className="h-3 w-20 animate-pulse rounded bg-zinc-700"></div>
          </div>
          <div className="h-2 w-full animate-pulse rounded bg-zinc-700"></div>
        </div>

        <div className="space-y-1">
          <div className="mb-1 text-xs">{t('turnover').replace(' (1hr)', '')}</div>
          <div className="flex items-center justify-between text-xs font-medium">
            <div className="h-3 w-28 animate-pulse rounded bg-zinc-700"></div>
            <div className="h-3 w-28 animate-pulse rounded bg-zinc-700"></div>
          </div>
          <div className="h-2 w-full animate-pulse rounded bg-zinc-700"></div>
        </div>

        <div className="space-y-1">
          <div className="mb-1 text-xs">{t('traders').replace(' (1hr)', '')}</div>
          <div className="flex items-center justify-between text-xs font-medium">
            <div className="h-3 w-24 animate-pulse rounded bg-zinc-700"></div>
            <div className="h-3 w-24 animate-pulse rounded bg-zinc-700"></div>
          </div>
          <div className="h-2 w-full animate-pulse rounded bg-zinc-700"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-5 rounded-lg md:px-3', className)}>
      <div className="space-y-1">
        <div className="mb-1 text-xs">{t('transactions').replace(' (1hr)', '')}</div>
        <div className="flex items-center justify-between text-xs font-medium">
          <span className="text-[#00B38C]">
            {t('buy')} {formatNumber(parseFloat(txBuy || '0'), { notation: 'compact', maximumFractionDigits: 2 })}
          </span>
          <span className="text-pink-600">
            {t('sell')} {formatNumber(parseFloat(txSell || '0'), { notation: 'compact', maximumFractionDigits: 2 })}
          </span>
        </div>
        <div className="relative h-2 w-full overflow-hidden rounded">
          <div
            style={{ '--score-l-1': calculatePercentage(txBuy, txSell) } as React.CSSProperties}
            className="absolute left-0 top-0 h-full w-[var(--score-l-1)] bg-[#00B38C]"
          />
          <div
            style={{ '--score-r-1': calculatePercentage(txSell, txBuy) } as React.CSSProperties}
            className="absolute right-0 top-0 h-full w-[var(--score-r-1)] bg-pink-600"
          />
        </div>
      </div>
      <div className="space-y-1">
        <div className="mb-1 text-xs">{t('turnover').replace(' (1hr)', '')}</div>
        <div className="flex items-center justify-between text-xs font-medium">
          <span className="text-[#00B38C]">
            {t('buy')} $ {formatNumber(parseFloat(volumeBuy || '0'), { notation: 'compact', maximumFractionDigits: 2 })}{' '}
            USD
          </span>
          <span className="text-pink-600">
            {t('sell')} ${' '}
            {formatNumber(parseFloat(volumeSell || '0'), { notation: 'compact', maximumFractionDigits: 2 })} USD
          </span>
        </div>
        <div className="relative h-2 w-full overflow-hidden rounded">
          <div
            style={{ '--score-l': calculatePercentage(volumeBuy, volumeSell) } as React.CSSProperties}
            className="absolute left-0 top-0 h-full w-[var(--score-l)] bg-[#00B38C]"
          />
          <div
            style={{ '--score-r': calculatePercentage(volumeSell, volumeBuy) } as React.CSSProperties}
            className="absolute right-0 top-0 h-full w-[var(--score-r)] bg-pink-600"
          />
        </div>
      </div>
      <div className="space-y-1">
        <div className="mb-1 text-xs">{t('traders').replace(' (1hr)', '')}</div>
        <div className="flex items-center justify-between text-xs font-medium">
          <span className="text-[#00B38C]">
            {t('buyers')}{' '}
            {formatNumber(parseFloat(tradeNumBuy || '0'), { notation: 'compact', maximumFractionDigits: 2 })}
          </span>
          <span className="text-pink-600">
            {t('sellers')}{' '}
            {formatNumber(parseFloat(tradeNumSell || '0'), { notation: 'compact', maximumFractionDigits: 2 })}
          </span>
        </div>
        <div className="relative h-2 w-full overflow-hidden rounded">
          <div
            style={{ '--score-l-2': calculatePercentage(tradeNumBuy, tradeNumSell) } as React.CSSProperties}
            className="absolute left-0 top-0 h-full w-[var(--score-l-2)] bg-[#00B38C]"
          />
          <div
            style={{ '--score-r-2': calculatePercentage(tradeNumSell, tradeNumBuy) } as React.CSSProperties}
            className="absolute right-0 top-0 h-full w-[var(--score-r-2)] bg-pink-600"
          />
        </div>
      </div>
    </div>
  );
}
