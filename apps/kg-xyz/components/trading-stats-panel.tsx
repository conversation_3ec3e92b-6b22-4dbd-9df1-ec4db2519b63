'use client';

import { ChevronRight } from 'lucide-react';
import * as motion from 'motion/react-client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createQuery } from 'react-query-kit';

import { usePrices } from '@/contexts/price-context';
import { useWalletConnectModal } from '@/contexts/wallet-connect-modal-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useBuySignals, useSellSignals } from '@/hooks/use-signals';
import useIsMobile from '@/hooks/useIsMobile';
import { fetchJupiterOrders } from '@/lib/jupiter';
import { getAssetByAddress } from '@/lib/okx';
import { calculateTokenValue, cn, sum } from '@/lib/utils';
import { Button } from '@kryptogo/2b';
import { sendGTMEvent } from '@next/third-parties/google';

import { LimitOrderItem } from './limit-order-item';
import { FoldedTokenItem, TokenItem } from './token-item';

/* eslint-disable jsx-a11y/alt-text */

/* eslint-disable @next/next/no-img-element */

export function TradingStatsPanel({ isOpen, toggleOpenAction }: { isOpen: boolean; toggleOpenAction: () => void }) {
  const t = useTranslations('trading-stats-panel');
  const { address: publicKey } = useDebugWallet();
  const { openModal } = useWalletConnectModal();
  const isMobile = useIsMobile();
  const { registerToken, registerRealtimeToken, prices } = usePrices();
  const [activeTab, setActiveTab] = useState<string>('holdings');
  const [unrealizedPnl, setUnrealizedPnl] = useState<Map<string, number>>(new Map());
  const tokenListRef = useRef<HTMLDivElement>(null);
  // Create typed query for assets
  const useAssets = createQuery<Awaited<ReturnType<typeof getAssetByAddress>>, { publicKey: string }>({
    queryKey: ['assets'],
    fetcher: ({ publicKey }) => getAssetByAddress(publicKey || '', '501'),
  });

  const { data: assets } = useAssets({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 3000,
  });

  const { data: sellSignals } = useSellSignals();
  const { data: buySignals } = useBuySignals();

  // Create typed query for limit orders
  const useLimitOrders = createQuery<Awaited<ReturnType<typeof fetchJupiterOrders>>, { publicKey: string }>({
    queryKey: ['limit-orders'],
    fetcher: ({ publicKey }) => fetchJupiterOrders(publicKey || '', 'active'),
  });

  const { data: limitOrders } = useLimitOrders({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 10000,
  });

  // Register user assets with the PriceProvider as real-time tokens
  useEffect(() => {
    if (assets?.data?.[0]?.tokenAssets) {
      assets.data[0].tokenAssets.forEach((token) => {
        if (token.tokenContractAddress) {
          registerRealtimeToken(token.tokenContractAddress);
        }
      });
    }
  }, [assets, registerRealtimeToken]);

  // Register buy/sell signal tokens with the PriceProvider
  useEffect(() => {
    if (buySignals) {
      buySignals.forEach((signal) => {
        registerToken(signal.token_address);
      });
    }
  }, [buySignals, registerToken]);

  const tokens = useMemo(
    () =>
      (
        assets?.data?.[0]?.tokenAssets.filter(({ tokenContractAddress, isRiskToken, tokenPrice, balance }) => {
          if (!tokenContractAddress) return false;
          if (isRiskToken) return false;
          return calculateTokenValue({ tokenContractAddress, tokenPrice, balance }, prices) >= 1e-3;
        }) || []
      ).toSorted((a, b) => calculateTokenValue(b, prices) - calculateTokenValue(a, prices)),
    [assets, prices],
  );

  // 計算總價值和總未實現損益
  const { totalValue = 0, totalUnrealizedPNL } = useMemo(() => {
    const totalVal = assets?.data?.[0]?.tokenAssets.reduce((sum, token) => sum + calculateTokenValue(token, prices), 0);

    return { totalValue: totalVal, totalUnrealizedPNL: unrealizedPnl.values().reduce(sum, 0) };
  }, [assets, prices, unrealizedPnl]);

  if (isMobile && !isOpen) {
    return null;
  }

  if (!isOpen)
    return (
      <div className="mb-6 h-full overflow-y-auto">
        <div className="min-h-[calc(100vh-6rem)] w-20 border-l border-zinc-800 bg-zinc-900/50 p-4">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              visible: {
                x: 0,
                transition: {
                  duration: 0.2,
                  when: 'beforeChildren',
                  staggerChildren: 0.1,
                },
              },
              hidden: {
                x: '100%',
                transition: {
                  duration: 0.2,
                  when: 'afterChildren',
                },
              },
            }}
            className="flex flex-col items-center gap-4"
          >
            <button type="button" onClick={toggleOpenAction} className="mb-4 h-10 w-10">
              <img src="/left-arrow.svg" alt="" className="h-full w-full object-cover" />
            </button>
            {tokens.map((token, index) => (
              <FoldedTokenItem
                key={index}
                token={token}
                sellSignal={sellSignals?.find((x) => x.token_address === token.tokenContractAddress)}
              />
            ))}
            {!publicKey && (
              <Image
                onClick={() => openModal()}
                className="cursor-pointer"
                src="/wallet-icon.svg"
                alt=""
                width={36}
                height={36}
              />
            )}
          </motion.div>
        </div>
      </div>
    );

  return (
    <div className="fixed inset-0 bottom-0 top-16 z-40 h-screen w-full overflow-hidden bg-zinc-950 md:relative md:inset-auto md:top-0 md:z-40 md:h-auto md:min-h-[calc(100vh-6rem)] md:w-80 md:border-l md:border-zinc-800 md:bg-zinc-900/50">
      <div className="sticky top-0 flex w-full flex-col border-b border-t border-zinc-800 bg-zinc-900/50 md:w-[320px] md:border-t-0 md:bg-zinc-900/50">
        <div className="flex w-full items-center justify-between px-4 py-2">
          <div>
            <button
              onClick={() => setActiveTab('holdings')}
              className={cn('mr-4 text-sm text-zinc-400', activeTab === 'holdings' && 'font-bold text-white')}
            >
              {t('tab-holdings')}
              {(tokenListRef.current?.childElementCount ?? 0) > 0 && `(${tokenListRef.current?.childElementCount})`}
            </button>
            <button
              onClick={() => setActiveTab('limitOrders')}
              className={cn('mr-4 text-sm text-zinc-400', activeTab === 'limitOrders' && 'font-bold text-white')}
            >
              {t('tab-limit-orders')}
              {` (${limitOrders?.orders?.length || 0})`}
            </button>
          </div>
          <Button onClick={toggleOpenAction} variant="ghost" size="icon" className="ml-2 h-8 w-8">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="h-[calc(100vh-8rem)] overflow-y-auto pb-8 md:h-[calc(100vh-10rem)]">
        {activeTab === 'holdings' && (
          <>
            {/* Token stats */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                visible: {
                  x: 0,
                  transition: {
                    duration: 0.2,
                    when: 'beforeChildren',
                    staggerChildren: 0.1,
                  },
                },
                hidden: {
                  x: '100%',
                  transition: {
                    duration: 0.2,
                    when: 'afterChildren',
                  },
                },
              }}
              className="h-full overflow-y-auto"
            >
              {/* Total Value and Total Unrealized PNL Section - Always shown */}
              <div
                className="relative mb-4 overflow-hidden p-3"
                style={{
                  backgroundImage: 'url(/total-value-card-bg.avif)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'top',
                  backgroundRepeat: 'no-repeat',
                }}
              >
                <div className="relative z-10 flex flex-row items-center justify-between">
                  {/* Total Value */}
                  <div>
                    <div className="text-xs text-zinc-400">{t('total-value')}</div>
                    <div className="text-left text-lg font-bold text-white">
                      ${tokens.length > 0 ? totalValue.toFixed(2) : '0.00'}
                    </div>
                  </div>

                  {/* Total Unrealized PNL */}
                  <div>
                    <div className="text-xs text-zinc-400">{t('total-unrealized-pnl')}</div>
                    {tokens.length > 0 ? (
                      <div
                        className={`text-right text-lg font-bold ${totalUnrealizedPNL >= 0 ? 'text-[#00B38C]' : 'text-pink-500'}`}
                      >
                        {totalUnrealizedPNL >= 0 ? '+' : '-'}${Math.abs(totalUnrealizedPNL).toFixed(2)}
                      </div>
                    ) : (
                      <div className="text-right text-lg font-bold text-[#00B38C]">+$0.00</div>
                    )}
                  </div>
                </div>
              </div>
              <div ref={tokenListRef}>
                {tokens.map((token) => (
                  <TokenItem
                    key={token.tokenContractAddress}
                    token={token}
                    sellSignal={sellSignals?.find((x) => x.token_address === token.tokenContractAddress)}
                    smallSellSignal={buySignals?.find(
                      (x) => x.token_address === token.tokenContractAddress && x.win_rate >= 0.25 && x.win_rate < 0.5,
                    )}
                    updateUnrealizedPnl={([tokenAddress, unrealizedPnl]) =>
                      setUnrealizedPnl((prev) => prev.set(tokenAddress, unrealizedPnl))
                    }
                  />
                ))}
              </div>
              {tokens.length === 0 && publicKey && (
                <div className="flex h-[calc(100vh-20rem)] flex-col items-center justify-center gap-2 text-[#666666]">
                  <Image src="/empty-wallet.svg" alt="" className="mt-0" width={200} height={200} />
                  <p>{t('no-tokens-found', { defaultMessage: 'No tokens found in your wallet' })}</p>
                </div>
              )}
            </motion.div>
          </>
        )}

        {activeTab === 'limitOrders' && (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              visible: {
                x: 0,
                transition: {
                  duration: 0.2,
                  when: 'beforeChildren',
                  staggerChildren: 0.1,
                },
              },
              hidden: {
                x: '100%',
                transition: {
                  duration: 0.2,
                  when: 'afterChildren',
                },
              },
            }}
            className="h-full overflow-y-auto"
          >
            {limitOrders?.orders && limitOrders.orders.length > 0 ? (
              <div className="px-0">
                {limitOrders.orders.map((order, index) => (
                  <LimitOrderItem key={index} order={order} />
                ))}
              </div>
            ) : (
              <div className="flex h-full flex-col items-center justify-center gap-2 text-zinc-400">
                <p>{t('no-limit-orders-found', { defaultMessage: 'No active limit orders found' })}</p>
              </div>
            )}
          </motion.div>
        )}

        {!publicKey && (
          <div className="flex h-full flex-col items-center justify-center gap-2 text-zinc-400">
            <Image src="/empty-wallet.svg" alt="" className="mt-4" width={300} height={300} />
            {t('wallet-not-connected')}
            <div className="flex items-center justify-center">
              <button
                className="mt-4 rounded-md bg-[#ffc30f] px-4 py-1.5 font-semibold text-black"
                onClick={() => {
                  sendGTMEvent({
                    event: 'app_xyz_connect_wallet_click',
                    platform: 'web',
                  });

                  openModal();
                }}
                type="button"
              >
                {t('connect-wallet')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
