import React from 'react';

// Signal strength enum representing different categories of signals
export enum SignalStrength {
  BIG_BUY = 'bigBuy',
  SMALL_BUY = 'smallBuy',
  SMALL_SELL = 'smallSell',
  BIG_SELL = 'bigSell',
}

// Reusable component for displaying signal ranks with appropriate styling
interface SignalRankBadgeProps {
  type: 'smartMoneyHolding' | 'priceSignal' | 'winRate';
  signalStrength: SignalStrength;
  signalConfig: any; // Using any for now, but ideally this should be properly typed
}

export const SignalRankBadge = ({ type, signalStrength, signalConfig }: SignalRankBadgeProps) => {
  const config = signalConfig[signalStrength];
  const typeConfig = config[type];
  return <span className={`rounded-full ${config.styleClasses} px-3 py-1 text-xs font-medium`}>{typeConfig.text}</span>;
};
