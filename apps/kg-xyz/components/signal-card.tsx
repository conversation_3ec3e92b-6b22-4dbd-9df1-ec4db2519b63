'use client';

import { <PERSON><PERSON>, <PERSON>, X, SquareArrowOutUpRight } from 'lucide-react';
import MotionNumber from 'motion-number';
import * as motion from 'motion/react-client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';

import { TradingSection } from '@/app/token/[network]/[address]/components/trading-section';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useDefaultBuyAmount } from '@/contexts/default-buy-amount-context';
import { usePrices } from '@/contexts/price-context';
import { useTokenInfoQuery, useTokenOverviewQuery } from '@/hooks/api-queries';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import type { SignalResponse } from '@/types/kg-api';
import { formatTimeAgo } from '@/utils/time';

import { BuyButton } from './buy-button';
import { ResponsiveTooltipDrawer } from './responsive-tooltip-drawer';
import { SignalStrength } from './signal-rank-badge';
import { Dialog, DialogContent, DialogHeader, DialogClose } from './ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerClose } from './ui/drawer';

/* eslint-disable @next/next/no-img-element */

interface SignalCardProps extends Partial<SignalResponse> {
  token_address?: string;
  smart_wallet_count?: number;
  buy_entry_price?: number;
  highest_price?: number;
  emit_time?: number;
  telegram_link?: string;
  win_rate?: number;
  average_win_rate?: number;
  average_holding?: number;
  isDimmed?: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

// Using SignalStrength enum from signal-rank-badge.tsx

// Function to determine signal rank based on a value between 0 and 1
export const getSignalRank = (value: number): SignalStrength => {
  if (value >= 0.75) return SignalStrength.BIG_BUY;
  if (value >= 0.5) return SignalStrength.SMALL_BUY;
  if (value >= 0.25) return SignalStrength.SMALL_SELL;
  return SignalStrength.BIG_SELL;
};

// Using SignalRankBadge component from signal-rank-badge.tsx

export function SignalCard({
  buy_entry_price = 0,
  highest_price = 0,
  win_rate = 0,
  average_holding = 0,
  average_win_rate = 0,
  token_address: address = '',
  emit_time,
  telegram_link,
  isDimmed,
  onMouseEnter,
  onMouseLeave,
}: SignalCardProps) {
  // Extract values from props, supporting both old and new interface
  const t = useTranslations('signal-card');

  const [isTradeModalOpen, setIsTradeModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { prices } = usePrices();
  const price = prices?.[`sol:${address}`] ?? 0.0;
  const { defaultBuyAmount } = useDefaultBuyAmount();

  // Check if on mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Listen for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const [elapsedSeconds, setElapsedSeconds] = useState<number | null>(null);

  // Live count-up timer effect for signal duration < 10 min
  useEffect(() => {
    if (!emit_time) return;
    const emitTimeMs = emit_time * 1000;
    const updateElapsed = () => {
      const now = Date.now();
      const seconds = Math.floor((now - emitTimeMs) / 1000);
      setElapsedSeconds(seconds);
    };
    updateElapsed();
    // Only update if under 10 min (600s)
    if (Date.now() - emitTimeMs < 600000) {
      const interval = setInterval(() => {
        updateElapsed();
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [emit_time]);

  // Helper to format seconds as mm:ss or just seconds
  const formatMMSS = (totalSeconds: number) => {
    const m = Math.floor(totalSeconds / 60);
    const s = totalSeconds % 60;
    if (m === 0) {
      return `${s}s`;
    }
    return `${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`;
  };

  // Use the centralized token info and token overview queries

  const { data: token } = useTokenInfoQuery({
    variables: { address },
  });

  const { data: tokenOverview } = useTokenOverviewQuery({
    variables: { address },
  });

  const createTime = tokenOverview?.memeInfo?.createTime;
  const timeDiff = createTime ? Date.now() - new Date(+createTime).getTime() : null;
  const timeAgo = timeDiff ? formatTimeAgo(timeDiff) : null;
  const signalDuration = emit_time ? formatTimeAgo(Date.now() - new Date(+emit_time * 1000).getTime()) : null;

  return (
    <motion.div
      layout
      className={`relative flex flex-col gap-4 rounded-xl border border-neutral-800 bg-zinc-900/50 px-4 py-3 pt-0 md:px-5 md:py-3 md:transition-opacity md:duration-200 md:hover:border-neutral-700 ${isDimmed ? 'opacity-60' : 'opacity-100'}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <Link
        href={`/token/501/${address}?openModal=true`}
        className="absolute right-12 top-2 flex items-center rounded-md border border-zinc-500/50 p-1.5"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{
            scale: 1.5,
            rotate: 15,
          }}
          transition={{
            duration: 0.4,
            scale: { type: 'spring', visualDuration: 0.6, bounce: 0.7 },
          }}
          className="cursor-pointer"
        >
          <Image src="/ic-solid-star-01.svg" alt="Star" width={16} height={16} className="text-[#FFC211]" />
        </motion.div>
      </Link>
      {/* Redirect to Telegram chat bubble button in top right corner */}
      <button
        onClick={() => window.open(telegram_link, '_blank')}
        className="absolute right-2 top-2 rounded-lg border border-zinc-500/50 p-1.5 transition-colors hover:bg-zinc-700/50 hover:text-zinc-300 md:p-2"
        aria-label={t('social.share')}
      >
        <SquareArrowOutUpRight className="h-3 w-3 md:h-3 md:w-3" />
      </button>
      <Drawer>
        {/* Main content section */}
        <div className="mt-2 flex gap-4 border-b border-zinc-700/50 py-4 md:mt-2">
          {/* Left column - Max Gain */}
          <div className="flex flex-1 flex-col">
            <div className="flex items-center gap-3">
              <Link
                href={`/token/501/${address}`}
                className="relative h-[2.2rem] w-[2.2rem] overflow-hidden rounded-full bg-zinc-800 md:h-[2.75rem] md:w-[2.75rem]"
              >
                <div className="relative h-full w-full duration-500">
                  {!token?.tokenLogoUrl && <div className="absolute inset-0 animate-pulse rounded-full bg-zinc-700" />}
                  <motion.img
                    src={token?.tokenLogoUrl}
                    alt={token?.tokenSymbol}
                    className={cn(
                      'h-full w-full object-cover transition-opacity',
                      token?.tokenLogoUrl ? 'opacity-100' : 'opacity-0',
                    )}
                    onLoad={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.classList.remove('opacity-0');
                      target.classList.add('opacity-100');
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.classList.add('opacity-0');
                    }}
                    whileHover={{
                      rotate: 360,
                      transition: { repeat: Infinity, duration: 1, ease: 'linear' },
                    }}
                    initial={{ rotate: 0 }}
                    animate={{ rotate: 0 }}
                  />
                </div>
              </Link>
              <div className="flex flex-col">
                <div className="flex items-center gap-1">
                  <Link href={`/token/501/${address}`}>
                    <span className="text-base font-bold text-white md:text-lg">{token?.tokenSymbol || '-'}</span>
                  </Link>
                  <span className="mx-1.5 rounded-md bg-zinc-900 px-1.5 py-0.5 text-[0.6rem] text-zinc-500 md:px-2 md:text-[0.7rem]">
                    {timeAgo}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-[0.65rem] text-zinc-500 md:text-xs">
                    {address ? `${address.slice(0, 7)}...${address.slice(-5)}` : '-'}
                  </span>
                  <button
                    onClick={() => navigator.clipboard.writeText(address)}
                    className="text-zinc-500 md:hover:text-zinc-400"
                  >
                    <Copy size={12} />
                  </button>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <ResponsiveTooltipDrawer
                title={t('max-gain-after-alert')}
                description={t('max-gain-after-alert-desc')}
                className="-mb-4 select-none text-left"
              />
              <span
                className="block max-w-full truncate whitespace-nowrap text-[clamp(1.7rem,8vw,2.2rem)] font-semibold text-emerald-400"
                style={{ minWidth: 0 }}
              >
                +
                <MotionNumber
                  value={
                    +highest_price > 0 && +buy_entry_price > 0
                      ? Math.max(
                          ((+highest_price - +buy_entry_price) / +buy_entry_price) * 100,
                          price > 0 ? ((+price - +buy_entry_price) / +buy_entry_price) * 100 : 0,
                        )
                      : 0
                  }
                  format={{
                    style: 'decimal',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                    useGrouping: true,
                  }}
                />
                %
              </span>
            </div>
            <div className="mt-4 content-start items-center md:flex">
              <span className="text-[0.7rem] font-light text-zinc-400 md:text-[0.75rem]">
                {t('price-since-signal')}
              </span>
              <span
                className={cn(
                  'ml-2 text-sm font-semibold',
                  +price - +buy_entry_price > 0 ? 'text-[#00B38C]' : 'text-pink-600',
                )}
              >
                {price === 0 ? (
                  <span className="text-xs text-zinc-600">{t('loading')}</span>
                ) : (
                  <span className="flex items-center">
                    <span className="max-w-full whitespace-nowrap text-sm md:text-sm [&:not(:has(+_br))]:text-sm">
                      {+(price || 0) - +buy_entry_price > 0 ? '+' : ''}
                      {(((+(price || 0) - +buy_entry_price) / +buy_entry_price) * 100).toFixed(2)}%
                    </span>
                  </span>
                )}
              </span>
            </div>
          </div>

          <div className="-my-4 w-0 border-r border-zinc-700/50" />
          {/* Right column - Smart Money stats */}
          <div className="flex flex-1 flex-col gap-2">
            <div className="flex flex-col">
              <span className="text-[0.7rem] font-light text-zinc-400 md:text-[0.75rem]">{t('mcap')}</span>
              <span className="flex items-center gap-1 text-base font-normal text-white md:text-lg">
                $
                {formatNumber(parseFloat(token?.marketCap || '**********'), {
                  notation: 'compact',
                  maximumFractionDigits: 2,
                })}
                {parseFloat(token?.marketCap || '**********') < 30000 && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="flex cursor-pointer items-center">
                          <Image src="/ic-solid-alert-triangle.svg" alt="Low Market Cap" width={16} height={16} />
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs bg-zinc-700 p-4 text-sm text-white">
                        <p>{t('low-mcap-warning')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </span>
            </div>
            <div>
              <ResponsiveTooltipDrawer
                title={t('smart-money-holdings')}
                description={t('smart-money-holdings-desc')}
                className="mb-0.5 text-left"
              />
              <div className={cn('text-base font-normal md:text-lg', average_holding > 0.7 && 'text-yellow-400')}>
                {formatNumber(average_holding * 100, { maximumFractionDigits: 0 })}%
              </div>
            </div>
            <div>
              <ResponsiveTooltipDrawer
                title={t('smart-money-win-rate')}
                description={t('smart-money-win-rate-desc')}
                className="mb-0.5 text-left"
              />
              <div className={cn('text-base font-normal md:text-lg', average_win_rate > 0.7 && 'text-yellow-400')}>
                {formatNumber(average_win_rate * 100, { maximumFractionDigits: 0 })}%
              </div>
            </div>
            <div>
              <ResponsiveTooltipDrawer
                title={t('signal-duration')}
                description={t('signal-duration-desc')}
                className="mb-0.5 text-left"
              />
              <div className="flex items-center text-base font-normal text-white md:text-lg">
                {elapsedSeconds !== null && elapsedSeconds < 600 ? (
                  <>
                    <span className="mr-1 text-amber-500">🔥</span>
                    {formatMMSS(elapsedSeconds)}
                  </>
                ) : (
                  <>
                    {(signalDuration?.includes('s') ||
                      (signalDuration?.includes('m') && +signalDuration.slice(0, -1) <= 10)) && (
                      <span className="mr-1 text-amber-500">🔥</span>
                    )}
                    {signalDuration}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-3">
            {tokenOverview?.socialMedia?.twitter && (
              <button
                title={t('social.twitter')}
                aria-label={t('social.twitter')}
                onClick={() => window.open(tokenOverview?.socialMedia?.twitter, '_blank')}
                className="text-zinc-400 md:hover:text-zinc-300"
              >
                <img
                  tw="mt-20 -mb-[56px]"
                  src={`${process.env.NEXT_PUBLIC_WEB_BASEURL}/social-icon/x-twitter.svg`}
                  alt=""
                />
              </button>
            )}
            {tokenOverview?.socialMedia?.telegram && (
              <button
                title={t('social.telegram')}
                aria-label={t('social.telegram')}
                onClick={() => window.open(tokenOverview?.socialMedia?.telegram, '_blank')}
                className="text-zinc-400 md:hover:text-zinc-300"
              >
                <img
                  tw="mt-20 -mb-[56px]"
                  src={`${process.env.NEXT_PUBLIC_WEB_BASEURL}/social-icon/telegram.svg`}
                  alt=""
                />
              </button>
            )}
            {tokenOverview?.socialMedia?.officialWebsite && (
              <button
                title={t('social.website')}
                aria-label={t('social.website')}
                onClick={() => window.open(tokenOverview?.socialMedia?.officialWebsite, '_blank')}
                className="text-zinc-400 md:hover:text-zinc-300"
              >
                <img
                  tw="mt-20 -mb-[56px]"
                  src={`${process.env.NEXT_PUBLIC_WEB_BASEURL}/social-icon/website.svg`}
                  alt=""
                />
              </button>
            )}
            {tokenOverview?.basicInfo.tokenContractAddress && tokenOverview?.basicInfo.isMeme == '1' && (
              <button
                title="Pump Fun"
                aria-label="Pump Fun"
                onClick={() =>
                  window.open(`https://pump.fun/coin/${tokenOverview?.basicInfo.tokenContractAddress}`, '_blank')
                }
                className="text-zinc-400 md:hover:text-zinc-300"
              >
                <img
                  tw="mt-20 -mb-[56px]"
                  src={`${process.env.NEXT_PUBLIC_WEB_BASEURL}/social-icon/pump-fun.svg`}
                  alt=""
                />
              </button>
            )}
          </div>
          <div className="flex items-center gap-2 md:gap-3">
            <button
              type="button"
              onClick={() => setIsTradeModalOpen(true)}
              className="flex items-center justify-center rounded-md border border-zinc-700 p-2 text-white md:rounded-lg md:px-3 md:py-3 md:hover:bg-zinc-800"
            >
              <Pen className="h-3 w-3 md:h-4 md:w-4" />
            </button>
            <div className="flex items-center justify-end">
              <BuyButton tokenAddress={address} amount={defaultBuyAmount} sellMode={win_rate < 0.25} flashMode />
            </div>
          </div>
        </div>

        {/* Trading Modal - Drawer for mobile, Dialog for desktop */}
        {isMobile ? (
          <Drawer open={isTradeModalOpen} onOpenChange={setIsTradeModalOpen}>
            <DrawerContent className="border-t border-zinc-800/50 bg-[#0C0C0C] px-4">
              <DrawerHeader className="flex flex-row items-center justify-between pt-2">
                <div className="text-xl font-bold text-white">
                  {token?.tokenSymbol} {t('trading')}
                </div>
                <DrawerClose asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <X className="h-4 w-4" />
                  </Button>
                </DrawerClose>
              </DrawerHeader>
              <div className="px-1 pb-8">
                <TradingSection
                  tokenAddress={address}
                  tokenSymbol={token?.tokenSymbol || ''}
                  afterTrade={() => setIsTradeModalOpen(false)}
                />
              </div>
            </DrawerContent>
          </Drawer>
        ) : (
          <Dialog open={isTradeModalOpen} onOpenChange={setIsTradeModalOpen}>
            <DialogContent className="border border-zinc-800/50 bg-[#0C0C0C] p-0 md:max-w-md">
              <DialogHeader className="flex flex-row items-center justify-between border-b border-zinc-800/50 p-4">
                <div className="text-xl font-bold text-white">
                  {token?.tokenSymbol} {t('trading')}
                </div>
                <DialogClose asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <X className="h-4 w-4" />
                  </Button>
                </DialogClose>
              </DialogHeader>
              <div className="max-h-[80vh] overflow-y-auto px-4">
                <TradingSection
                  tokenAddress={address}
                  tokenSymbol={token?.tokenSymbol || ''}
                  afterTrade={() => setIsTradeModalOpen(false)}
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </Drawer>
    </motion.div>
  );
}
