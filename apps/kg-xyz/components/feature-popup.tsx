'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useEffect, useState, useRef } from 'react';

import { Drawer, DrawerContent, DrawerHeader, DrawerFooter } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/useIsMobile';
import { cn } from '@/lib/utils';

interface FeaturePopupProps {
  isOpen?: boolean;
  onClose?: () => void;
}

// 定義每個頁面的內容
const getPages = (t: any) => [
  {
    title: t('page1-title', { defaultValue: 'Real-Time Alpha Signals' }),
    description: t('page1-description', {
      defaultValue:
        'We sift through the noise to deliver high-potential meme coins, so you can catch the wave with confidence.',
    }),
    image: '/signal.avif',
  },
  {
    title: t('page2-title', { defaultValue: 'Suspect Insiders Analysis' }),
    description: t('page2-description', {
      defaultValue: 'Trade like a top trader with just a 1-click analysis of Suspect Insiders activities.',
    }),
    image: '/suspect-insiders-analysis.avif',
  },
  {
    title: t('page3-title', { defaultValue: 'Crystal-Clear Trade History' }),
    description: t('page3-description', {
      defaultValue: 'Track every swap and limit order with diamond-hand clarity for data-driven decisions.',
    }),
    image: '/limit-orders.png',
  },
  {
    title: t('page4-title', { defaultValue: 'Ultimate App Trading Experience' }),
    description: t('page4-description', {
      defaultValue:
        'Download KryptoGO for a silky-smooth trading ecosystem—every move, signal, and swap crafted for degens who demand the best.',
    }),
    image: '/trade-in-kryptogo.avif',
  },
  {
    title: t('page5-title', { defaultValue: 'Instant Signal Alerts' }),
    description: t('page5-description', {
      defaultValue: 'Get Telegram pings to stay ahead of the meme coin game—never miss a hot signal.',
    }),
    image: '/signal-tg.png',
  },
  {
    title: t('page6-title', { defaultValue: 'Multi-Wallet Support' }),
    description: t('page6-description', {
      defaultValue: 'Plug in your favorite wallets and stack sats with a smooth, secure trading vibe.',
    }),
    image: '/multi-wallet.avif',
  },
];

export function FeaturePopup({ isOpen, onClose }: FeaturePopupProps) {
  const t = useTranslations('feature-popup');
  const [currentPage, setCurrentPage] = useState(0);
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right
  const isMobile = useIsMobile();
  const autoplayRef = useRef<NodeJS.Timeout | null>(null);
  const autoplayDelay = 20000; // 20 seconds between slides
  const [imgError, setImgError] = useState(false);
  // Add a timestamp to force GIF reload when revisiting a slide
  const [gifTimestamp, setGifTimestamp] = useState<Record<number, number>>({});

  // Get localized pages content
  const pages = getPages(t);

  // Animation variants for swiper-like effect
  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? '100%' : '-100%',
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? '100%' : '-100%',
      opacity: 0,
    }),
  };

  // Reset image error and update GIF timestamp when page changes
  useEffect(() => {
    setImgError(false);
    // Update timestamp for the current page to force GIF reload
    setGifTimestamp((prev) => ({
      ...prev,
      [currentPage]: Date.now(),
    }));
  }, [currentPage]);

  // Helper function to get image source with timestamp for GIFs
  const getImageSrc = (index: number) => {
    if (imgError) return '/feature-popup-image.png';

    const imagePath = pages[index].image;
    // If it's a GIF, append a timestamp to force reload
    if (imagePath.toLowerCase().endsWith('.gif')) {
      const timestamp = gifTimestamp[index] || Date.now();
      return `${imagePath}?t=${timestamp}`;
    }

    return imagePath;
  };

  // 處理下一頁按鈕點擊
  const handleNextPage = () => {
    setDirection(1);
    if (currentPage < pages.length - 1) {
      setCurrentPage(currentPage + 1);
    } else {
      // 如果是最後一頁，則關閉彈窗
      handleClose();
    }
    resetAutoplay();
  };

  // 處理上一頁按鈕點擊
  const handlePrevPage = () => {
    setDirection(-1);
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
    resetAutoplay();
  };

  const handleClose = () => {
    // 重置頁面索引
    setCurrentPage(0);
    if (onClose) {
      onClose();
    }
    clearAutoplay();
  };

  // Handle "Don't show again" button click - saves preference to localStorage
  const handleDontShowAgain = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('featurePopupDismissed', 'true');
    }
    handleClose();
  };

  // Start autoplay with animation completion
  const startAutoplay = () => {
    clearAutoplay();

    // First, reset any existing animations
    const resetAnimations = () => {
      const dots = document.querySelectorAll('.pagination-dot-path');
      dots.forEach((dot) => {
        const element = dot as HTMLElement;
        element.style.animation = 'none';
        // Trigger reflow to reset animation
        void element.offsetWidth;
        element.style.animation = '';
      });
    };

    resetAnimations();

    // Create a function to handle the slide transition
    const transitionToNextSlide = () => {
      // First complete the current animation
      const activeDot = document.querySelector('.pagination-dot-active .pagination-dot-path') as HTMLElement;
      if (activeDot) {
        // Ensure animation completes
        activeDot.style.animationPlayState = 'running';
        activeDot.style.animationDuration = '10ms';
        activeDot.style.strokeDashoffset = '0';
      }

      // Then after a small delay, change the slide
      setTimeout(() => {
        setDirection(1);
        setCurrentPage((prev) => {
          if (prev < pages.length - 1) {
            return prev + 1;
          } else {
            return 0; // Loop back to the first slide
          }
        });

        // Reset animations for the next cycle
        setTimeout(resetAnimations, 50);
      }, 200);
    };

    // Set the interval to transition slides
    autoplayRef.current = setInterval(transitionToNextSlide, autoplayDelay);
  };

  // Clear autoplay
  const clearAutoplay = () => {
    if (autoplayRef.current) {
      clearInterval(autoplayRef.current);
      autoplayRef.current = null;
    }
  };

  // Reset autoplay timer
  const resetAutoplay = () => {
    clearAutoplay();
    startAutoplay();
  };

  // Reset current page and start autoplay when popup is opened
  useEffect(() => {
    if (isOpen) {
      setCurrentPage(0);
      setDirection(0);
      startAutoplay();
    } else {
      clearAutoplay();
    }

    return () => clearAutoplay();
  }, [isOpen]);

  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'ArrowRight') {
        handleNextPage();
      } else if (e.key === 'ArrowLeft') {
        handlePrevPage();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentPage]);

  if (!isOpen) return null;

  // This section was previously the FeatureContent component
  // It has been integrated directly into the main component

  // Mobile view - use drawer
  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={(open) => !open && handleClose()}>
        <DrawerContent className="border-t border-zinc-800 bg-zinc-900/50 backdrop-blur-md">
          <div className="mx-auto w-full max-w-md pb-4">
            <DrawerHeader>
              <div className="flex w-full flex-col items-center">
                {/* Image with fixed dimensions */}
                <div className="relative mb-6 h-[250px] w-full overflow-hidden rounded-lg">
                  <div className="relative h-full w-full">
                    <AnimatePresence custom={direction} initial={false} mode="popLayout">
                      <motion.div
                        key={currentPage}
                        custom={direction}
                        variants={variants}
                        initial="enter"
                        animate="center"
                        exit="exit"
                        transition={{
                          x: { type: 'spring', stiffness: 300, damping: 30 },
                          opacity: { duration: 0.3 },
                        }}
                        className="absolute inset-0 h-full w-full"
                        style={{ position: 'absolute' }}
                      >
                        <div className="relative flex h-full w-full items-center justify-center">
                          <Image
                            src={getImageSrc(currentPage)}
                            alt={pages[currentPage].title}
                            className="rounded-md object-contain"
                            fill
                            sizes="100vw"
                            onError={() => setImgError(true)}
                            priority
                            unoptimized
                          />
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  </div>
                </div>

                {/* Title */}
                <h2 className="mb-2 text-center text-xl font-bold text-white">{pages[currentPage].title}</h2>

                {/* Description */}
                <p className="max-w-2xl text-center text-sm text-zinc-400 md:text-base">
                  {pages[currentPage].description}
                </p>
              </div>
            </DrawerHeader>
            <DrawerFooter>
              {/* Pagination dots */}
              <div className="mb-4 flex items-center justify-center gap-2">
                {pages.map((_, i) => (
                  <button
                    key={i}
                    onClick={() => {
                      setDirection(i > currentPage ? 1 : -1);
                      setCurrentPage(i);
                      resetAutoplay();
                    }}
                    className={cn(
                      'pagination-dot focus:outline-none',
                      i === currentPage ? 'pagination-dot-active' : '',
                    )}
                    aria-label={`Go to slide ${i + 1}`}
                    style={
                      i === currentPage ? ({ '--autoplay-duration': `${autoplayDelay}ms` } as React.CSSProperties) : {}
                    }
                  >
                    <svg width="16" height="16" viewBox="0 0 16 16">
                      <circle
                        className="pagination-dot-path"
                        cx="8"
                        cy="8"
                        r="6"
                        fill="none"
                        stroke="white"
                        strokeWidth="1.5"
                      />
                      <circle
                        className="pagination-dot-inner"
                        cx="8"
                        cy="8"
                        r="3"
                        fill={i === currentPage ? 'white' : '#52525b'}
                      />
                    </svg>
                  </button>
                ))}
              </div>

              <div className="flex w-full gap-4">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage === 0}
                  className="w-full rounded-lg border border-zinc-700 px-8 py-3 font-medium text-white transition-colors hover:bg-zinc-800 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {t('back-button')}
                </button>
                <button
                  onClick={handleNextPage}
                  className="w-full rounded-lg bg-[#FFC211] px-8 py-3 font-medium text-black transition-colors hover:bg-[#FFC211]/80"
                >
                  {currentPage === pages.length - 1 ? t('done-button') : t('next-button')}
                </button>
              </div>

              {/* Don't show again text */}
              <button
                onClick={handleDontShowAgain}
                className="mt-4 text-center text-xs text-zinc-500 transition-colors hover:text-zinc-300"
              >
                {t('dont-show-again')}
              </button>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop view - use popup
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="h-vh relative flex w-full max-w-xl flex-col overflow-hidden rounded-xl border border-zinc-800/50 bg-zinc-900/50 shadow-xl backdrop-blur-md">
        {/* Content - fixed height with no scrolling */}
        <div className="flex h-full flex-col items-center justify-between p-6 pt-10">
          {/* Feature content with fixed dimensions */}
          <div className="flex w-full flex-col items-center">
            {/* Image with fixed dimensions */}
            <div className="relative mb-6 h-[320px] w-full overflow-hidden rounded-lg">
              <div className="relative h-full w-full">
                <AnimatePresence custom={direction} initial={false} mode="popLayout">
                  <motion.div
                    key={currentPage}
                    custom={direction}
                    variants={variants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                      x: { type: 'spring', stiffness: 300, damping: 30 },
                      opacity: { duration: 0.3 },
                    }}
                    className="absolute inset-0 h-full w-full"
                    style={{ position: 'absolute' }}
                  >
                    <div className="relative flex h-full w-full items-center justify-center">
                      <Image
                        src={getImageSrc(currentPage)}
                        alt={pages[currentPage].title}
                        className="rounded-md object-cover"
                        fill
                        sizes="100vw"
                        onError={() => setImgError(true)}
                        priority
                        unoptimized
                      />
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/* Title */}
            <h2 className="mb-2 text-center text-xl font-bold text-white">{pages[currentPage].title}</h2>

            {/* Description */}
            <p className="max-w-2xl text-center text-sm text-zinc-400">{pages[currentPage].description}</p>
          </div>

          {/* Empty space to push navigation to bottom */}
          <div className="flex-grow"></div>
        </div>

        {/* Navigation buttons - fixed at bottom */}
        <div className="flex flex-col border-t border-zinc-800/50 bg-zinc-950/10 px-6 pb-4 pt-4 backdrop-blur-sm">
          {/* Pagination dots */}
          <div className="mb-4 flex items-center justify-center gap-2">
            {pages.map((_, i) => (
              <button
                key={i}
                onClick={() => {
                  setDirection(i > currentPage ? 1 : -1);
                  setCurrentPage(i);
                  resetAutoplay();
                }}
                className={cn('pagination-dot focus:outline-none', i === currentPage ? 'pagination-dot-active' : '')}
                aria-label={`Go to slide ${i + 1}`}
                style={
                  i === currentPage ? ({ '--autoplay-duration': `${autoplayDelay}ms` } as React.CSSProperties) : {}
                }
              >
                <svg width="16" height="16" viewBox="0 0 16 16">
                  <circle
                    className="pagination-dot-path"
                    cx="8"
                    cy="8"
                    r="6"
                    fill="none"
                    stroke="white"
                    strokeWidth="1.5"
                  />
                  <circle
                    className="pagination-dot-inner"
                    cx="8"
                    cy="8"
                    r="3"
                    fill={i === currentPage ? 'white' : '#52525b'}
                  />
                </svg>
              </button>
            ))}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-center gap-4">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 0}
              className="rounded-lg border border-zinc-700 px-6 py-2 font-medium text-white transition-colors hover:bg-zinc-800 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {t('back-button', { defaultValue: 'Back' })}
            </button>
            <button
              onClick={handleNextPage}
              className="rounded-lg bg-[#FFC211] px-6 py-2 font-medium text-black transition-colors hover:bg-[#FFC211]/80"
            >
              {currentPage === pages.length - 1
                ? t('done-button', { defaultValue: 'Done' })
                : t('next-button', { defaultValue: 'Next' })}
            </button>
          </div>

          {/* Don't show again text */}
          <button
            onClick={handleDontShowAgain}
            className="mt-3 text-center text-[0.75rem] text-zinc-500 transition-colors hover:text-zinc-300"
          >
            {t('dont-show-again')}
          </button>
        </div>
      </div>
    </div>
  );
}
