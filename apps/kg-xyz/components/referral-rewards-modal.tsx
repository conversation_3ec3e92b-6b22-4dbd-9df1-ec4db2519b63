'use client';

import { <PERSON>UpRight, X } from 'lucide-react';

import type { TradeButtonsProps } from '@/app/token/[network]/[address]/components/trade-buttons';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useReferralRewards } from '@/hooks/use-referral-rewards';
import { Button } from '@kryptogo/2b';
import { useKGUser } from '@kryptogo/kryptogokit-sdk-react';
import { getAppStoreUrl } from '@kryptogo/utils';

interface ReferralRewardsModalProps extends TradeButtonsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ReferralRewardsModal({ isOpen, onClose }: ReferralRewardsModalProps) {
  const { token } = useKGUser();
  const env = typeof window !== 'undefined' ? new URLSearchParams(window.location.search).get('env') || 'prod' : 'prod';

  const { data: rewards } = useReferralRewards({ token: token!, isOpen });

  const handleDeepLink = () => {
    let prefix = 'kryptogo://referral';
    if (env === 'dev') {
      prefix = 'kryptogodev://referral';
    } else if (env === 'staging') {
      prefix = 'kryptogostaging://referral';
    }

    // Try window.open first
    const newWindow = window.open(prefix, '_blank');

    // If window.open was blocked or returned null, use location.href after a short delay
    // This gives time for the OS to handle any existing deep link handlers
    if (!newWindow) {
      setTimeout(() => {
        window.location.href = prefix;
      }, 500);
    }
  };

  const available_rewards = (rewards?.available_rewards ?? '') == '' ? '0' : rewards?.available_rewards;
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="border-none bg-[#1C1C1E] p-0 text-zinc-400 sm:max-w-[425px]">
        <button
          onClick={onClose}
          className="ring-offset-background absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>

        <div className="px-6 pt-6">
          <DialogHeader className="pb-6 text-center">
            <DialogTitle className="text-xl text-white">Referral Rewards</DialogTitle>
            <DialogDescription>
              Earn a 50% rebate from your friends&apos; trading fees when they trade using your referral link.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-lg font-medium text-white">My Rewards</h3>
              <div className="mb-4 flex items-center justify-between">
                <span className="text-3xl font-bold text-[#FFB800]">{available_rewards} SOL</span>
                <Button
                  variant="secondary"
                  className="flex items-center gap-1 rounded-md bg-[#FFB800]/20 py-2 text-[#FFB800] hover:bg-[#FFB800]/30"
                  onClick={handleDeepLink}
                >
                  Withdraw in App <ArrowUpRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {/*
            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-lg bg-[#2C2C2E] p-4">
                <div className="mb-2 text-sm text-zinc-400">Referral Link Clicked</div>
                <div className="text-2xl font-semibold text-white">1,000</div>
              </div>
              <div className="rounded-lg bg-[#2C2C2E] p-4">
                <div className="mb-2 text-sm text-zinc-400">Total Friends Trades</div>
                <div className="text-2xl font-semibold text-white">30</div>
              </div>
            </div> */}
            <DialogDescription>
              Minimum withdrawal: 0.01 SOL Withdrawal should arrive within 60 minutes.
            </DialogDescription>

            <div className="pb-6 pt-4 text-yellow-500">
              <Button
                variant="secondary"
                className="w-full font-medium"
                onClick={() => window.open(getAppStoreUrl(), '_blank')}
              >
                Download KryptoGO App
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
