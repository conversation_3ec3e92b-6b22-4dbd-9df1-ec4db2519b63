'use client';

import { Command } from 'cmdk';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useRef } from 'react';

import useIsMobile from '@/hooks/useIsMobile';
import { CHAIN_ID_TO_NETWORK, CHAIN_ID_TO_NAME } from '@/lib/chains';
import type { OKXSearchTokensResult, SearchedToken } from '@/types/okx';

export function Search() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [tokens, setTokens] = useState<SearchedToken[]>([]);
  const [loading, setLoading] = useState(false);
  const overlayRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const t = useTranslations('search');

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (overlayRef.current && e.target === overlayRef.current) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('click', handleClick);
      return () => document.removeEventListener('click', handleClick);
    }
  }, [open]);

  useEffect(() => {
    if (!search || !open) {
      setTokens([]);
      return;
    }

    const fetchTokens = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/okx/search?keyword=${encodeURIComponent(search)}`, {
          headers: {
            accept: 'application/json',
          },
        });
        const data: OKXSearchTokensResult = await response.json();
        setTokens(data ?? []);
      } catch (error) {
        console.error('Error fetching tokens:', error);
        setTokens([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimeout = setTimeout(fetchTokens, 300);
    return () => clearTimeout(debounceTimeout);
  }, [search, open]);

  const handleSelect = (address: string, chainId: string) => {
    const network = CHAIN_ID_TO_NETWORK[chainId] || chainId;
    router.push(`/token/${network}/${address}`);
    setOpen(false);
    setSearch('');
  };

  return (
    <>
      {isMobile ? (
        <button
          type="button"
          onClick={() => setOpen(true)}
          className="z-40 ml-auto flex h-8 w-8 items-center justify-center rounded-lg text-zinc-400 hover:text-zinc-300"
          aria-label={t('search')}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
          </svg>
        </button>
      ) : (
        <div className="flex flex-1 flex-row-reverse items-center gap-2">
          <button
            type="button"
            onClick={() => setOpen(true)}
            className="mx-2 flex h-10 w-[18rem] flex-row items-center overflow-hidden text-ellipsis text-nowrap rounded-md border border-zinc-700/50 bg-zinc-900/50 px-3 py-1 text-left text-zinc-600 hover:border-zinc-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 text-zinc-400"
            >
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
            </svg>
            <span className="flex-1 text-xs">{t('search-placeholder')}</span>
            <span
              onClick={async (e) => {
                e.stopPropagation();
                try {
                  const text = await navigator.clipboard.readText();
                  setSearch(text);
                  setOpen(true);
                } catch (err) {
                  console.error('Failed to read clipboard:', err);
                }
              }}
              className="ml-auto cursor-pointer text-xs font-medium text-white hover:text-zinc-300"
            >
              {t('paste')}
            </span>
          </button>
        </div>
      )}

      {open && (
        <Command.Dialog
          open={open}
          onOpenChange={setOpen}
          label={t('search-placeholder')}
          shouldFilter={false}
          className="fixed left-0 top-0 z-50 h-full w-full"
        >
          <div
            ref={overlayRef}
            className="fixed left-0 top-0 z-50 h-full w-full overflow-hidden bg-black/80 p-0 sm:p-4 md:p-6 lg:p-8"
          >
            <div
              className="relative mx-auto h-full max-h-[600px] w-full max-w-2xl overflow-hidden rounded-none bg-zinc-900 shadow-2xl sm:h-auto sm:rounded-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center border-b border-zinc-800">
                <Command.Input
                  value={search}
                  onValueChange={setSearch}
                  placeholder={t('search-input-placeholder')}
                  className="w-full bg-transparent px-2 py-1.5 text-base text-white outline-none placeholder:text-zinc-500 md:text-sm"
                  autoFocus
                />
                <span
                  onClick={async (e) => {
                    e.stopPropagation();
                    try {
                      const text = await navigator.clipboard.readText();
                      setSearch(text);
                    } catch (err) {
                      console.error('Failed to read clipboard:', err);
                    }
                  }}
                  className="cursor-pointer px-4 py-2 text-sm text-zinc-400 hover:text-white"
                >
                  {t('paste')}
                </span>
                <button
                  onClick={() => setOpen(false)}
                  className="hidden px-4 py-2 text-sm text-zinc-400 hover:text-white sm:block"
                >
                  {t('esc')}
                </button>
                <button
                  onClick={() => setOpen(false)}
                  className="px-4 py-2 text-zinc-400 hover:text-white sm:hidden"
                  aria-label={t('close-search')}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                </button>
              </div>

              <Command.List className="h-[calc(100%-56px)] overflow-y-auto p-2 sm:max-h-[300px]">
                <Command.Empty className="p-4 text-sm text-zinc-500">
                  {loading ? t('loading') : t('no-results')}
                </Command.Empty>

                {search && !loading && tokens.length > 0 && (
                  <Command.Group heading={t('search-results')} className="px-2 pb-2 text-sm text-zinc-500">
                    {tokens.map((token) => (
                      <Command.Item
                        key={`${token.chainId}-${token.tokenContractAddress}`}
                        value={token.tokenContractAddress}
                        onSelect={() => handleSelect(token.tokenContractAddress, token.chainId)}
                        className="flex cursor-pointer items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-zinc-800"
                      >
                        <div className="flex items-center gap-2">
                          {token.tokenLogoUrl && (
                            <Image
                              src={token.tokenLogoUrl}
                              alt={token.tokenSymbol}
                              width={24}
                              height={24}
                              className="rounded-full"
                            />
                          )}
                          <div className="flex flex-col">
                            <span className="text-white">
                              {token.tokenName} ({token.tokenSymbol})
                            </span>
                            <span className="text-xs text-zinc-500">{token.tokenContractAddress}</span>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className="rounded-md bg-zinc-800 px-2 py-1 text-xs text-zinc-400">
                            {CHAIN_ID_TO_NAME[token.chainId] || token.chainId}
                          </span>
                        </div>
                      </Command.Item>
                    ))}
                  </Command.Group>
                )}
              </Command.List>
            </div>
          </div>
        </Command.Dialog>
      )}
    </>
  );
}
