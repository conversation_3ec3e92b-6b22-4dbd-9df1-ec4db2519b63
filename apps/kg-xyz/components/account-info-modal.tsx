'use client';

import { X } from 'lucide-react';

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@kryptogo/2b';
import { useKGUser } from '@kryptogo/kryptogokit-sdk-react';
import { getAppStoreUrl } from '@kryptogo/utils';

interface AccountInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AccountInfoModal({ isOpen, onClose }: AccountInfoModalProps) {
  const { userInfo, logout } = useKGUser();
  const handleLogout = async () => {
    await logout();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="border-zinc-800 bg-zinc-950 p-0 text-zinc-400 sm:max-w-[425px]">
        <button
          onClick={onClose}
          className="ring-offset-background absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>
        <div className="px-6 pt-6">
          <DialogHeader className="pb-6">
            <DialogTitle className="text-xl text-white">Account Info</DialogTitle>
            <p className="mt-1 text-left text-sm text-zinc-400">
              Please login with the account below at KryptoGO App to check and withdraw your referral rewards.
            </p>
          </DialogHeader>

          <div className="space-y-4 border-t border-zinc-800 py-6">
            <div className="flex items-center justify-between">
              <span className="text-sm">Email</span>
              <span className="font-medium text-white">{userInfo?.email}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Phone</span>
              <span className="font-medium text-white">{userInfo?.phone_number}</span>
            </div>
            {userInfo?.handle && (
              <div className="flex items-center justify-between">
                <span className="text-sm">Handle</span>
                <span className="font-medium text-white">@{userInfo.handle}</span>
              </div>
            )}
          </div>

          <div className="border-t border-zinc-800 py-6 text-yellow-500">
            <Button
              variant="secondary"
              className="w-full font-medium"
              onClick={() => window.open(getAppStoreUrl(), '_blank')}
            >
              Download KryptoGO App
            </Button>
          </div>
          <div className="border-t border-zinc-800 py-6">
            <Button
              variant="ghost"
              className="w-full font-medium text-zinc-400 hover:text-white"
              onClick={handleLogout}
            >
              Logout
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
