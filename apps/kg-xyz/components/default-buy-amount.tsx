'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState, useEffect } from 'react';

import { useDefaultBuyAmount } from '@/contexts/default-buy-amount-context';
import { usePrices } from '@/contexts/price-context';
import { SOL_ADDRESS } from '@/lib/chains';
import { formatNumber } from '@/lib/format';

export function DefaultBuyAmount() {
  const { defaultBuyAmount, setDefaultBuyAmount } = useDefaultBuyAmount();
  const t = useTranslations('trading-section');
  const [inputValue, setInputValue] = useState(defaultBuyAmount.toString());
  const { prices } = usePrices();
  const solPrice = prices?.[`sol:${SOL_ADDRESS}`] ?? 0;
  const usdValue = solPrice * defaultBuyAmount;

  // Update input value when defaultBuyAmount changes externally
  useEffect(() => {
    setInputValue(defaultBuyAmount.toString());
  }, [defaultBuyAmount]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Only update context if value is a valid number
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      setDefaultBuyAmount(numValue);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <div className="hidden h-4 w-4 text-yellow-400 md:block">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
          <path d="M13 2L3 14H12L11 22L21 10H13L13 2Z" fill="currentColor" />
        </svg>
      </div>
      <span className="mr-2 hidden text-xs text-zinc-400 md:block md:text-sm">{t('set-default-buy-amount')}</span>
      <div className="flex flex-col items-center gap-1 md:flex-row">
        <div className="flex items-center rounded-md border border-zinc-700/50 px-2 py-1 md:px-3 md:py-1.5">
          <span className="mr-2 flex h-5 w-5 items-center">
            <Image src="/chain-icons/icon-solana.png" alt="SOL" width={20} height={20} className="object-contain" />
          </span>
          <input
            type="text"
            value={inputValue}
            onChange={handleChange}
            className="w-16 bg-transparent text-xs text-white focus:outline-none md:text-sm"
            min="0.1"
            step="0.1"
          />
          <span className="-ml-5 text-xs text-zinc-400 md:text-sm">SOL</span>
        </div>
        {solPrice > 0 && (
          <div className="ml-2 text-center text-xs text-zinc-400">
            (≈ ${formatNumber(usdValue, { maximumFractionDigits: 2 })} USD)
          </div>
        )}
      </div>
    </div>
  );
}
