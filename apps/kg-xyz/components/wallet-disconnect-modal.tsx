'use client';

import { X } from 'lucide-react';
import { LogOut } from 'lucide-react';
import Image from 'next/image';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useWallet } from '@solana/wallet-adapter-react';

import { CopyButton } from './copy-button';
import { Button } from './ui/button';
import { Card } from './ui/card';

interface WalletDisconnectModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WalletDisconnectModal({ isOpen, onClose }: WalletDisconnectModalProps) {
  const { publicKey, disconnect } = useWallet();

  const handleDisconnect = async () => {
    try {
      await disconnect();
      onClose();
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };
  //
  //   // Format the wallet address to show first and last few characters
  //   const formatAddress = (address: string) => {
  //     if (!address) return '';
  //     return `${address.slice(0, 6)}...${address.slice(-4)}`;
  //   };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-[26rem] overflow-hidden bg-[#ccced5] p-0 data-[state=open]:rounded-3xl">
        <Card className="w-full max-w-[26rem] rounded-3xl border border-gray-200 shadow-lg">
          <div className="flex items-center justify-between bg-white p-4">
            <div className="shadow-rk flex items-center gap-3 rounded-xl p-2">
              <div className="flex h-6 w-6 items-center justify-center">
                <Image
                  src="/chain-icons/icon-solana.png"
                  alt="KryptoGO"
                  width={24}
                  height={24}
                  className="h-full w-full rounded-full object-contain"
                />
              </div>

              <div className="flex items-center gap-1">
                <span className="font-bold text-gray-800">
                  {publicKey?.toBase58().slice(0, 6) + '...' + publicKey?.toBase58().slice(-4)}
                </span>
                <CopyButton text={publicKey?.toBase58() ?? ''} />
              </div>
            </div>

            {onClose && (
              <button
                onClick={onClose}
                className="-mt-3 rounded-full bg-[#e8eaee] p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
                aria-label="Close"
              >
                <X strokeWidth={3} size={18} />
              </button>
            )}
          </div>

          <div className="bg-white px-4 pb-4">
            <Button
              variant="outline"
              className="shadow-rk flex w-full items-center justify-center gap-2 rounded-xl border-none text-sm font-semibold text-black"
              onClick={handleDisconnect}
            >
              <LogOut size={16} />
              Disconnect
            </Button>
          </div>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
