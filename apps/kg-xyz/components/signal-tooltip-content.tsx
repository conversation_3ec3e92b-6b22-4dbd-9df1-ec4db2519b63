import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';

import { SignalRankBadge, SignalStrength } from './signal-rank-badge';

interface SignalTooltipContentProps {
  signalRank: SignalStrength;
  averageHoldingRank: SignalStrength;
  averageWinRateRank: SignalStrength;
  average_holding: number;
  average_win_rate: number;
  token_price: number;
  buy_entry_price: number;
}

// Unified signal configuration for all rank types
export const signalConfig = {
  [SignalStrength.BIG_BUY]: {
    icon: (
      <svg width="15" height="20" viewBox="0 0 15 20" fill="none">
        <path
          d="M13.1538 9.12935C12.9211 8.82584 12.6378 8.56279 12.3748 8.29974C11.6969 7.69271 10.928 7.25767 10.2805 6.62029C8.77303 5.14318 8.43916 2.70493 9.4003 0.833252C8.43916 1.06595 7.59944 1.59204 6.88112 2.16872C4.26076 4.2731 3.22881 7.98611 4.46311 11.173C4.50357 11.2742 4.54404 11.3754 4.54404 11.5069C4.54404 11.7295 4.39229 11.9318 4.18994 12.0128C3.95725 12.1139 3.71443 12.0532 3.52221 11.8913C3.46479 11.8432 3.41676 11.7849 3.38057 11.7194C2.23732 10.2726 2.05521 8.19857 2.82412 6.53935C1.13455 7.91529 0.213885 10.2422 0.345408 12.4377C0.406111 12.9435 0.466814 13.4494 0.638807 13.9553C0.780448 14.5623 1.05361 15.1693 1.35713 15.7055C2.44978 17.4558 4.3417 18.7103 6.37526 18.9633C8.54034 19.2364 10.8572 18.8419 12.5164 17.3445C14.3678 15.6651 15.0153 12.9739 14.0643 10.6672L13.9328 10.4041C13.7203 9.93873 13.1538 9.12935 13.1538 9.12935ZM9.95674 15.5032C9.67346 15.746 9.20807 16.009 8.84385 16.1102C7.71073 16.5149 6.5776 15.9483 5.90986 15.2806C7.11381 14.9973 7.83213 14.107 8.04459 13.2066C8.21659 12.3972 7.89284 11.7295 7.76131 10.9504C7.63991 10.2018 7.66014 9.56439 7.9333 8.8663C8.12553 9.25076 8.32787 9.63521 8.57069 9.93873C9.34971 10.9504 10.5739 11.3956 10.8369 12.7715C10.8774 12.9132 10.8976 13.0548 10.8976 13.2066C10.928 14.0362 10.5638 14.9467 9.95674 15.5032Z"
          fill="#FFC211"
        />
      </svg>
    ),
    borderColor: 'border-yellow-400',
    message: 'Significant smart money holdings with minimal recent price increase. This could be a good entry point!',
    actionText: 'Strong Buy',
    styleClasses: 'bg-yellow-400/20 text-yellow-400',
    smartMoneyHolding: {
      text: 'Excellent',
    },
    priceSignal: {
      text: 'Prime Entry',
    },
    winRate: {
      text: 'Superior',
    },
  },
  [SignalStrength.SMALL_BUY]: {
    icon: (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9.99967 3.33325C9.53944 3.33325 9.16634 3.70635 9.16634 4.16659V9.16658H4.16634C3.7061 9.16658 3.33301 9.53968 3.33301 9.99992C3.33301 10.4602 3.7061 10.8333 4.16634 10.8333H9.16634V15.8333C9.16634 16.2935 9.53944 16.6666 9.99967 16.6666C10.4599 16.6666 10.833 16.2935 10.833 15.8333V10.8333H15.833C16.2932 10.8333 16.6663 10.4602 16.6663 9.99992C16.6663 9.53968 16.2932 9.16658 15.833 9.16658H10.833V4.16659C10.833 3.70635 10.4599 3.33325 9.99967 3.33325Z"
          fill="white"
        />
      </svg>
    ),
    borderColor: 'border-blue-400',
    message:
      'Smart money shows interest, but the price has already gone up. Consider a small position to test the waters.',
    actionText: 'Strategic Entry',
    styleClasses: 'bg-blue-400/20 text-blue-400',
    smartMoneyHolding: {
      text: 'Good',
    },
    priceSignal: {
      text: 'Acceptable',
    },
    winRate: {
      text: 'Strong',
    },
  },

  [SignalStrength.SMALL_SELL]: {
    icon: (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9.99967 19.1666C15.0623 19.1666 19.1663 15.0625 19.1663 9.99992C19.1663 4.93731 15.0623 0.833252 9.99967 0.833252C4.93706 0.833252 0.833008 4.93731 0.833008 9.99992C0.833008 15.0625 4.93706 19.1666 9.99967 19.1666ZM14.3049 6.03665L8.25488 12.49L6.11107 10.2033C5.85682 9.93212 5.44461 9.93212 5.19036 10.2033C4.93611 10.4745 4.93611 10.9142 5.19036 11.1854L7.79453 13.9632C8.04877 14.2344 8.46099 14.2344 8.71524 13.9632L15.2257 7.01874C15.4799 6.74755 15.4799 6.30785 15.2257 6.03665C14.9714 5.76545 14.5592 5.76545 14.3049 6.03665Z"
          fill="#FF8D3A"
        />
      </svg>
    ),

    borderColor: 'border-orange-400',
    message:
      'Smart money is reducing holdings, and the price has risen significantly. Recommend taking profits gradually.',
    actionText: 'Reduce Position',
    styleClasses: 'bg-orange-400/20 text-orange-400',
    smartMoneyHolding: {
      text: 'Average',
    },
    priceSignal: {
      text: 'Late to Party',
    },
    winRate: {
      text: 'Moderate',
    },
  },
  [SignalStrength.BIG_SELL]: {
    icon: (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M10.0003 18.3332C5.39795 18.3332 1.66699 14.6022 1.66699 9.99984C1.66699 5.39746 5.39795 1.6665 10.0003 1.6665C14.6027 1.6665 18.3337 5.39746 18.3337 9.99984C18.3337 14.6022 14.6027 18.3332 10.0003 18.3332ZM9.16699 6.6665V9.99984C9.16699 10.4601 9.54009 10.8332 10.0003 10.8332C10.4606 10.8332 10.8337 10.4601 10.8337 9.99984V6.6665C10.8337 6.20627 10.4606 5.83317 10.0003 5.83317C9.54009 5.83317 9.16699 6.20627 9.16699 6.6665ZM10.0003 14.1665C10.5756 14.1665 11.042 13.7001 11.042 13.1248C11.042 12.5495 10.5756 12.0832 10.0003 12.0832C9.42503 12.0832 8.95866 12.5495 8.95866 13.1248C8.95866 13.7001 9.42503 14.1665 10.0003 14.1665Z"
          fill="#E1004A"
        />
      </svg>
    ),
    borderColor: 'border-rose-500',
    message:
      "Smart money holdings have dropped sharply, and the price is already high. If you're still holding, it's advisable to exit soon to manage risk.",
    actionText: 'Smart Money Exit',
    styleClasses: 'bg-rose-500/20 text-rose-500',
    smartMoneyHolding: {
      text: 'Poor',
    },
    priceSignal: {
      text: 'FOMO Zone',
    },
    winRate: {
      text: 'Weak',
    },
  },
};

const getPriceSignal = (priceSignal: number) => {
  switch (true) {
    case priceSignal < 30:
      return SignalStrength.BIG_BUY;
    case priceSignal < 80:
      return SignalStrength.SMALL_BUY;
    case priceSignal < 200:
      return SignalStrength.SMALL_SELL;
    default:
      return SignalStrength.BIG_SELL;
  }
};

export function SignalTooltipContent({
  signalRank,
  averageHoldingRank,
  averageWinRateRank,
  average_holding,
  average_win_rate,
  token_price,
  buy_entry_price,
}: SignalTooltipContentProps) {
  const t = useTranslations('signal-tooltip');

  return (
    <div className="relative rounded-lg border-none">
      <div className="absolute bottom-[-6px] left-1/2 h-3 w-3 -translate-x-1/2 rotate-45 transform border-none bg-[#1E1E1E]"></div>
      <div className="rounded-lg bg-[#1E1E1E] p-4">
        <div
          className={cn(
            'flex items-start rounded-lg border px-3 py-2',
            signalConfig[signalRank].borderColor,
            signalConfig[signalRank].styleClasses,
          )}
        >
          <div className="p-2">{signalConfig[signalRank].icon}</div>
          <div>
            <p className={cn('text-sm font-medium text-white')}>
              {t(`signal-actions.${signalRank}.actionText`, { defaultMessage: signalConfig[signalRank].actionText })}
            </p>
            <p className="text-xs text-white">
              {t(`signal-actions.${signalRank}.message`, { defaultMessage: signalConfig[signalRank].message })}
            </p>
          </div>
        </div>
        <div className="mt-4 grid grid-rows-3 gap-4 text-center sm:grid-cols-3 sm:grid-rows-1">
          <div className="flex flex-col items-center gap-2 rounded-lg bg-[#252525] p-3">
            <span className="text-xs font-medium text-[#A0A0A0]">{t('smart-money-holdings')}</span>
            <span className="mt-1 text-base font-medium text-white">
              {(average_holding * 100).toLocaleString('en-US', { maximumFractionDigits: 2 })}%
            </span>
            <SignalRankBadge type="smartMoneyHolding" signalStrength={averageHoldingRank} signalConfig={signalConfig} />
          </div>
          <div className="flex flex-col items-center gap-2 rounded-lg bg-[#252525] p-3">
            <span className="text-xs font-medium text-[#A0A0A0]">
              {t('price-since-signal')} <br />
              <br />
            </span>
            <span className="mt-1 text-base font-medium text-white">
              {+(token_price || 0) - +buy_entry_price > 0 ? '+' : ''}
              {(((+(token_price || 0) - +buy_entry_price) / +buy_entry_price) * 100).toFixed(2)}%
            </span>
            <SignalRankBadge
              type="priceSignal"
              signalStrength={getPriceSignal(((+(token_price || 0) - +buy_entry_price) / +buy_entry_price) * 100)}
              signalConfig={signalConfig}
            />
          </div>
          <div className="flex flex-col items-center gap-2 rounded-lg bg-[#252525] p-3">
            <span className="text-xs font-medium text-[#A0A0A0]">{t('smart-money-win-rate')}</span>
            <span className="mt-1 text-base font-medium text-white">
              {(average_win_rate * 100).toLocaleString('en-US', { maximumFractionDigits: 2 })}%
            </span>
            <SignalRankBadge type="winRate" signalStrength={averageWinRateRank} signalConfig={signalConfig} />
          </div>
        </div>
      </div>
    </div>
  );
}
