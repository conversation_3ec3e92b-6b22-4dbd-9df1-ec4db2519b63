'use client';

import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useState, useRef, useEffect } from 'react';

import { CreditDropdown } from '@/components/credit-dropdown';
// Added
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useTokenAnalysisCredits } from '@/hooks/use-token-analysis-credits';
import { useIsMobile } from '@/hooks/useIsMobile';

export function HeaderCreditDisplay() {
  const isMobile = useIsMobile(); // Added
  const { address: publicKey } = useDebugWallet();
  const { data: { credits } = { credits: 0 }, refetch: refetchCredits } = useTokenAnalysisCredits(publicKey);
  const [showCreditDropdown, setShowCreditDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside (desktop only)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile) return; // Explicitly do nothing if on mobile
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowCreditDropdown(false);
      }
    };

    if (showCreditDropdown && !isMobile) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      // Ensure we only remove if it might have been added for desktop
      if (!isMobile) {
        document.removeEventListener('mousedown', handleClickOutside);
      }
    };
  }, [showCreditDropdown, isMobile]); // Added isMobile to dependencies

  const handleCreditUpdate = () => {
    // Refetch credits after purchase/claim
    refetchCredits();
  };

  if (!publicKey) {
    return null;
  }

  const creditDisplayButton = (
    <button
      // onClick is handled differently for mobile (by DrawerTrigger) and desktop
      onClick={!isMobile ? () => setShowCreditDropdown(!showCreditDropdown) : undefined}
      type="button"
      className="flex h-8 items-center gap-2 rounded-md border border-zinc-700/50 bg-zinc-900/50 px-3 py-1 text-white hover:bg-zinc-800 md:h-10"
    >
      <Image src="/icons/credit.svg" alt="Credits" width={16} height={16} className="text-yellow-400" />
      <div className="flex flex-col items-start">
        <div className="text-sm font-medium text-white">{credits ?? '...'}</div>
      </div>
    </button>
  );

  return (
    <div className="relative" ref={dropdownRef}>
      {isMobile ? (
        <Drawer open={showCreditDropdown} onOpenChange={setShowCreditDropdown}>
          <DrawerTrigger asChild>{creditDisplayButton}</DrawerTrigger>
          <DrawerContent
            // Event stopping moved to a specific div around CreditDropdown
            className="flex max-h-[85vh] flex-col rounded-t-lg border-t border-zinc-800/50 bg-zinc-950 focus:outline-none"
          >
            <div
              className="flex-grow overflow-hidden" // Use overflow-hidden and let CreditDropdown handle its own scroll
              onPointerDownCapture={(e) => (e.nativeEvent as Event).stopImmediatePropagation()} // Stop propagation for the content area only
            >
              <CreditDropdown onCreditUpdate={handleCreditUpdate} isDrawerContext />
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <>
          {creditDisplayButton}
          <AnimatePresence initial={false}>
            {showCreditDropdown && (
              <motion.div
                key="credit-dropdown-wrapper" // Keep key for AnimatePresence
                className="absolute left-1/2 top-full z-[9999] mt-1 -translate-x-1/2"
              >
                <CreditDropdown onCreditUpdate={handleCreditUpdate} />
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </div>
  );
}
