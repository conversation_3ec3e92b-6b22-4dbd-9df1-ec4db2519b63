'use client';

import * as React from 'react';

import { <PERSON>er, Drawer<PERSON>ontent, <PERSON>er<PERSON>eader, DrawerT<PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/useIsMobile';
import { cn } from '@/lib/utils';

interface ResponsiveTooltipDrawerProps {
  title: string;
  description: string;
  className?: string;
  children?: React.ReactNode;
}

export function ResponsiveTooltipDrawer({ title, description, className, children }: ResponsiveTooltipDrawerProps) {
  const isMobile = useIsMobile();

  // For mobile, use a Drawer
  if (isMobile) {
    return (
      <span
        className={cn(
          'inline-block cursor-pointer text-xs font-light text-zinc-400 underline decoration-dotted underline-offset-[3px] md:text-[0.75rem]',
          className,
        )}
      >
        <Drawer>
          <DrawerTrigger asChild>
            <span
              className={cn(
                'w-full text-left text-xs underline decoration-dotted underline-offset-[3px] md:text-[0.75rem]',
                className,
              )}
            >
              {title}
            </span>
          </DrawerTrigger>
          <DrawerContent className="border-t border-zinc-800">
            <div className="mx-auto w-full max-w-sm pb-8">
              <DrawerHeader>
                <DrawerTitle>{title}</DrawerTitle>
                <div className="text-muted-foreground py-2 text-sm">{description}</div>
              </DrawerHeader>
              {children && (
                <div className="p-4">{React.isValidElement(children) ? children : <div>{children}</div>}</div>
              )}
            </div>
          </DrawerContent>
        </Drawer>
      </span>
    );
  }

  // For desktop, use a Tooltip
  return (
    <TooltipProvider delayDuration={0} disableHoverableContent>
      <Tooltip>
        <TooltipTrigger
          className={cn(
            'cursor-pointer text-left text-xs font-light text-zinc-400 underline decoration-dotted underline-offset-[3px] md:text-[0.75rem]',
            className,
          )}
        >
          {title}
        </TooltipTrigger>
        <TooltipContent side="bottom" align="start" className="max-w-xs bg-zinc-700 p-4 text-sm text-white">
          <div>
            <p>{description}</p>
            {children && (React.isValidElement(children) ? children : <div>{children}</div>)}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
