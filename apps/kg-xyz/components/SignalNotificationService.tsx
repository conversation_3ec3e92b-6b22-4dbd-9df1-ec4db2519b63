'use client';

import { useEffect, useCallback } from 'react';

import { formatNumber } from '@/lib/format';

interface SignalNotificationServiceProps {
  onNewSignal?: () => void;
  websocketUrl: string;
}

export const SignalNotificationService = ({ onNewSignal, websocketUrl }: SignalNotificationServiceProps) => {
  const requestNotificationPermission = useCallback(async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      console.error('Notifications are not supported in this browser');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();

      if (permission === 'granted') {
        console.info('Notifications enabled successfully');
        return true;
      } else if (permission === 'denied') {
        console.error('Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      console.error('Failed to request notification permission');
    }

    return false;
  }, []);

  useEffect(() => {
    // Connect to WebSocket
    let ws = new WebSocket(websocketUrl);

    // Request notification permission when component mounts
    requestNotificationPermission();

    // Set up WebSocket event handlers
    ws.onopen = () => {
      const message = { event: 'token_signal_subscribe' };
      ws.send(JSON.stringify(message));
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data?.event === 'token_sell_signal') return;
      const symbol = data?.data?.symbol;
      const price = data?.data?.price;
      if (!symbol || !price) return;
      new Notification('🟢 BUY Signal Alert!', {
        body: `$${symbol}: $${formatNumber(price, { notation: 'compact', maximumFractionDigits: 6 })}`,
        icon: '/logo.svg',
      });

      // Call the callback function if provided
      if (onNewSignal) {
        onNewSignal();
      }

      console.log(data, 'websocket get data');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    ws.onclose = () => {
      console.log('WebSocket disconnected. Attempting to reconnect...');
      setTimeout(() => {
        const newWs = new WebSocket(websocketUrl);
        newWs.onopen = ws.onopen;
        newWs.onmessage = ws.onmessage;
        newWs.onerror = ws.onerror;
        newWs.onclose = ws.onclose;
        ws = newWs;
      }, 1000);
    };

    // Clean up WebSocket connection when component unmounts
    return () => ws.close();
  }, [websocketUrl, onNewSignal, requestNotificationPermission]);

  // This component doesn't render anything visible
  return null;
};
