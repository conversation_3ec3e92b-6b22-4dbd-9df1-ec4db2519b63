'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect, useRef } from 'react';

import { CopyButton } from '@/components/copy-button';
import { useWalletConnectModal } from '@/contexts/wallet-connect-modal-context';
import { useAssetsQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import useIsMobile from '@/hooks/useIsMobile';
import { cn } from '@/lib/utils';
import { sendGTMEvent } from '@next/third-parties/google';
import { useWallet } from '@solana/wallet-adapter-react';

import { HeaderCreditDisplay } from './header-credit-display';
import { Search } from './search';
import { TradingStatsPanel } from './trading-stats-panel';
import { WalletDisconnectModal } from './wallet-disconnect-modal';

export function Header({ handleToggle }: { handleToggle?: () => void }) {
  const t = useTranslations('header');
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const pathname = usePathname();
  const { address: publicKey } = useDebugWallet();
  const previousPublicKey = useRef<typeof publicKey>();
  const { openModal } = useWalletConnectModal();
  const [isWalletDisconnectModalOpen, setIsWalletDisconnectModalOpen] = useState(false);
  const { wallet } = useWallet();
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(false);

  // Fetch wallet assets to get SOL balance
  const { data: assets } = useAssetsQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Get SOL balance from assets
  const solBalance = assets?.data?.[0]?.tokenAssets?.find((asset) => asset.tokenContractAddress === '')?.balance || '0';

  // Format SOL balance for display
  const formattedSolBalance = parseFloat(solBalance).toFixed(5);

  useEffect(() => {
    if (publicKey && previousPublicKey.current !== publicKey) {
      sendGTMEvent({
        event: 'app_xyz_connect_wallet_success',
        walletAddress: publicKey,
        platform: 'web',
      });
    }
    previousPublicKey.current = publicKey;
  }, [publicKey]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  // No longer needed with the context

  return (
    <header className="sticky top-0 z-50 w-full border-b border-white/10 bg-zinc-950/95">
      <div className="flex h-16 items-center px-2 md:h-14 md:px-6">
        {/* Logo with fixed width */}
        <Link className="flex-none" href="/">
          <Image
            width={24}
            height={24}
            src="/logo.svg"
            alt="KryptoGO"
            className="min-w-[24px] md:h-[24px] md:w-[24px]"
          />
        </Link>

        {/* Navigation - only shown on desktop */}
        {!isMobile && (
          <nav className="ml-6 flex items-center space-x-8">
            <div className="relative">
              <Link
                href="/"
                className={cn(
                  'text-sm font-medium transition-colors hover:text-white',
                  pathname === '/' ? 'font-medium text-white' : 'text-white/70',
                )}
              >
                {t('explore')}
              </Link>
              {pathname === '/' && <div className="absolute -bottom-0 left-0 right-0 h-[2px] bg-[#FFC211]"></div>}
            </div>
          </nav>
        )}

        {/* Search bar - takes remaining space on desktop, hidden on mobile */}
        <div className={cn('mx-1', isMobile ? 'ml-auto flex-none' : 'flex-1')}>
          <Search />
        </div>

        {/* Credit Display - only show when wallet is connected */}
        {publicKey && (
          <div className="mr-2 flex-none">
            <HeaderCreditDisplay />
          </div>
        )}

        {(() => {
          if (!publicKey) {
            return (
              <button
                className="flex-none whitespace-nowrap rounded-md bg-[#ffc30f] px-4 py-1.5 font-semibold text-black"
                style={{ flexBasis: '100px' }}
                onClick={() => {
                  sendGTMEvent({
                    event: 'app_xyz_connect_wallet_click',
                    platform: 'web',
                  });
                  openModal();
                }}
                type="button"
              >
                {t('connect')}
              </button>
            );
          }

          // Format the wallet address to show only the first 6 and last 4 characters
          const formattedAddress = publicKey ? `${publicKey.slice(0, 6)}...${publicKey.slice(-4)}` : '';

          return (
            <div className="z-10 flex items-center gap-0 md:gap-2">
              <button
                onClick={() => setIsWalletDisconnectModalOpen(true)}
                type="button"
                className="z-10 flex h-8 items-center rounded-md border border-zinc-700/50 bg-zinc-900/50 px-3 py-1 text-white md:h-10"
              >
                <div className="mr-2 hidden items-center justify-center md:flex">
                  <Image
                    className="h-5 w-5 md:h-5 md:w-5"
                    src={wallet?.adapter.icon || ''}
                    alt={t('wallet-icon')}
                    height={24}
                    width={24}
                  />
                </div>
                <div className="flex flex-col">
                  <div className="hidden items-center gap-1 text-xs text-zinc-400 md:flex">
                    <span>{formattedAddress}</span>
                    <CopyButton text={publicKey || ''} />
                  </div>
                </div>
                <div className="mx-1 hidden h-6 w-px self-center bg-zinc-700/50 md:mx-2 md:flex md:h-8"></div>
                <span className="mr-1 flex items-center">
                  <img src="/chain-icons/icon-solana.png" alt="SOL" className="h-4 w-4 object-contain" />
                </span>
                <div className="mr-2 flex items-center gap-1">
                  <span className="pl-1 text-xs font-medium">{formattedSolBalance} SOL</span>
                </div>
                <div className="flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white"
                  >
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
              </button>
              {isMobile && (
                <>
                  {/* My Holdings Button */}
                  <button
                    onClick={() => handleToggle?.() ?? setIsRightPanelOpen(!isRightPanelOpen)}
                    type="button"
                    className="z-10 ml-2 flex h-8 w-8 items-center justify-center rounded-md border border-zinc-700/50 bg-zinc-900/50 hover:bg-zinc-800"
                  >
                    <div className="relative h-5 w-5">
                      <Image
                        src="/ic-line-file-history.svg"
                        alt="History"
                        width={20}
                        height={20}
                        className="brightness-0 invert"
                        style={{ filter: 'brightness(0) invert(1)' }}
                      />
                    </div>
                  </button>

                  {/* Menu Button with Dropdown */}
                  <div className="relative" ref={menuRef}>
                    <button
                      onClick={() => setShowMenu(!showMenu)}
                      type="button"
                      className="z-10 ml-2 flex h-8 w-8 items-center justify-center rounded-md border border-zinc-700/50 bg-zinc-900/50 hover:bg-zinc-800"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-white"
                      >
                        <line x1="4" x2="20" y1="12" y2="12" />
                        <line x1="4" x2="20" y1="6" y2="6" />
                        <line x1="4" x2="20" y1="18" y2="18" />
                      </svg>
                    </button>

                    {/* Dropdown Menu */}
                    {showMenu && (
                      <div className="absolute right-0 top-full z-50 mt-1 w-48 rounded-md border border-zinc-700 bg-zinc-900 py-1 shadow-lg">
                        <button
                          className="flex w-full items-center px-4 py-2 text-left text-sm text-white hover:bg-zinc-800"
                          onClick={() => {
                            setShowMenu(false);
                            window.open(
                              'https://www.notion.so/kryptogo/KryptoGO-xyz-1e33499de8a2808291d7c98dc0703f6f',
                              '_blank',
                            );
                          }}
                        >
                          {t('tutorial')}
                        </button>
                        <button
                          className="flex w-full items-center px-4 py-2 text-left text-sm text-white hover:bg-zinc-800"
                          onClick={() => {
                            setShowMenu(false);
                            window.open('https://t.me/pump_hound_signal', '_blank');
                          }}
                        >
                          {t('get-instant-signal')}
                        </button>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          );
        })()}
      </div>
      {/* WalletConnectModal is now managed by the context */}
      <WalletDisconnectModal
        isOpen={isWalletDisconnectModalOpen}
        onClose={() => setIsWalletDisconnectModalOpen(false)}
      />
      {/* Trading Stats Panel - Only show on mobile */}
      {isMobile && (
        <TradingStatsPanel
          isOpen={isRightPanelOpen}
          toggleOpenAction={() => handleToggle?.() ?? setIsRightPanelOpen(!isRightPanelOpen)}
        />
      )}
    </header>
  );
}
