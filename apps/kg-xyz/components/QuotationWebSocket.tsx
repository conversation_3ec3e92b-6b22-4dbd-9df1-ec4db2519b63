'use client';

import { useEffect, useRef } from 'react';

interface QuotationWebSocketProps {
  chainId: string;
  tokenAddress: string;
  timeFrame: string;
  onData?: (data: any) => void;
}

export function QuotationWebSocket({ chainId, tokenAddress, timeFrame, onData }: QuotationWebSocketProps) {
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Close existing WebSocket if it exists
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    const ws = new WebSocket('wss://ws.gmgn.ai/quotation');
    wsRef.current = ws;

    ws.onopen = () => {
      const message = {
        action: 'subscribe',
        channel: 'kline',
        id: '79edb931241c9e64',
        data: [{ chain: chainId, addresses: tokenAddress, interval: timeFrame }],
      };
      ws.send(JSON.stringify(message));
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.channel === 'kline' && onData) {
        onData(data);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    ws.onclose = () => {
      wsRef.current = null;
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
      wsRef.current = null;
    };
  }, [chainId, tokenAddress, timeFrame, onData]);

  return null; // This component doesn't render anything
}
