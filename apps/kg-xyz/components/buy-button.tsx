'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useWalletConnectModal } from '@/contexts/wallet-connect-modal-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { SOL_ADDRESS } from '@/lib/chains';
import { executeJupiterSwap } from '@/lib/jupiter-swap';
import { getAssetByAddress } from '@/lib/okx';
import { cn } from '@/lib/utils';
import { sendGTMEvent } from '@next/third-parties/google';
import * as Sentry from '@sentry/nextjs';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { useQuery } from '@tanstack/react-query';

import { Button } from './ui/button';
import { Spinner } from './ui/spinner';

type Props = {
  tokenAddress: string;
  amount?: number;
  rawAmount?: number;
  sellMode?: boolean;
  label?: string;
  flashMode?: boolean;
};

export const BuyButton = (props: Props) => {
  const { tokenAddress, sellMode = false, label, flashMode } = props;
  const t = useTranslations('buy-button');
  const tTrading = useTranslations('trading-section');
  const { address: publicKey } = useDebugWallet();
  const { sendTransaction } = useWallet();
  const { connection } = useConnection();
  const [loading, setLoading] = useState(false);
  const [tokenDecimals, setTokenDecimals] = useState(6);
  const [pendingTx, setPendingTx] = useState<string | null>(null);
  const [txConfirmed, setTxConfirmed] = useState(false);
  const { openModal } = useWalletConnectModal();
  const { toast } = useToast();
  const { prices, slippages } = usePrices();

  const amount = props.amount || (props.rawAmount ?? 0) / 10 ** (sellMode ? tokenDecimals : 9);
  const rawAmount = Math.floor(props.rawAmount || (props.amount ?? 0) * 10 ** (sellMode ? tokenDecimals : 9));
  const fromPriceKey = sellMode ? `sol:${tokenAddress}` : `sol:${SOL_ADDRESS}`;
  const usdValue = amount * (prices[fromPriceKey] || 0.0);

  useEffect(() => {
    connection.getAccountInfo(new PublicKey(tokenAddress)).then((accountInfo) => {
      if (accountInfo?.data) {
        const array = Array.from(accountInfo.data);
        setTokenDecimals(array[44] || 6);
      }
    });
  }, [connection, tokenAddress]);

  const { refetch: refetchAssets } = useQuery({
    queryKey: ['assets', publicKey],
    queryFn: () => getAssetByAddress(publicKey || '', '501'),
    enabled: !!publicKey,
    refetchInterval: 10000,
  });

  const handleBuy = () => {
    setLoading(true);
    if (!publicKey?.toString()) {
      const url = new URL(window.location.href);
      url.searchParams.set('token', tokenAddress);
      window.history.replaceState({}, '', url.toString());

      openModal();
      setLoading(false);
      return;
    }

    // Track click event
    sendGTMEvent({
      event: `app_xyz_${sellMode ? 'sell' : 'buy'}_click`,
      token_address: tokenAddress,
      amount: `${amount}`,
      walletAddress: publicKey,
      platform: 'web',
    });

    // Execute Jupiter swap using the shared utility
    executeJupiterSwap({
      inputMint: sellMode ? tokenAddress : SOL_ADDRESS,
      outputMint: sellMode ? SOL_ADDRESS : tokenAddress,
      amount: rawAmount,
      slippageBps: Math.round(slippages[`sol:${tokenAddress}`] * 10000),
      publicKey,
      connection,
      sendTransaction,
      isSellMode: sellMode,
      baseToken: 'SOL',
      tokenAddress,
      amount_display: `${amount}`,
      usd_value: `${usdValue}`,
      toast,
      t: tTrading,
      onTransactionSubmitted: (txHash) => {
        setPendingTx(txHash);
      },
      onTransactionConfirmed: () => {
        setTxConfirmed(true);
        setTimeout(() => refetchAssets(), 2000);
      },
      onTransactionFailed: (error) => {
        console.error('Transaction failed:', error);
        Sentry.captureException(error);
      },
    }).finally(() => {
      setLoading(false);
    });
  };

  return (
    <Button
      disabled={loading}
      onClick={handleBuy}
      variant={flashMode ? 'ghost' : 'outline'}
      size="sm"
      className={cn(
        pendingTx ? 'text-amber-500' : sellMode ? 'text-pink-600' : 'text-emerald-500',
        flashMode
          ? 'flex h-auto items-center rounded-md bg-yellow-400 px-3 py-2 text-xs font-bold text-black shadow-md hover:bg-yellow-500 hover:text-black md:rounded-xl md:px-4 md:py-3 md:text-sm'
          : 'flex-1 border border-zinc-800 p-5 font-semibold',
      )}
    >
      {loading ? (
        <Spinner className="text-white" size="sm" />
      ) : pendingTx ? (
        t('confirming')
      ) : flashMode ? (
        <>
          <Image src="/ic-solid-lightning.svg" alt="lightning" width={14} height={14} className="mr-1 md:h-4 md:w-4" />{' '}
          {t('buy', { amount: amount || 0 })}
        </>
      ) : (
        `${label || amount}`
      )}
    </Button>
  );
};
