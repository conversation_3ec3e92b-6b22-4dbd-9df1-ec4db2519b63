{"typescript": {"quoteStyle": "preferSingle", "jsx.quoteStyle": "preferDouble"}, "json": {}, "markdown": {}, "toml": {}, "malva": {}, "markup": {}, "yaml": {}, "excludes": ["**/node_modules", "**/*-lock.json"], "plugins": ["https://plugins.dprint.dev/typescript-0.95.7.wasm", "https://plugins.dprint.dev/json-0.20.0.wasm", "https://plugins.dprint.dev/markdown-0.18.0.wasm", "https://plugins.dprint.dev/toml-0.7.0.wasm", "https://plugins.dprint.dev/g-plane/malva-v0.12.1.wasm", "https://plugins.dprint.dev/g-plane/markup_fmt-v0.20.0.wasm", "https://plugins.dprint.dev/g-plane/pretty_yaml-v0.5.1.wasm"]}