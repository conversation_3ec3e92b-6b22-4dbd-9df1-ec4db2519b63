import createNextIntlPlugin from 'next-intl/plugin';

import { withSentryConfig } from '@sentry/nextjs';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-ancestors 'none'",
          },
        ],
      },
    ];
  },
  webpack: (config, { isServer }) => {
    // Fix for node:inspector and other Node.js built-in modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        child_process: false,
        inspector: false,
        'node:inspector': false,
      };
    }
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'static.oklink.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'static.coinall.ltd',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'wallet-static.kryptogo.com',
        pathname: '/**',
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/okx/:path*',
        destination: 'https://www.okx.com/priapi/v1/dx/market/v2/:path*',
      },
      {
        source: '/api/okx-dex/:path*',
        destination: 'https://web3.okx.com/priapi/v5/dex/:path*',
      },
      {
        source: '/api/jupiter/:path*',
        destination: 'https://api.jup.ag/:path*',
      },
    ];
  },
};

// Only use Sentry webpack plugin when not using Turbopack
const useSentryWebpack = process.env.TURBOPACK !== '1';

// Injected content via Sentry wizard below
const sentryConfig = withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: 'kryptogo-jr',
  project: 'kryptogo-xyz',

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});

export default withNextIntl(useSentryWebpack ? sentryConfig : nextConfig);
