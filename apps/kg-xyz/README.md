# KryptoGO XYZ

KryptoGO XYZ is a web platform that serves as an independent testing ground for new features and user flows for the KryptoGO ecosystem. Built with Next.js 13 (App Router), TypeScript, and enhanced with shadcn UI components, it provides a modern and efficient development environment.
[![🏄‍♂️ Scheduled E2E Test](https://github.com/kryptogo/kg-frontend/actions/workflows/test-scheduled.yml/badge.svg)](https://github.com/kryptogo/kg-frontend/actions/workflows/test-scheduled.yml)
## Tech Stack

- Next.js 13 (App Router)
- TypeScript
- Tailwind CSS
- shadcn/ui
- @kryptogo/2b (Shared Components)

## Prerequisites

- Node.js >= 18.x (recommended: 18.20.5 LTS)
- pnpm >= 9.0.0
- Git

### Environment Setup

1. Install or update pnpm to the correct version:
```bash
npm install -g pnpm@latest
or
brew upgrade pnpm
```

2. Install the recommended Node.js version using nvm:
```bash
nvm install lts/hydrogen  # This installs Node.js 18.20.5 LTS
nvm use lts/hydrogen
```

3. Verify your installations:
```bash
node --version  # Should show v18.x.x
pnpm --version  # Should show ≥9.0.0
```

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/kryptogo/kg-frontend.git
cd kg-frontend
```

2. Install dependencies:
```bash
pnpm install
```

3. Start the development server:
```bash
pnpm run dev
```

The application will be available at `http://localhost:3000`

## Development & Testing

### Local Development

1. Make sure you're in the correct workspace:
```bash
cd apps/kg-xyz
```

2. Create a `.env.local` file based on `.env.example`

### Starting the Frontend

1. Start the development server:
```bash
pnpm dev
```

2. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

### Starting the Backend

1. Navigate to the server directory:
```bash
cd server
```

2. Install dependencies (if not already installed):
```bash
pnpm install
```

3. Start the NestJS development server:
```bash
pnpm start:dev
```

The backend server will run on [http://localhost:3001](http://localhost:3001).

### Running Both Services

For full functionality, you need to run both the frontend and backend services:

1. Start the backend server first:
```bash
cd server && pnpm start:dev
```

2. In a new terminal, start the frontend:
```bash
pnpm dev
```

Now you can access the application at [http://localhost:3000](http://localhost:3000) with full API functionality.

### Testing

We use several types of testing to ensure code quality:

1. **Unit Testing**
```bash
pnpm test
```

2. **E2E Testing**
```bash
pnpm test:e2e
```

3. **Component Testing**
```bash
pnpm test:component
```

### Code Quality

Before submitting your code:

1. Run linting:
```bash
pnpm lint
```

2. Run type checking:
```bash
pnpm typecheck
```

## Project Structure

```
apps/kg-xyz/
├── app/             # Next.js 13 app directory
├── components/      # Local components
├── lib/            # Utility functions and shared logic
├── styles/         # Global styles and Tailwind config
└── tests/          # Test files
```

## Contributing

1. Create a new branch from `main`
2. Make your changes
3. Run tests and ensure all checks pass
4. Submit a Pull Request

## License

Copyright 2025 KryptoGO. All rights reserved.
