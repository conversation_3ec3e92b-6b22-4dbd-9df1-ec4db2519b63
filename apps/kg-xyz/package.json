{"name": "kg-xyz", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbo", "dev:dependency": "turbo dev --ui=stream --filter=kg-xyz^...", "build": "next build", "start": "next start", "lint": "oxlint .", "test:unit": "vitest run", "coverage": "vitest run --coverage", "typecheck": "tsgo --project ./tsconfig.json", "test": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:prod": "playwright test --config ./playwright.config.prod.ts"}, "dependencies": {"@devnomic/marquee": "^1.0.3", "@jup-ag/api": "^6.0.42", "@kryptogo/2b": "workspace:*", "@kryptogo/configs": "workspace:*", "@kryptogo/kryptogokit-sdk-react": "workspace:*", "@kryptogo/utils": "workspace:*", "@next/third-parties": "canary", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/cli": "^2.42.4", "@sentry/nextjs": "^9", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.26", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/wallet-standard-wallet-adapter-base": "^1.1.4", "@solana/wallet-standard-wallet-adapter-react": "^1.1.4", "@solana/web3.js": "^1.98.0", "@tanstack/react-query": "^5.64.1", "@types/crypto-js": "^4.2.2", "bs58": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "catalog:", "cmdk": "^1.0.4", "crypto-js": "^4.2.0", "framer-motion": "^12.11.3", "idb": "^8.0.2", "lightweight-charts": "^4.2.2", "lucide-react": "^0.352.0", "motion": "^12.6.1", "motion-number": "^0.1.7", "next": "canary", "next-intl": "^4.0.3", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "react": "18.3.1", "react-cmdk": "^1.3.9", "react-dom": "18.3.1", "react-query-kit": "^3.3.1", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "require-in-the-middle": "^7.5.2", "shadcn-ui": "^0.9.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "catalog:", "vaul": "1.1.2", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@playwright/test": "^1.50.1", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/node": "^20.11.5", "@types/qrcode": "^1.5.5", "@types/react": "18.2.42", "@types/react-dom": "18.2.18", "@typescript/native-preview": "latest", "autoprefixer": "^10.4.17", "dprint": "^0.50.0", "eslint": "^8.56.0", "eslint-config-next": "^15.3.3", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "oxlint": "^1.1.0", "postcss": "^8.4.33", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}