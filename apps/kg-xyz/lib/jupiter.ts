import type { JupiterOrdersCacheMetadata, JupiterOrdersResponse } from '@/types/jupiter';
import { type JupiterLimitedOrder } from '@/types/jupiter';

import { OKXTransaction } from '../types/okx';
import { getCachedData, cacheData, getMetadataCacheKey, getPageCacheKey } from './indexedDB';
import { getTokenAveragePriceAtTime } from './okx';
import { sleep } from './utils';

/**
 * Fetches a specific page of Jupiter orders
 */
async function fetchOrdersPage(
  address: string,
  orderStatus: 'active' | 'history',
  page: number,
): Promise<JupiterOrdersResponse | null> {
  try {
    const response = await fetch(
      `https://lite-api.jup.ag/trigger/v1/getTriggerOrders?user=${address}&orderStatus=${orderStatus}&page=${page}`,
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching Jupiter orders page ${page}:`, error);
    return null;
  }
}

/**
 * Fetches Jupiter limit orders with pagination support
 * @param address The user's wallet address
 * @param orderStatus The order status to fetch (active/history)
 * @returns Jupiter limit orders from all pages combined
 */
export async function fetchJupiterOrders(
  address: string,
  orderStatus: 'active' | 'history',
): Promise<{
  orders: JupiterLimitedOrder[];
  orderStatus: string;
  user: string;
  page: number;
  totalItems: number;
  totalPages: number;
}> {
  // Skip caching for active orders or server-side rendering
  if (orderStatus !== 'history' || typeof window === 'undefined') {
    return fetchAllOrdersFromAPI(address, orderStatus);
  }

  try {
    // Get cache metadata to check if we have cached pages
    const metadata = await getCachedData<JupiterOrdersCacheMetadata>(
      getMetadataCacheKey(address, orderStatus),
      'metadata',
    );

    // If no metadata found, fetch all pages from API
    if (!metadata) {
      console.log(`No cached metadata found for ${address}, fetching all pages from API`);
      return fetchAllOrdersFromAPI(address, orderStatus);
    }

    // Always fetch the first page to check for new data
    const firstPageData = await fetchOrdersPage(address, orderStatus, 1);

    if (!firstPageData) {
      // If we can't fetch the first page, try to use cached data
      throw new Error(`Failed to fetch first page for ${address}`);
    }

    // Check if the totalItems has changed
    if (firstPageData.totalItems !== metadata.totalItemsLength) {
      console.log(
        `Total items changed from ${metadata.totalItemsLength} to ${firstPageData.totalItems}, refetching all data`,
      );
      return fetchAllOrdersFromAPI(address, orderStatus);
    }

    // Update the first page in cache
    await cacheData(getPageCacheKey(address, orderStatus, 1), 'page-data', firstPageData);

    // Get all cached pages and combine them
    const result = await getAllCachedOrders(address, orderStatus, metadata.totalPages);

    // If we couldn't get combined data from cache, fetch all from API
    if (!result) {
      return fetchAllOrdersFromAPI(address, orderStatus);
    }

    return result as JupiterOrdersResponse;
  } catch (error) {
    console.error(`Error with cached Jupiter orders:`, error);
    // Fallback to API if cache operations fail
    return fetchAllOrdersFromAPI(address, orderStatus);
  }
}

/**
 * Fetches all pages of orders from the API and caches them individually
 */
async function fetchAllOrdersFromAPI(
  address: string,
  orderStatus: 'active' | 'history',
): Promise<JupiterOrdersResponse> {
  const failResponse = {
    orders: [],
    orderStatus,
    user: '',
    page: 1,
    totalItems: 0,
    totalPages: 0,
  };

  try {
    // Fetch first page to get total pages
    const firstPageData = await fetchOrdersPage(address, orderStatus, 1);

    if (!firstPageData) {
      throw new Error('Failed to fetch first page for Jupiter orders');
    }

    const { totalPages } = firstPageData;

    // Cache the first page if it's history data
    if (orderStatus === 'history') {
      await cacheData(getPageCacheKey(address, orderStatus, 1), 'page-data', firstPageData);
    }

    if (totalPages <= 1) {
      // Cache metadata if it's history data
      if (orderStatus === 'history') {
        await cacheData(getMetadataCacheKey(address, orderStatus), 'metadata', {
          totalPages,
          lastUpdated: Date.now(),
          user: address,
          orderStatus,
          totalItemsLength: firstPageData.orders.length,
        });
      }
      return firstPageData; // Return immediately if only one page
    }

    // Initialize with first page data
    const allOrders = [...firstPageData.orders];

    // Fetch remaining pages
    for (let currentPage = 2; currentPage <= totalPages; currentPage++) {
      // Sleep for 1 second between requests
      await sleep(1000);

      const pageData = await fetchOrdersPage(address, orderStatus, currentPage);

      if (pageData) {
        allOrders.push(...pageData.orders);
        console.log(`Fetched page ${currentPage}/${totalPages} for Jupiter orders`);

        // Cache each page individually if it's history data
        if (orderStatus === 'history') {
          await cacheData(getPageCacheKey(address, orderStatus, currentPage), 'page-data', pageData);
        }
      }
    }

    // Combine results
    const result = {
      ...firstPageData,
      orders: allOrders,
      page: 1, // Reset to page 1 as we're returning all data
      totalItems: allOrders.length,
    };

    // Cache metadata if it's history data
    if (orderStatus === 'history') {
      await cacheData(getMetadataCacheKey(address, orderStatus), 'metadata', {
        totalPages,
        lastUpdated: Date.now(),
        user: address,
        orderStatus,
        totalItemsLength: allOrders.length,
      });
    }

    return result;
  } catch (error) {
    console.error(`Error fetching Jupiter orders:`, error);
    const metadata = await getCachedData<JupiterOrdersCacheMetadata>(
      getMetadataCacheKey(address, orderStatus),
      'metadata',
    );
    if (!metadata) {
      return failResponse;
    }
    // Get all cached pages and combine them
    const result = await getAllCachedOrders(address, orderStatus, metadata.totalPages);
    if (!result) {
      return failResponse;
    }
    return result;
  }
}

/**
 * Gets all cached orders and combines them
 */
async function getAllCachedOrders(
  address: string,
  orderStatus: 'active' | 'history',
  totalPages: number,
): Promise<JupiterOrdersResponse | null> {
  try {
    const allOrders: JupiterLimitedOrder[] = [];
    let firstPageData: JupiterOrdersResponse | null = null;

    // Fetch all pages from cache
    for (let page = 1; page <= totalPages; page++) {
      const pageData = await getCachedData<JupiterOrdersResponse>(
        getPageCacheKey(address, orderStatus, page),
        'page-data',
      );

      if (!pageData) {
        console.error(`Missing cached page ${page} for ${address}`);
        return null; // If any page is missing, return null to trigger a full refetch
      }

      if (page === 1) {
        firstPageData = pageData;
      }

      allOrders.push(...pageData.orders);
    }

    if (!firstPageData) {
      return null;
    }

    // Return combined results
    return {
      ...firstPageData,
      orders: allOrders,
      page: 1, // Reset to page 1 as we're returning all data
      totalItems: allOrders.length,
    };
  } catch (error) {
    console.error(`Error combining cached orders:`, error);
    return null;
  }
}

/**
 * Convert Jupiter limit orders to OKXTransaction format
 * @param orders Jupiter limit orders
 * @param address User's wallet address
 * @param tokenContractAddress Token contract address
 * @param chainId Chain ID
 * @param filterCompleted Whether to filter for completed orders only
 * @returns Converted transactions in OKXTransaction format
 */
export async function formatJupiterOrdersToTransactions(
  orders: JupiterLimitedOrder[],
  address: string,
  tokenContractAddress: string,
  chainId: string,
  filterCompleted: boolean = true,
): Promise<OKXTransaction[]> {
  try {
    // Filter for completed orders if requested
    const tokenOrders = orders.filter(
      (order) => order.outputMint === tokenContractAddress || order.inputMint === tokenContractAddress,
    );
    if (tokenContractAddress === 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm') {
      console.log({ tokenOrders });
    }

    const filteredOrders = filterCompleted
      ? tokenOrders.filter((order) => order.status === 'Completed' || order.trades.length > 0)
      : tokenOrders;
    // Transform Jupiter orders to match OKXTransaction format
    const jupiterTransactions: OKXTransaction[] = await Promise.all(
      filteredOrders.map(async (order): Promise<OKXTransaction> => {
        const isTokenInput = order.inputMint === tokenContractAddress;
        const baseToken = isTokenInput ? order.outputMint : order.inputMint;
        const [amount, baseTokenAmount] = isTokenInput
          ? [order.makingAmount, order.takingAmount]
          : [order.takingAmount, order.makingAmount];

        const baseTokenHistoricalPrice = await getTokenAveragePriceAtTime(
          baseToken,
          new Date(order.updatedAt).getTime(),
          chainId,
        );

        const turnover = `${parseFloat(baseTokenAmount) * baseTokenHistoricalPrice}`;
        const price = `${(parseFloat(baseTokenAmount) * baseTokenHistoricalPrice) / parseFloat(amount)}`;
        const internalTradingFill = `${order.trades
          .map(({ inputAmount, outputAmount }) => (isTokenInput ? inputAmount : outputAmount))
          .reduce((a, b) => a + parseFloat(b), 0)}`;

        return {
          // Type 1 = Buy, Type 2 = Sell
          type: isTokenInput ? 2 : 1,
          blockTime: new Date(order.updatedAt).getTime(),
          turnover,
          amount,
          price,
          singleRealizedProfit: '0',
          // Add required fields with default values
          blockHeight: 0,
          chainId: parseInt(chainId),
          flag: false,
          globalIndex: '',
          id: 0,
          txHash: order.closeTx || '',
          txHashUrl: '',
          walletAddress: address,
          isLimitedOrder: true,
          isPartialFill: order.trades.length > 0 && order.status !== 'Completed',
          internalTradingFill,
          internalTrading: order.trades,
        };
      }),
    );

    if (tokenContractAddress === '81YHAzq9yfptVbNTHiYuMVKVQ6ZNs3XeQFFENnNUpump') {
      console.log({ jupiterTransactions });
    }

    return jupiterTransactions;
  } catch (error) {
    console.error('Error converting Jupiter orders to transactions:', error);
    return [];
  }
}

/**
 * Cancel a Jupiter limit order
 * @param orderKey The order key to cancel
 * @param maker The maker's address
 * @returns Response from the cancel API
 */
export async function cancelJupiterOrder(orderKey: string, maker: string) {
  try {
    const response = await fetch('https://lite-api.jup.ag/trigger/v1/cancelOrder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order: orderKey,
        maker,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw new Error(`Failed to cancel Jupiter order: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export const JUPITER_ULTRA_API_BASE_URL = 'https://lite-api.jup.ag/ultra/v1';

// Function to get an order from Jupiter Ultra API
export const getUltraOrder = async ({
  inputMint,
  outputMint,
  amount,
  taker,
  slippageBps = 50,
  referralAccount,
  referralFee,
}: {
  inputMint: string;
  outputMint: string;
  amount: number;
  taker: string;
  slippageBps?: number;
  referralAccount?: string;
  referralFee?: number;
}): Promise<{ transaction: string; requestId: string; error?: string }> => {
  const url = new URL(`${JUPITER_ULTRA_API_BASE_URL}/order`);
  url.searchParams.append('inputMint', inputMint);
  url.searchParams.append('outputMint', outputMint);
  url.searchParams.append('amount', amount.toString());
  url.searchParams.append('taker', taker);
  url.searchParams.append('slippageBps', slippageBps.toString());
  if (referralAccount) url.searchParams.append('referralAccount', referralAccount);
  if (referralFee) url.searchParams.append('referralFee', referralFee.toString());

  const response = await fetch(url.toString());
  if (!response.ok) {
    throw new Error(`Ultra API error: ${response.statusText}`);
  }

  return await response.json();
};
