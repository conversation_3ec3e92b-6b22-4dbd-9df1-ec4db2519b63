/**
 * IndexedDB utility functions for caching API responses
 */

// IndexedDB configuration
const DB_NAME = 'jupiterOrdersCache';
const DB_VERSION = 1;
const STORE_NAME = 'orders';
const CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes in milliseconds

/**
 * Initialize the IndexedDB database
 */
export const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    if (!('indexedDB' in window)) {
      reject(new Error('IndexedDB is not supported in this browser'));
      return;
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => {
      reject(new Error('Error opening IndexedDB'));
    };

    request.onsuccess = () => {
      resolve(request.result);
    };

    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
};

/**
 * Get cached data from IndexedDB
 * @param key Cache key
 * @param type Data type identifier
 */
export const getCachedData = async <T>(key: string, type: string): Promise<T | null> => {
  try {
    if (typeof window === 'undefined') return null; // Skip on server-side

    const db = await initDB();
    const cacheKey = `${type}-${key}`;

    return new Promise((resolve) => {
      const transaction = db.transaction(STORE_NAME, 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.get(cacheKey);

      request.onsuccess = () => {
        const result = request.result;
        if (!result) {
          resolve(null);
          return;
        }

        // Check if cache is expired
        const now = Date.now();
        if (now - result.timestamp > CACHE_EXPIRY) {
          resolve(null);
          return;
        }

        resolve(result.data as T);
      };

      request.onerror = () => {
        console.error('Error reading from cache');
        resolve(null);
      };
    });
  } catch (error) {
    console.error('Error accessing IndexedDB:', error);
    return null;
  }
};

/**
 * Store data in IndexedDB
 * @param key Cache key
 * @param type Data type identifier
 * @param data Data to cache
 */
export const cacheData = async <T>(key: string, type: string, data: T): Promise<void> => {
  try {
    if (typeof window === 'undefined') return; // Skip on server-side

    const db = await initDB();
    const cacheKey = `${type}-${key}`;

    const transaction = db.transaction(STORE_NAME, 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    store.put({
      id: cacheKey,
      data,
      timestamp: Date.now(),
    });

    transaction.oncomplete = () => {
      process.env.NEXT_PUBLIC_VERCEL_ENV && console.log(`Cached ${type} data for ${key}`);
    };

    transaction.onerror = () => {
      console.error(`Error caching ${type} data`);
    };
  } catch (error) {
    console.error('Error writing to IndexedDB:', error);
  }
};

/**
 * Get the cache key for a specific page of Jupiter orders
 */
export const getPageCacheKey = (address: string, orderStatus: string, page: number) => {
  return `jupiter-orders-${orderStatus}-${address}-page-${page}`;
};

/**
 * Get the cache key for Jupiter orders metadata
 */
export const getMetadataCacheKey = (address: string, orderStatus: string) => {
  return `jupiter-orders-${orderStatus}-${address}-metadata`;
};
