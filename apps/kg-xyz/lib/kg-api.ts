import type { SignalResponse } from '@/types/kg-api';

export async function getPriceByAddress(contractAddresses: Pick<SignalResponse, 'token_address'>[] | undefined) {
  return fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v2/asset/prices_by_contract`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      tokens: contractAddresses?.map((item) => ({
        chain_id: 'sol',
        contract_address: item.token_address,
      })),
    }),
  })
    .then((x) => x.json())
    .then((x) => x.data as { [address: string]: number });
}

/**
 * Get token prices by trying Phantom API first, then falling back to prices_by_contract
 * @param tokenAddresses Array of token addresses
 * @returns A map of token prices with keys in the format 'sol:contract_address'
 */
export async function getTokenPrices(tokenAddresses: string[]): Promise<{ [key: string]: number }> {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return {};
  }

  // Initialize result with all token addresses set to 0.0
  const result: { [key: string]: number } = {};
  tokenAddresses.forEach((address) => {
    result[`sol:${address}`] = 0.0;
  });

  try {
    const contractAddresses = tokenAddresses.map((address) => ({ token_address: address }));
    const pricesByContract = await getPriceByAddress(contractAddresses);

    // Update result with prices from prices_by_contract
    Object.entries(pricesByContract || {}).forEach(([address, price]) => {
      result[address] = price;
    });

    return result;
  } catch (fallbackError) {
    console.error('Error fetching prices from prices_by_contract', fallbackError);
    return result; // Return the result with all addresses set to 0.0
  }
}
