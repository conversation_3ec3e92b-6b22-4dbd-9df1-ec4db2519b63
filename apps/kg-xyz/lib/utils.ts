import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

import type { OKXTransaction } from '@/types/okx';
import type { Connection } from '@solana/web3.js';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function calAveragePositionCost(transactions: OKXTransaction[] | undefined) {
  if (!transactions || transactions.length === 0) {
    return 0;
  }

  let currentPosition = 0;
  let totalCost = 0;

  // Process transactions in chronological order
  transactions?.toReversed().forEach((tx) => {
    const amount = parseFloat(tx.amount);
    const price = parseFloat(tx.price);

    if (tx.type === 1) {
      // Buy
      totalCost += amount * price;
      currentPosition += amount;
    } else if (tx.type === 2) {
      const amountToReduce = Math.min(amount, currentPosition);
      if (amountToReduce > 0) {
        // Sell
        // Calculate the portion of cost being reduced
        const costReduction = (amountToReduce / currentPosition) * totalCost;
        totalCost -= costReduction;
        currentPosition -= amountToReduce;
      }
    }
  });

  // Calculate average position cost
  return currentPosition > 0 ? totalCost / currentPosition : 0;
}

export const calculateRealizedPnl = (transactions: OKXTransaction[] | undefined) => {
  if (!transactions || transactions.length === 0) {
    return 0;
  }

  let totalPnl = 0;
  let currentPosition = 0;
  let totalCost = 0;

  // Process transactions in chronological order
  transactions?.toReversed().forEach((tx) => {
    const amount = parseFloat(tx.amount);
    const price = parseFloat(tx.price);

    if (tx.type === 1) {
      // Buy
      totalCost += amount * price;
      currentPosition += amount;
    } else if (tx.type === 2) {
      // Sell
      if (currentPosition <= 0) {
        // If we're selling with no position, no realized PnL to calculate
        currentPosition -= amount; // Track negative position
        return;
      }

      // Calculate how much we can actually sell from our current position
      const amountToSell = Math.min(amount, currentPosition);

      // Calculate realized profit/loss for this sell transaction
      const avgCostAtSell = currentPosition > 0 ? totalCost / currentPosition : 0;
      const realizedPnl = amountToSell * (price - avgCostAtSell);
      totalPnl += realizedPnl;

      // Update position and cost
      const costReduction = (amountToSell / currentPosition) * totalCost;
      totalCost -= costReduction;

      currentPosition -= amount; // This might go negative if selling more than current position
    }
  });

  return totalPnl;
};

// Pure function to calculate token value (price * balance)
export const calculateTokenValue = (
  token: { tokenContractAddress: string; tokenPrice?: string; balance?: string },
  pricesMap?: Record<string, number>,
) => {
  const priceFromContext = pricesMap?.[`sol:${token.tokenContractAddress}`] ?? 0;
  const price = priceFromContext !== 0 ? priceFromContext : parseFloat(token.tokenPrice || '0');
  const bal = parseFloat(token.balance || '0');
  return price * bal;
};

export const isOKXDAppBrowser = () => /OKApp/i.test(navigator.userAgent);

export const isPhantomDAppBrowser = () => /Phantom/i.test(navigator.userAgent);

export const isBitgetDAppBrowser = () => /BitKeep/i.test(navigator.userAgent);

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const retryPromise =
  <T, R>(fn: (x: T) => Promise<R>, retries: number, delay: number = 0, attempt = 0): typeof fn =>
  async (x: T) =>
    attempt < retries - 1 ? sleep(delay).then(() => fn(x).catch(retryPromise(fn, retries, delay, attempt + 1))) : fn(x);

// Check if transaction is confirmed on-chain - pure function
export const checkTxConfirmation = async (
  hash: string,
  connection: Connection,
  options: {
    maxAttempts?: number;
    sleepTime?: number;
  } = {},
) => {
  if (!hash) return { status: 'no_hash' as const };

  let attempts = 0;
  const maxAttempts = options.maxAttempts || 30; // Try for about 30 seconds (30 attempts * 1 second)
  const sleepTime = options.sleepTime || 500;

  while (attempts < maxAttempts) {
    try {
      const status = await connection.getSignatureStatus(hash);
      // If transaction failed
      if (status.value?.err) {
        return { status: 'failed' as const, error: status.value.err };
      }

      // Check if transaction is confirmed or finalized
      if (status.value?.confirmationStatus === 'confirmed' || status.value?.confirmationStatus === 'finalized') {
        return { status: 'confirmed' as const, data: status.value };
      }
    } catch (error) {
      console.error('Error checking transaction status:', error);
    }

    // Wait before checking again
    await sleep(sleepTime);
    attempts++;
  }

  // If we've reached max attempts without confirmation
  return { status: 'timeout' as const };
};

export const uniqueByKey = <T>(arr: T[], key: keyof T): T[] =>
  Array.from(arr.reduce((map, item) => map.set(item[key], item), new Map<any, T>()).values());

export const uniqueWith = <T>(arr: T[], comparator: (a: T, b: T) => boolean): T[] =>
  arr.reduce<T[]>((acc, item) => (acc.some((existing) => comparator(existing, item)) ? acc : acc.concat(item)), []);

export const partition = <T>(arr: T[], predicate: (item: T) => boolean): [T[], T[]] =>
  arr.reduce<[T[], T[]]>(
    ([pass, fail], item) => (predicate(item) ? [[...pass, item], fail] : [pass, [...fail, item]]),
    [[], []],
  );

export const sum = (acc: number, curr: number) => acc + curr;

export const assoc =
  <T extends object, const K extends string, R>(key: K, value: R) =>
  (obj: T) =>
    ({
      ...obj,
      [key]: value,
    }) as T & Record<K, R>;
