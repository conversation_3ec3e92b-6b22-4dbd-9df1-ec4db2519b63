import React from 'react';

import { TRADING_CONFIG } from '@/config/trading';
import { notifyTransactionWatch } from '@/lib/notify-transaction';
import { jupiterQuoteApi, processTransactionData, addSolTransferToTransactionV2 } from '@/lib/sign-transaction';
import { assoc, checkTxConfirmation, retryPromise } from '@/lib/utils';
import { sendGTMEvent } from '@next/third-parties/google';
import * as Sentry from '@sentry/nextjs';
import { type Connection, PublicKey, type VersionedTransaction } from '@solana/web3.js';

type QuoteResponse = any; // Define proper type if available from Jupiter API

export interface JupiterSwapParams {
  // Required parameters
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps: number;
  publicKey: string | null | undefined;
  connection: Connection;
  sendTransaction: (transaction: VersionedTransaction, connection: Connection) => Promise<string | undefined>;

  // Fee configuration
  isSellMode: boolean;
  baseToken?: 'SOL' | 'USDC';

  // Callbacks and event tracking
  onTransactionSubmitted?: (txHash: string) => void;
  onTransactionConfirmed?: (txHash: string) => void;
  onTransactionFailed?: (error: any) => void;

  // Event tracking data
  tokenAddress: string;
  amount_display: string | number;
  usd_value: string | number;

  // Toast functions
  toast: (props: { title: string; description: string | React.ReactNode; variant?: 'default' | 'destructive' }) => void;
  t: (key: string) => string;
}

export async function executeJupiterSwap({
  inputMint,
  outputMint,
  amount,
  slippageBps,
  publicKey,
  connection,
  sendTransaction,
  isSellMode,
  onTransactionSubmitted,
  onTransactionConfirmed,
  onTransactionFailed,
  tokenAddress,
  amount_display,
  usd_value,
  toast,
  t,
}: JupiterSwapParams) {
  try {
    // Get quote from Jupiter API
    const swapResponse = await jupiterQuoteApi
      .quoteGet({
        inputMint,
        outputMint,
        amount,
        slippageBps,
      })
      .then(
        retryPromise(
          (x: QuoteResponse) =>
            jupiterQuoteApi
              .swapPost({
                swapRequest: {
                  quoteResponse: x,
                  dynamicSlippage: false,
                  dynamicComputeUnitLimit: true,
                  userPublicKey: publicKey?.toString() || '',
                  prioritizationFeeLamports: {
                    priorityLevelWithMaxLamports: {
                      priorityLevel: 'veryHigh',
                      maxLamports: 40_000_000,
                    },
                  },
                },
              })
              .then(assoc('quoteResponse', x)),
          3,
          200,
        ),
      );

    // Process transaction data
    const unSignedTransaction: VersionedTransaction | null = processTransactionData(swapResponse.swapTransaction);

    if (!unSignedTransaction) {
      Sentry.captureException('Jupiter Swap Error');
      toast({
        variant: 'destructive',
        title: 'Request Error',
        description: 'Jupiter Swap Error',
      });
      return;
    }

    // Add fee transfer if configured
    let finalTransaction = unSignedTransaction;
    try {
      const commissionTransferAmount = Math.round(
        (isSellMode ? +swapResponse.quoteResponse.outAmount : +swapResponse.quoteResponse.inAmount) *
          (TRADING_CONFIG.platformFeeBps / 10_000),
      );

      const recipientPubkey = new PublicKey(TRADING_CONFIG.commissionWallet);
      const senderPubkey = new PublicKey(publicKey!.toString());

      finalTransaction = await addSolTransferToTransactionV2(
        unSignedTransaction,
        senderPubkey,
        recipientPubkey,
        commissionTransferAmount,
        connection,
      );
    } catch (transferError) {
      const error = transferError as Error;
      console.error('Failed to add transfer:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      Sentry.captureException(error);
      // Continue with original transaction
    }

    // Send transaction
    const txHash = await sendTransaction(finalTransaction, connection);
    if (!txHash) return;

    // Notify backend about the transaction
    notifyTransactionWatch(txHash);

    // Call the transaction submitted callback if provided
    if (onTransactionSubmitted) {
      onTransactionSubmitted(txHash);
    }

    // Show toast notification
    toast({
      title: t('tx-submitted'),
      description: React.createElement(
        'a',
        {
          href: `https://solscan.io/tx/${txHash}`,
          target: '_blank',
          rel: 'noopener noreferrer',
        },
        [
          t('tx-hash'),
          ' ',
          React.createElement('span', { className: 'underline' }, `${txHash?.slice(0, 8)}...${txHash?.slice(-7)}`),
        ],
      ),
    });

    // Check transaction confirmation
    const checkResult = async () => {
      const txResult = await checkTxConfirmation(txHash, connection);

      // Handle the result based on status
      if (txResult.status === 'confirmed') {
        // Send GTM event for successful transaction
        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}_success`,
          token_address: tokenAddress,
          amount: amount_display,
          usd_volume: typeof usd_value === 'number' ? usd_value.toFixed(2) : usd_value,
          walletAddress: publicKey,
          platform: 'web',
        });

        // Show confirmation toast
        toast({
          title: t('tx-confirmed'),
          description: React.createElement(
            'a',
            {
              href: `https://solscan.io/tx/${txHash}`,
              target: '_blank',
              rel: 'noopener noreferrer',
            },
            [
              t('tx-hash'),
              ' ',
              React.createElement('span', { className: 'underline' }, `${txHash?.slice(0, 8)}...${txHash?.slice(-7)}`),
              React.createElement('br'),
              t('confirmed-message'),
            ],
          ),
        });

        // Call the transaction confirmed callback if provided
        if (onTransactionConfirmed) {
          onTransactionConfirmed(txHash);
        }
      } else {
        // Send GTM event for failed transaction
        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}_failed`,
          token_address: tokenAddress,
          amount: amount_display,
          usd_volume: typeof usd_value === 'number' ? usd_value.toFixed(2) : usd_value,
          walletAddress: publicKey,
          platform: 'web',
        });

        // Show error toast
        toast({
          variant: 'destructive',
          title: t('tx-failed'),
          description: t('failed-message'),
        });

        Sentry.captureException(`Transaction failed: ${JSON.stringify(txResult.error)}`);

        // Call the transaction failed callback if provided
        if (onTransactionFailed) {
          onTransactionFailed(txResult.error);
        }
      }
    };

    // Execute the check result function
    checkResult().catch((error) => {
      console.error('Error checking transaction result:', error);
      Sentry.captureException(error);
    });

    return txHash;
  } catch (error) {
    console.error('Jupiter swap error:', error);
    Sentry.captureException(error);

    toast({
      variant: 'destructive',
      title: 'Swap Error',
      description: 'Failed to execute swap transaction',
    });

    if (onTransactionFailed) {
      onTransactionFailed(error);
    }

    return undefined;
  }
}
