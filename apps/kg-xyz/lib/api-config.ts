import cryptoJS from 'crypto-js';

import { type FetchWrapper } from '@kryptogo/2b/swap-widget';

const secretKey = process.env.NEXT_PUBLIC_OKX_SECRET_KEY!;
const apiBaseUrl = 'https://web3.okx.com/api/v5/dex';

export const getHeadersParams = (path: string) => {
  const timestamp = new Date().toISOString();
  return {
    'Content-Type': 'application/json',
    'OK-ACCESS-PROJECT': process.env.NEXT_PUBLIC_OKX_ACCESS_PROJECT!,
    'OK-ACCESS-KEY': process.env.NEXT_PUBLIC_OKX_ACCESS_KEY!,
    'OK-ACCESS-SIGN': cryptoJS.enc.Base64.stringify(cryptoJS.HmacSHA256(timestamp + 'GET' + path.slice(20), secretKey)),
    'OK-ACCESS-TIMESTAMP': timestamp,
    'OK-ACCESS-PASSPHRASE': process.env.NEXT_PUBLIC_OKX_ACCESS_PASSPHRASE!,
  };
};

export const fetchWrapper = async <T>(url: string, options: RequestInit = {}): FetchWrapper<T> =>
  fetch(url, {
    method: 'get',
    headers: getHeadersParams(url),
    ...options,
  }).then((res) => res.json());

const urlBuilder = (path: `/${string}`) => (methodName: string) => apiBaseUrl + path + methodName;

export const getAssetUrl = urlBuilder('/balance');
