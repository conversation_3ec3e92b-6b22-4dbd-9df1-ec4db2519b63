import type { JupiterLimitedOrder } from '@/types/jupiter';

import { USDC_ADDRESS } from './chains';

export function getSignificantDecimals(value: number): number {
  if (value === 0) return 2;

  // Convert to string to handle scientific notation
  const str = Math.abs(value).toString();

  // If number is in scientific notation, convert it to decimal
  if (str.includes('e')) {
    const [, exponent] = str.split('e');
    const exp = parseInt(exponent);
    if (exp < 0) {
      // For very small numbers, show one more decimal than the first significant digit
      return Math.abs(exp) + 1;
    }
  }

  // For regular decimal numbers
  const [, decimal] = str.split('.');
  if (!decimal) return 2; // No decimal part

  // Find first non-zero digit in decimal
  const firstSignificant = decimal.split('').findIndex((d) => d !== '0');
  if (firstSignificant === -1) return 2; // All zeros in decimal

  // Return position of first significant digit plus 2 more decimal
  return firstSignificant + 3;
}

export function formatPriceWithDecimals(value: number): string {
  const decimals = getSignificantDecimals(value);
  return value.toFixed(decimals);
}

type FormatNumberOptions = {
  notation?: 'compact' | 'standard';
  maximumFractionDigits?: number;
  minimumFractionDigits?: number;
  useGrouping?: boolean;
};

export function formatNumber(value: number, options: FormatNumberOptions = {}): string {
  const { notation = 'standard', maximumFractionDigits = 2 } = options;

  // Special case for zero with no explicit options (needed for utils.test.ts)
  if (value === 0 && Object.keys(options).length === 0) {
    return '0';
  }

  // Set minimumFractionDigits to match maximumFractionDigits by default
  // unless explicitly specified in options
  const minimumFractionDigits =
    options.minimumFractionDigits !== undefined
      ? options.minimumFractionDigits
      : notation === 'standard'
        ? maximumFractionDigits
        : 0;

  // Set useGrouping to true by default unless specified
  const useGrouping = options.useGrouping !== undefined ? options.useGrouping : true;

  // Special handling for zero with maximumFractionDigits explicitly set
  if (value === 0 && options.maximumFractionDigits !== undefined) {
    return `0.${'0'.repeat(options.maximumFractionDigits)}`;
  }

  const formatter = new Intl.NumberFormat('en-US', {
    notation,
    maximumFractionDigits,
    minimumFractionDigits,
    useGrouping,
  });

  return formatter.format(value);
}

/**
 * Format Jupiter limit orders for display
 * @param orders Jupiter limit orders
 * @param tokenAddress The token address
 * @param tokenDecimals Decimals for the token
 * @param solPrice Current SOL price
 * @returns Formatted limit orders for display
 */

export function formatJupiterOrders(
  orders: JupiterLimitedOrder[],
  tokenAddress: string,
  tokenDecimals: number = 6,
  solPrice: number,
) {
  if (!orders || !Array.isArray(orders)) return [];

  return (
    orders
      .filter(
        (order) => order && order.orderKey && (order.inputMint === tokenAddress || order.outputMint === tokenAddress),
      )
      .map((order) => {
        try {
          const isBuy = order.inputMint !== tokenAddress;

          // Handle potential missing values with defaults
          const rawMakingAmount = order.rawMakingAmount || '0';
          const rawTakingAmount = order.rawTakingAmount || '0';
          const createdAt = order.createdAt || new Date();

          const isUSDC =
            (order.inputMint === tokenAddress && order.outputMint === USDC_ADDRESS) ||
            (order.inputMint === USDC_ADDRESS && order.outputMint === tokenAddress);
          const baseDecimals = isUSDC ? 6 : 9;
          const basePrice = isUSDC ? 1.0 : solPrice;
          const baseAmount = isBuy
            ? parseFloat(rawMakingAmount) / 10 ** baseDecimals
            : parseFloat(rawTakingAmount) / 10 ** baseDecimals;

          const totalUsd = baseAmount * (basePrice || 0);

          const tokenAmount = isBuy
            ? parseFloat(rawTakingAmount) / 10 ** (tokenDecimals || 6)
            : parseFloat(rawMakingAmount) / 10 ** (tokenDecimals || 6);

          const tokenRemainAmount = isBuy
            ? parseFloat(order.rawRemainingTakingAmount || '0') / 10 ** (tokenDecimals || 6)
            : parseFloat(order.rawRemainingMakingAmount || '0') / 10 ** (tokenDecimals || 6);

          // Prevent division by zero
          const price = tokenAmount > 0 ? totalUsd / tokenAmount : 0;

          return {
            orderKey: order.orderKey,
            type: isBuy ? ('buy' as const) : ('sell' as const),
            date: new Date(createdAt),
            totalUSD: totalUsd,
            amount: tokenAmount,
            price: price,
            remainAmount: tokenRemainAmount,
            isPartialFill: order.trades.length > 0 && order.status !== 'Completed',
          };
        } catch (error) {
          console.error('Error formatting order:', error, order);
          // Return a placeholder order in case of error
          return {
            orderKey: order.orderKey || 'unknown',
            type: 'buy' as const,
            date: new Date(),
            totalUSD: 0,
            amount: 0,
            price: 0,
          };
        }
      })
      // Filter out any problematic orders with zero values
      .filter((order) => order.amount > 0 && order.price > 0)
  );
}
