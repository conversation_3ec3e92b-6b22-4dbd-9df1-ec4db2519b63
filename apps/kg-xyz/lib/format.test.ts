import { describe, it, expect } from 'vitest';

import { getSignificantDecimals, formatPriceWithDecimals, formatNumber } from './format';

describe('getSignificantDecimals', () => {
  it('should return 2 for zero', () => {
    expect(getSignificantDecimals(0)).toBe(2);
  });

  it('should handle regular decimal numbers', () => {
    expect(getSignificantDecimals(1.23)).toBe(3);
    expect(getSignificantDecimals(1.234)).toBe(3);
  });

  it('should handle small decimal numbers', () => {
    expect(getSignificantDecimals(0.001)).toBe(5);
    expect(getSignificantDecimals(0.00123)).toBe(5);
  });

  it('should handle scientific notation', () => {
    expect(getSignificantDecimals(1e-6)).toBe(8);
    expect(getSignificantDecimals(1.23e-4)).toBe(6);
  });

  it('should handle whole numbers', () => {
    expect(getSignificantDecimals(100)).toBe(2);
    expect(getSignificantDecimals(1000)).toBe(2);
  });
});

describe('formatPriceWithDecimals', () => {
  it('should format regular numbers', () => {
    expect(formatPriceWithDecimals(1.23)).toBe('1.230');
    expect(formatPriceWithDecimals(100)).toBe('100.00');
  });

  it('should format small numbers', () => {
    expect(formatPriceWithDecimals(0.001)).toBe('0.00100');
    expect(formatPriceWithDecimals(0.00123)).toBe('0.00123');
  });

  it('should format numbers in scientific notation', () => {
    expect(formatPriceWithDecimals(1e-6)).toBe('0.00000100');
    expect(formatPriceWithDecimals(1.23e-4)).toBe('0.000123');
  });
});

describe('formatNumber', () => {
  it('should format with standard notation by default', () => {
    expect(formatNumber(1234.5678)).toBe('1,234.57');
    expect(formatNumber(1000)).toBe('1,000.00');
  });

  it('should respect maximumFractionDigits option', () => {
    expect(formatNumber(1234.5678, { maximumFractionDigits: 3 })).toBe('1,234.568');
    expect(formatNumber(1000, { maximumFractionDigits: 0 })).toBe('1,000');
  });

  it('should respect minimumFractionDigits option', () => {
    expect(formatNumber(1.2, { notation: 'compact', minimumFractionDigits: 0 })).toBe('1.2');
    expect(formatNumber(100, { notation: 'compact', minimumFractionDigits: 1 })).toBe('100.0');
    expect(formatNumber(0.5, { notation: 'compact', minimumFractionDigits: 2 })).toBe('0.50');
  });

  it('should format with compact notation when specified', () => {
    expect(formatNumber(1234567, { notation: 'compact' })).toBe('1.23M');
    expect(formatNumber(1234567890, { notation: 'compact' })).toBe('1.23B');
  });

  it('should handle compact notation with different maximumFractionDigits', () => {
    expect(formatNumber(1234567, { notation: 'compact', maximumFractionDigits: 1 })).toBe('1.2M');
    expect(formatNumber(1234567, { notation: 'compact', maximumFractionDigits: 0 })).toBe('1M');
  });
});
