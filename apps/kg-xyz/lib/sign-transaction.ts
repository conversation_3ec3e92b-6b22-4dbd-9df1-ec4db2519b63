import { createJupiterApiClient } from '@jup-ag/api';
import {
  TOKEN_PROGRAM_ID,
  createTransferInstruction,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  getAccount,
  TokenAccountNotFoundError,
  ASSOCIATED_TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import {
  VersionedTransaction,
  SystemProgram,
  PublicKey,
  TransactionMessage,
  AddressLookupTableAccount,
  TransactionInstruction,
  Connection,
  MessageV0,
} from '@solana/web3.js';

import { USDC_ADDRESS } from './chains';

export const processTransactionData = (transactionBase64: string) => {
  const buffer = Buffer.from(transactionBase64, 'base64');
  const versionedTransaction = VersionedTransaction.deserialize(buffer);
  return versionedTransaction;
};

/**
 * Common helper function to rebuild a transaction with additional instructions
 * @param versionedTransaction The original transaction
 * @param additionalInstructions Instructions to add to the transaction
 * @param payerKey The payer public key
 * @param connection Solana connection for getting fresh blockhash
 * @returns New VersionedTransaction with additional instructions
 */
const rebuildTransactionWithInstructions = async (
  versionedTransaction: VersionedTransaction,
  additionalInstructions: TransactionInstruction[],
  payerKey: PublicKey,
  connection: Connection,
): Promise<VersionedTransaction> => {
  const message = versionedTransaction.message as MessageV0;

  // Step 1: Fetch Address Lookup Table accounts if they exist
  const addressLookupTableAccounts: AddressLookupTableAccount[] = [];

  if (message.addressTableLookups && message.addressTableLookups.length > 0) {
    console.log('Found ALT lookups:', message.addressTableLookups.length);

    for (const lookup of message.addressTableLookups) {
      try {
        const accountInfo = await connection.getAccountInfo(lookup.accountKey);
        if (accountInfo) {
          const addressLookupTableAccount = new AddressLookupTableAccount({
            key: lookup.accountKey,
            state: AddressLookupTableAccount.deserialize(accountInfo.data),
          });
          addressLookupTableAccounts.push(addressLookupTableAccount);
          console.log(
            `Fetched ALT ${lookup.accountKey.toBase58()} with ${addressLookupTableAccount.state.addresses.length} addresses`,
          );
        }
      } catch (error) {
        console.error(`Failed to fetch ALT account ${lookup.accountKey.toBase58()}:`, error);
        throw error;
      }
    }
  }

  // Step 2: Get all account keys (static + ALT accounts)
  const allAccountKeys = message.getAccountKeys({ addressLookupTableAccounts });
  console.log('Total account keys:', allAccountKeys.length);
  console.log('Static account keys:', message.staticAccountKeys.length);

  // Step 3: Decompile instructions with proper account resolution
  const instructions: TransactionInstruction[] = [];

  for (let i = 0; i < message.compiledInstructions.length; i++) {
    const compiledIx = message.compiledInstructions[i];

    // Get program ID
    const programId = allAccountKeys.get(compiledIx.programIdIndex);
    if (!programId) {
      throw new Error(`Program ID not found at index ${compiledIx.programIdIndex}`);
    }

    // Build account metas
    const keys = compiledIx.accountKeyIndexes.map((keyIndex: number, idx: number) => {
      const pubkey = allAccountKeys.get(keyIndex);
      if (!pubkey) {
        console.error(`Account key not found at index ${keyIndex} (instruction ${i}, account ${idx})`);
        console.error(`Max valid index: ${allAccountKeys.length - 1}`);
        throw new Error(`Account key not found at index ${keyIndex}`);
      }

      // Use Solana's built-in methods to determine signer/writable status
      const isSigner = message.isAccountSigner(keyIndex);
      const isWritable = message.isAccountWritable(keyIndex);

      return {
        pubkey,
        isSigner,
        isWritable,
      };
    });

    instructions.push(
      new TransactionInstruction({
        programId,
        keys,
        data: Buffer.from(compiledIx.data),
      }),
    );
  }

  // Step 4: Add the additional instructions
  instructions.push(...additionalInstructions);

  // Step 5: Get fresh blockhash
  const { blockhash } = await connection.getLatestBlockhash();

  // Step 6: Create new transaction message
  const newMessage = new TransactionMessage({
    payerKey,
    recentBlockhash: blockhash,
    instructions,
  });

  // Step 7: Compile to V0 message with ALTs
  const compiledMessage =
    addressLookupTableAccounts.length > 0
      ? newMessage.compileToV0Message(addressLookupTableAccounts)
      : newMessage.compileToV0Message();

  return new VersionedTransaction(compiledMessage);
};

/**
 * Add a SOL transfer instruction to an existing VersionedTransaction (with proper ALT handling)
 * @param versionedTransaction The original transaction
 * @param fromPubkey Sender's public key
 * @param toPubkey Recipient's public key
 * @param solAmount Amount in SOL (e.g., 0.1 for 0.1 SOL)
 * @param connection Solana connection for getting fresh blockhash
 * @returns New VersionedTransaction with SOL transfer added
 */
export const addSolTransferToTransactionV2 = async (
  versionedTransaction: VersionedTransaction,
  fromPubkey: PublicKey,
  toPubkey: PublicKey,
  lamports: number,
  connection: Connection,
): Promise<VersionedTransaction> => {
  try {
    // Validate inputs
    if (!fromPubkey || !toPubkey) {
      throw new Error('fromPubkey and toPubkey are required');
    }

    if (lamports <= 0) {
      throw new Error('lamports must be greater than 0');
    }

    // Create the SOL transfer instruction
    const transferInstruction = SystemProgram.transfer({
      fromPubkey,
      toPubkey,
      lamports,
    });

    console.log('Adding SOL transfer instruction');

    // Use the common helper to rebuild the transaction
    const result = await rebuildTransactionWithInstructions(
      versionedTransaction,
      [transferInstruction],
      fromPubkey,
      connection,
    );

    console.log('Transaction rebuilt with SOL transfer successfully');
    return result;
  } catch (error) {
    console.error('Error in addSolTransferToTransactionV2:', error);
    throw error;
  }
};

/**
 * Add a USDC (or any SPL token) transfer instruction to an existing VersionedTransaction (with proper ALT handling)
 * @param versionedTransaction The original transaction
 * @param fromPubkey Sender's public key
 * @param toPubkey Recipient's public key
 * @param amount Amount in token units (e.g., for USDC with 6 decimals, 1000000 = 1 USDC)
 * @param tokenMint The mint address of the token (USDC mint for USDC transfers)
 * @param connection Solana connection for getting fresh blockhash and checking accounts
 * @param decimals Token decimals (optional, defaults to 6 for USDC)
 * @returns New VersionedTransaction with token transfer added
 */
export const addTokenTransferToTransactionV2 = async (
  versionedTransaction: VersionedTransaction,
  fromPubkey: PublicKey,
  toPubkey: PublicKey,
  amount: number,
  tokenMint: PublicKey,
  connection: Connection,
  decimals: number = 6,
): Promise<VersionedTransaction> => {
  try {
    // Validate inputs
    if (!fromPubkey || !toPubkey) {
      throw new Error('fromPubkey and toPubkey are required');
    }

    if (amount <= 0) {
      throw new Error('amount must be greater than 0');
    }

    const instructions: TransactionInstruction[] = [];

    // Get associated token accounts
    const fromTokenAccount = await getAssociatedTokenAddress(tokenMint, fromPubkey, false, TOKEN_PROGRAM_ID);
    const toTokenAccount = await getAssociatedTokenAddress(tokenMint, toPubkey, false, TOKEN_PROGRAM_ID);

    // Check if recipient's token account exists
    let recipientAccountExists = true;
    try {
      await getAccount(connection, toTokenAccount);
    } catch (error) {
      if (error instanceof TokenAccountNotFoundError) {
        recipientAccountExists = false;
        console.log('Recipient token account does not exist, will create it');
      } else {
        throw error;
      }
    }

    // Create associated token account if it doesn't exist
    if (!recipientAccountExists) {
      const createATAInstruction = createAssociatedTokenAccountInstruction(
        fromPubkey, // payer
        toTokenAccount, // associated token account
        toPubkey, // owner
        tokenMint, // mint
        TOKEN_PROGRAM_ID,
        ASSOCIATED_TOKEN_PROGRAM_ID,
      );
      instructions.push(createATAInstruction);
      console.log('Added create associated token account instruction');
    }

    // Create the token transfer instruction
    const transferInstruction = createTransferInstruction(
      fromTokenAccount,
      toTokenAccount,
      fromPubkey,
      amount,
      [],
      TOKEN_PROGRAM_ID,
    );
    instructions.push(transferInstruction);
    console.log(`Adding token transfer instruction for ${amount} units`);

    // Use the common helper to rebuild the transaction
    const result = await rebuildTransactionWithInstructions(versionedTransaction, instructions, fromPubkey, connection);

    console.log('Transaction rebuilt with token transfer successfully');
    return result;
  } catch (error) {
    console.error('Error in addTokenTransferToTransactionV2:', error);
    throw error;
  }
};

/**
 * Add a USDC transfer instruction to an existing VersionedTransaction
 * This is a convenience wrapper around addTokenTransferToTransactionV2 specifically for USDC
 * @param versionedTransaction The original transaction
 * @param fromPubkey Sender's public key
 * @param toPubkey Recipient's public key
 * @param usdcAmount Raw amount in USDC (e.g., 1500000 for 1.5 USDC)
 * @param connection Solana connection
 * @param usdcMint USDC mint address (defaults to mainnet USDC)
 * @returns New VersionedTransaction with USDC transfer added
 */
export const addUsdcTransferToTransactionV2 = async (
  versionedTransaction: VersionedTransaction,
  fromPubkey: PublicKey,
  toPubkey: PublicKey,
  usdcAmount: number,
  connection: Connection,
  usdcMint: PublicKey = new PublicKey(USDC_ADDRESS), // Mainnet USDC
): Promise<VersionedTransaction> => {
  return addTokenTransferToTransactionV2(
    versionedTransaction,
    fromPubkey,
    toPubkey,
    usdcAmount,
    usdcMint,
    connection,
    6, // USDC has 6 decimals
  );
};

export const jupiterQuoteApi = createJupiterApiClient({
  basePath: '/api/jupiter',
});

/**
 * Rebuild a VersionedTransaction with proper ALT handling
 * This function decompiles and recompiles the transaction, properly resolving all account keys
 * @param versionedTransaction The original transaction
 * @param connection Solana connection for fetching ALT accounts
 * @returns New VersionedTransaction with same instructions but fresh blockhash
 */
export const rebuildVersionedTransaction = async (
  versionedTransaction: VersionedTransaction,
  connection: Connection,
): Promise<VersionedTransaction> => {
  try {
    const message = versionedTransaction.message as MessageV0;

    // Step 1: Fetch Address Lookup Table accounts if they exist
    const addressLookupTableAccounts: AddressLookupTableAccount[] = [];

    if (message.addressTableLookups && message.addressTableLookups.length > 0) {
      console.log('Found ALT lookups:', message.addressTableLookups.length);

      for (const lookup of message.addressTableLookups) {
        try {
          const accountInfo = await connection.getAccountInfo(lookup.accountKey);
          if (accountInfo) {
            const addressLookupTableAccount = new AddressLookupTableAccount({
              key: lookup.accountKey,
              state: AddressLookupTableAccount.deserialize(accountInfo.data),
            });
            addressLookupTableAccounts.push(addressLookupTableAccount);
            console.log(
              `Fetched ALT ${lookup.accountKey.toBase58()} with ${addressLookupTableAccount.state.addresses.length} addresses`,
            );
          }
        } catch (error) {
          console.error(`Failed to fetch ALT account ${lookup.accountKey.toBase58()}:`, error);
          throw error;
        }
      }
    }

    // Step 2: Get all account keys (static + ALT accounts)
    const allAccountKeys = message.getAccountKeys({ addressLookupTableAccounts });
    console.log('Total account keys:', allAccountKeys.length);
    console.log('Static account keys:', message.staticAccountKeys.length);

    // Step 3: Decompile instructions with proper account resolution
    const instructions: TransactionInstruction[] = [];

    for (let i = 0; i < message.compiledInstructions.length; i++) {
      const compiledIx = message.compiledInstructions[i];

      // Get program ID
      const programId = allAccountKeys.get(compiledIx.programIdIndex);
      if (!programId) {
        throw new Error(`Program ID not found at index ${compiledIx.programIdIndex}`);
      }

      console.log(
        `Instruction ${i}: Program ${programId.toBase58()}, accounts: ${compiledIx.accountKeyIndexes.length}`,
      );
      // Build account metas
      const keys = compiledIx.accountKeyIndexes.map((keyIndex: number, idx: number) => {
        const pubkey = allAccountKeys.get(keyIndex);
        if (!pubkey) {
          console.error(`Account key not found at index ${keyIndex} (instruction ${i}, account ${idx})`);
          console.error(`Max valid index: ${allAccountKeys.length - 1}`);
          throw new Error(`Account key not found at index ${keyIndex}`);
        }

        // Use Solana's built-in methods to determine signer/writable status
        const isSigner = message.isAccountSigner(keyIndex);
        const isWritable = message.isAccountWritable(keyIndex);

        console.log(
          `  Account ${idx}: ${pubkey.toBase58().slice(0, 8)}... (index: ${keyIndex}, signer: ${isSigner}, writable: ${isWritable})`,
        );

        return {
          pubkey,
          isSigner,
          isWritable,
        };
      });

      instructions.push(
        new TransactionInstruction({
          programId,
          keys,
          data: Buffer.from(compiledIx.data),
        }),
      );
    }

    // Step 4: Get fresh blockhash
    const { blockhash } = await connection.getLatestBlockhash();

    // Step 5: Create new transaction message
    const payerKey = message.staticAccountKeys[0]; // First account is always the payer
    const newMessage = new TransactionMessage({
      payerKey,
      recentBlockhash: blockhash,
      instructions,
    });

    // Step 6: Compile to V0 message with ALTs
    const compiledMessage =
      addressLookupTableAccounts.length > 0
        ? newMessage.compileToV0Message(addressLookupTableAccounts)
        : newMessage.compileToV0Message();

    console.log('Rebuilt transaction successfully');
    return new VersionedTransaction(compiledMessage);
  } catch (error) {
    console.error('Error in rebuildVersionedTransaction:', error);
    throw error;
  }
};

/**
 * Test function to analyze a transaction structure
 * @param transactionBase64 The transaction in base64 format
 * @param connection Solana connection
 */
export const analyzeTransaction = async (transactionBase64: string, connection: Connection): Promise<void> => {
  try {
    const versionedTransaction = processTransactionData(transactionBase64);
    const message = versionedTransaction.message as MessageV0;

    console.log('=== Transaction Analysis ===');
    console.log('Version:', versionedTransaction.version);
    console.log('Static Account Keys:', message.staticAccountKeys.length);
    console.log('Address Table Lookups:', message.addressTableLookups?.length || 0);
    console.log('Instructions:', message.compiledInstructions.length);

    // Check for ALT usage
    if (message.addressTableLookups && message.addressTableLookups.length > 0) {
      console.log('\n=== ALT Details ===');
      for (const lookup of message.addressTableLookups) {
        console.log(`ALT Key: ${lookup.accountKey.toBase58()}`);
        console.log(`  Writable Indexes: ${lookup.writableIndexes}`);
        console.log(`  Readonly Indexes: ${lookup.readonlyIndexes}`);
      }
    }

    // Check account index ranges
    console.log('\n=== Account Index Analysis ===');
    let maxAccountIndex = 0;
    message.compiledInstructions.forEach((ix, i) => {
      const maxInIx = Math.max(...ix.accountKeyIndexes);
      if (maxInIx > maxAccountIndex) {
        maxAccountIndex = maxInIx;
      }
      console.log(`Instruction ${i}: Max account index = ${maxInIx}`);
    });

    console.log(`\nMax account index used: ${maxAccountIndex}`);
    console.log(`Static account keys available: ${message.staticAccountKeys.length}`);

    if (maxAccountIndex >= message.staticAccountKeys.length) {
      console.log('⚠️  Transaction uses ALT accounts!');
      console.log(`ALT account indexes start at: ${message.staticAccountKeys.length}`);
    }

    // Try to rebuild
    console.log('\n=== Attempting Rebuild ===');
    const rebuilt = await rebuildVersionedTransaction(versionedTransaction, connection);
    console.log('✅ Rebuild successful!', rebuilt);
  } catch (error) {
    console.error('Analysis failed:', error);
    throw error;
  }
};
