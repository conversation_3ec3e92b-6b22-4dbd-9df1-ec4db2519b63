import * as Sentry from '@sentry/nextjs';

/**
 * Notifies the backend about a transaction for watching
 * @param txHash The transaction hash to watch
 * @param chainId The blockchain chain ID
 */
export async function notifyTransactionWatch(txHash: string, chainId: string = 'sol'): Promise<void> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v2/tx_watch/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chain_id: chainId,
        tx_hash: txHash,
      }),
    });

    if (!response.ok) {
      console.error(`Failed to notify transaction watch: ${response.status}`, txHash);
      Sentry.captureException(new Error(`Transaction watch notification failed: ${response.status}`), {
        tags: {
          txHash,
          chainId,
          statusCode: response.status,
        },
      });
    }
  } catch (error) {
    console.error('Error notifying transaction watch:', error);
    Sentry.captureException(error, {
      tags: {
        txHash,
        chainId,
      },
    });
  }
}
