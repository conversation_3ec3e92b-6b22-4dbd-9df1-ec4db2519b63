import { describe, it, expect, vi } from 'vitest';

import { formatNumber } from './format';
import { assoc, calculateTokenValue, retryPromise, uniqueByKey, uniqueWith } from './utils';

describe('formatNumber', () => {
  it('should handle standard notation with default precision', () => {
    expect(formatNumber(1234.5678)).toBe('1,234.57');
    expect(formatNumber(1000)).toBe('1,000.00');
    expect(formatNumber(1.23456)).toBe('1.23');
  });

  it('should handle standard notation with custom precision', () => {
    expect(formatNumber(1234.5678, { maximumFractionDigits: 3 })).toBe('1,234.568');
    expect(formatNumber(1000, { maximumFractionDigits: 1 })).toBe('1,000.0');
    expect(formatNumber(1.23456, { maximumFractionDigits: 4 })).toBe('1.2346');
  });

  it('should handle compact notation', () => {
    expect(formatNumber(1234.5678, { notation: 'compact' })).toBe('1.23K');
    expect(formatNumber(1000000, { notation: 'compact' })).toBe('1M');
    expect(formatNumber(1500000, { notation: 'compact', maximumFractionDigits: 1 })).toBe('1.5M');
  });

  it('should handle zero', () => {
    expect(formatNumber(0)).toBe('0');
    expect(formatNumber(0, { maximumFractionDigits: 4 })).toBe('0.0000');
  });

  it('should handle negative numbers', () => {
    expect(formatNumber(-1234.5678)).toBe('-1,234.57');
    expect(formatNumber(-1234.5678, { notation: 'compact' })).toBe('-1.23K');
  });

  it('should handle small numbers', () => {
    expect(formatNumber(0.123456)).toBe('0.12');
    expect(formatNumber(0.123456, { maximumFractionDigits: 4 })).toBe('0.1235');
  });
});

describe('calculateTokenValue', () => {
  it('should calculate token value using price from pricesMap', () => {
    const token = {
      tokenContractAddress: 'token123',
      tokenPrice: '2.5',
      balance: '10',
    };
    const pricesMap = {
      'sol:token123': 3.0,
    };

    // Should use price from pricesMap (3.0) instead of tokenPrice (2.5)
    expect(calculateTokenValue(token, pricesMap)).toBe(30);
  });

  it('should fall back to tokenPrice when price not available in pricesMap', () => {
    const token = {
      tokenContractAddress: 'token123',
      tokenPrice: '2.5',
      balance: '10',
    };

    // No price in pricesMap, should use tokenPrice (2.5)
    expect(calculateTokenValue(token, {})).toBe(25);
  });

  it('should handle missing tokenPrice when price not in pricesMap', () => {
    const token = {
      tokenContractAddress: 'token123',
      balance: '10',
    };

    // No price in pricesMap and no tokenPrice, should use 0
    expect(calculateTokenValue(token, {})).toBe(0);
  });

  it('should handle missing balance', () => {
    const token = {
      tokenContractAddress: 'token123',
      tokenPrice: '2.5',
    };
    const pricesMap = {
      'sol:token123': 3.0,
    };

    // Has price but no balance, should return 0
    expect(calculateTokenValue(token, pricesMap)).toBe(0);
  });

  it('should handle undefined pricesMap', () => {
    const token = {
      tokenContractAddress: 'token123',
      tokenPrice: '2.5',
      balance: '10',
    };

    // No pricesMap provided, should use tokenPrice (2.5)
    expect(calculateTokenValue(token, undefined)).toBe(25);
  });

  it('should handle zero values correctly', () => {
    const token = {
      tokenContractAddress: 'token123',
      tokenPrice: '0',
      balance: '10',
    };
    const pricesMap = {
      'sol:token123': 0,
    };

    // Both prices are 0, result should be 0
    expect(calculateTokenValue(token, pricesMap)).toBe(0);
  });
});

describe('retryPromise', () => {
  it('should resolve immediately if the promise succeeds on first try', async () => {
    const mockFn = async (x: unknown) => x;

    const result = await retryPromise(mockFn, 3, 100)('success');

    expect(result).toBe('success');
  });

  it('should retry and then succeed', async () => {
    const mockFn = vi
      .fn()
      .mockRejectedValueOnce(new Error('Fail 1'))
      .mockRejectedValueOnce(new Error('Fail 2'))
      .mockResolvedValue('success');

    const result = await retryPromise(mockFn, 3, 10)('only to type');

    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalledTimes(3);
  });

  it('should throw an error when max retries are reached', async () => {
    const error = new Error('Persistent failure');
    const mockFn = vi.fn().mockRejectedValue(error);

    await expect(retryPromise(mockFn, 3, 10)('only to type')).rejects.toThrow('Persistent failure');
    expect(mockFn).toHaveBeenCalledTimes(3);
  });

  it('should respect the delay between retries', async () => {
    vi.useFakeTimers();

    let attemptCount = 0;
    const mockFn = vi.fn().mockImplementation(() => {
      attemptCount++;
      if (attemptCount < 2) {
        return Promise.reject(new Error('Not yet'));
      }
      return Promise.resolve('success');
    });

    const promise = retryPromise(mockFn, 3, 200)('only to type');

    expect(mockFn).toHaveBeenCalledTimes(0);

    // Fast-forward time
    await vi.advanceTimersByTimeAsync(200);

    // After the delay, the second attempt should be made
    expect(mockFn).toHaveBeenCalledTimes(1);

    await vi.advanceTimersByTimeAsync(200);

    expect(mockFn).toHaveBeenCalledTimes(2);

    const result = await promise;
    expect(result).toBe('success');

    vi.useRealTimers();
  });
});

describe('uniqueByKey', () => {
  it('should return unique items by key', () => {
    const items = [
      { id: 1, name: 'Alice' },
      { id: 2, name: 'Bob' },
      { id: 1, name: 'Alice' },
    ];
    const uniqueItems = uniqueByKey(items, 'id');
    expect(uniqueItems).toEqual([
      { id: 1, name: 'Alice' },
      { id: 2, name: 'Bob' },
    ]);
  });
});

describe('uniqueWith', () => {
  it('should return unique items based on custom comparator', () => {
    const items = [
      { id: 1, value: 'a' },
      { id: 2, value: 'b' },
      { id: 3, value: 'a' },
    ];
    const uniqueItems = uniqueWith(items, (a, b) => a.value === b.value);
    expect(uniqueItems).toEqual([
      { id: 1, value: 'a' },
      { id: 2, value: 'b' },
    ]);
  });

  it('should handle empty array', () => {
    const items: { id: number }[] = [];
    const uniqueItems = uniqueWith(items, (a, b) => a.id === b.id);
    expect(uniqueItems).toEqual([]);
  });

  it('should handle array with single item', () => {
    const items = [{ id: 1, value: 'a' }];
    const uniqueItems = uniqueWith(items, (a, b) => a.id === b.id);
    expect(uniqueItems).toEqual([{ id: 1, value: 'a' }]);
  });

  it('should handle primitive values', () => {
    const items = [1, 2, 2, 3, 1, 4];
    const uniqueItems = uniqueWith(items, (a, b) => a === b);
    expect(uniqueItems).toEqual([1, 2, 3, 4]);
  });
});

describe('assoc', () => {
  it('should add a new key-value pair to an object', () => {
    const obj = { name: 'John' };
    const result = assoc('age', 30)(obj);
    expect(result).toEqual({ name: 'John', age: 30 });
  });

  it('should overwrite existing key-value pair', () => {
    const obj = { name: 'John', age: 25 };
    const result = assoc('age', 30)(obj);
    expect(result).toEqual({ name: 'John', age: 30 });
  });

  it('should not mutate the original object', () => {
    const obj = { name: 'John' };
    const result = assoc('age', 30)(obj);
    expect(obj).toEqual({ name: 'John' });
    expect(result).toEqual({ name: 'John', age: 30 });
  });

  it('should handle empty object', () => {
    const obj = {};
    const result = assoc('key', 'value')(obj);
    expect(result).toEqual({ key: 'value' });
  });
});
