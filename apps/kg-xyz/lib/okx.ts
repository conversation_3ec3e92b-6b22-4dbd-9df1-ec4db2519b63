import { type <PERSON><PERSON><PERSON>rapper } from '@kryptogo/2b/swap-widget';

import {
  OKXCandleResponse,
  OKXCandleResult,
  OKXSearchTokensResult,
  OKXTokenDetail,
  OKXTokenDetailResponse,
  OKXTokenSearchResponse,
  type OKXAssetData,
  type OKXRecentTransaction,
  type OKXTransaction,
  type OKXTokenOverview,
} from '../types/okx';
import { fetchWrapper, getAssetUrl } from './api-config';
import { getCachedData, cacheData } from './indexedDB';

const SEARCH_URL = 'https://www.okx.com/priapi/v1/dx/market/v2';
const MARKET_URL = 'https://www.okx.com/priapi/v5/dex';
const MAX_RETRIES = 5;
const RETRY_DELAY = 1000; // 1 second in milliseconds

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function searchTokens(keyword: string): Promise<OKXSearchTokensResult> {
  try {
    const response = await fetch(`${SEARCH_URL}/search?chainId=501&keyword=${encodeURIComponent(keyword)}`, {
      headers: {
        accept: 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return ((await response.json()) as OKXTokenSearchResponse).data;
  } catch (error) {
    throw new Error(`Failed to search tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getTokenCandles(
  chainId: string,
  address: string,
  after: number,
  bar: string = '1H',
  limit: number = 56,
): Promise<OKXCandleResult> {
  try {
    const response = await fetch(
      `${MARKET_URL}/token/market/dex-token-hlc-candles?chainId=${chainId}&address=${address}&after=${after}&bar=${bar}&limit=${limit}`,
      {
        headers: {
          accept: 'application/json',
        },
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const res = await response.json();
    const data = (res as OKXCandleResponse).data;
    return data.map((candle) => {
      const [, open, high, low, close] = candle;
      // Parse values to numbers to ensure proper comparison
      const openNum = parseFloat(open);
      const closeNum = parseFloat(close);
      const highNum = parseFloat(high);

      // Calculate the maximum reasonable high value
      const maxReasonableHigh = Math.max(openNum, closeNum) + Math.abs(openNum - closeNum) * 10;

      // Adjust high value if it exceeds the reasonable threshold
      const adjustedHigh =
        highNum > maxReasonableHigh ? (Math.max(openNum, closeNum) + Math.abs(openNum - closeNum)).toString() : high;

      // Return the candle with potentially adjusted high value
      return [candle[0], open, adjustedHigh, low, close, candle[5], candle[6], candle[7]];
    });
  } catch (error) {
    throw new Error(`Failed to get token candles: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getTokenInfo(tokenContractAddress: string, chainId: string): Promise<OKXTokenDetail> {
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_WEB_BASEURL}/api/okx/latest/info?tokenContractAddress=${tokenContractAddress}&chainId=${chainId}`,
        {
          headers: {
            accept: 'application/json',
          },
        },
      );

      if (response.status === 429) {
        retries++;
        if (process.env.NODE_ENV === 'production') {
          console.log(`Rate limited, retrying (${retries}/${MAX_RETRIES})...`);
        }
        await delay(RETRY_DELAY);
        continue;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = (await response.json()) as OKXTokenDetailResponse;
      return data.data;
    } catch (error) {
      if (retries < MAX_RETRIES - 1) {
        retries++;
        console.log(`Request failed, retrying (${retries}/${MAX_RETRIES})...`);
        await delay(RETRY_DELAY);
        continue;
      }
      throw new Error(`Failed to get token info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  throw new Error('Max retries exceeded');
}

export async function getTokenOverview(tokenContractAddress: string, chainId: string): Promise<OKXTokenOverview> {
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_WEB_BASEURL}/api/okx/token/overview?tokenContractAddress=${tokenContractAddress}&chainId=${chainId}`,
        {
          headers: {
            accept: 'application/json',
          },
        },
      );

      if (response.status === 429) {
        retries++;
        if (process.env.NODE_ENV === 'production') {
          console.log(`Rate limited, retrying (${retries}/${MAX_RETRIES})...`);
        }
        await delay(RETRY_DELAY);
        continue;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      if (retries < MAX_RETRIES - 1) {
        retries++;
        console.log(`Request failed, retrying (${retries}/${MAX_RETRIES})...`);
        await delay(RETRY_DELAY);
        continue;
      }
      throw new Error(`Failed to get token overview: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  throw new Error('Max retries exceeded');
}

export const getAssetByAddress = async (
  address: string,
  chainId: string,
): FetchWrapper<[{ tokenAssets: OKXAssetData[] }]> =>
  fetchWrapper(getAssetUrl(`/all-token-balances-by-address?address=${address}&chains=${chainId}`));
/**
 * Get combined transactions from OKX and Jupiter limit orders
 * @param address The user's wallet address
 * @param chainId The chain ID
 * @param tokenContractAddress The token contract address
 * @returns Combined transactions from both OKX and Jupiter limit orders
 */
export async function getOkxTransactions(
  address: string,
  chainId: string,
  tokenContractAddress: string,
): Promise<OKXTransaction[]> {
  // Skip caching for server-side rendering
  if (typeof window === 'undefined') {
    return fetchOkxTransactionsFromAPI(address, chainId, tokenContractAddress);
  }

  try {
    const transactions = await fetchOkxTransactionsFromAPI(address, chainId, tokenContractAddress);

    // Generate a cache key for this specific request
    const cacheKey = `okx-transactions-${address}-${chainId}-${tokenContractAddress}`;

    // Cache the results
    await cacheData(cacheKey, 'okx-data', transactions);

    return transactions;
  } catch (error) {
    console.error('Error with cached OKX transactions:', error);
    // Fallback to API if cache operations fail
    return fetchOkxTransactionsFromAPI(address, chainId, tokenContractAddress);
  }
}

/**
 * Fetch OKX transactions directly from the API without caching
 * @param address The user's wallet address
 * @param chainId The chain ID
 * @param tokenContractAddress The token contract address
 * @returns OKX transactions
 */
async function fetchOkxTransactionsFromAPI(
  address: string,
  chainId: string,
  tokenContractAddress: string,
): Promise<OKXTransaction[]> {
  try {
    // Fetch OKX transactions
    const okxResponse = await fetch(
      `${process.env.NEXT_PUBLIC_WEB_BASEURL}/api/okx/my-position/recent-transactions?walletAddress=${address}&
      sort=desc&pageSize=100&types=0&chainId=${chainId}&tokenContractAddress=${tokenContractAddress}
      `,
      {
        headers: {
          accept: 'application/json',
        },
      },
    );

    if (!okxResponse.ok) {
      throw new Error(`HTTP error for OKX transactions! status: ${okxResponse.status}`);
    }

    const okxData = (await okxResponse.json()).data as OKXRecentTransaction;

    const transactions = await Promise.all(
      (okxData.transactions || []).map(async (tx) => {
        if (tx.price !== '0') {
          return tx;
        }

        const price = await getTokenAveragePriceAtTime(tokenContractAddress, tx.blockTime / 1000, chainId);
        const turnover = parseFloat(tx.amount) * price;

        return { ...tx, price: price.toString(), turnover: turnover.toString() };
      }),
    );

    return transactions.filter((tx) => tx.type === 1 || tx.type === 2);
  } catch (error) {
    // Generate a cache key for this specific request
    const cacheKey = `okx-transactions-${address}-${chainId}-${tokenContractAddress}`;

    // Try to get data from cache first
    const cachedData = await getCachedData<OKXTransaction[]>(cacheKey, 'okx-data');

    if (cachedData) {
      console.log(`Using cached OKX transactions for ${address}`);
      return cachedData;
    }

    throw new Error(`Failed to get okx transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get the average price of a token at a specific past time
 * @param chainId The chain ID
 * @param address The token contract address
 * @param timestamp The timestamp in seconds to get the price at
 * @param bar The time interval for candles (default: '1H')
 * @returns The average price or null if no data available
 */
export async function getTokenAveragePriceAtTime(
  address: string,
  timestamp: number,
  chainId: string = '501',
  bar: string = '1H',
): Promise<number> {
  try {
    // Convert timestamp to milliseconds if it's in seconds
    const timestampMs = timestamp * 1000;

    // Normalize timestamp to the hour
    const normalizedTimestamp = new Date(timestampMs);
    normalizedTimestamp.setMinutes(0, 0, 0);
    const normalizedTimestampMs = normalizedTimestamp.getTime();

    // Create a cache key
    const cacheKey = `price_${address}_${chainId}_${normalizedTimestampMs}_${bar}`;

    // Check if we have a cached result
    const cachedResult = localStorage.getItem(cacheKey);
    if (cachedResult) {
      return parseFloat(cachedResult);
    }

    // Fetch one candle at the specified timestamp
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WEB_BASEURL}/api/okx-dex/token/market/history-dex-token-hlc-candles?chainId=${chainId}&address=${address}&after=${normalizedTimestampMs.toString().slice(0, -3)}&bar=${bar}&limit=1&t=${Date.now()}`,
      {
        headers: { accept: 'application/json' },
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = (await response.json()).data as OKXCandleResult;

    // Calculate average price from the candle
    const candle = result[0];
    const open = parseFloat(candle[1]);

    // Cache the result forever
    localStorage.setItem(cacheKey, open.toString());

    return open;
  } catch (error) {
    throw new Error(`Failed to get token average price: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
