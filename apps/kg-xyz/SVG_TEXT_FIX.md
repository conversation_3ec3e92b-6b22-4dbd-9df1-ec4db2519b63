# SVG Text Element 修復

## 問題描述

在 Next.js ImageResponse 中，SVG 的 `<text>` 元素不被支援，會出現以下錯誤：
```
<text> nodes are not currently supported, please convert them to <path>
```

## 解決方案

我已經修復了 `SmoothPriceChartWithMarkers.tsx` 組件中的這個問題：

### 修改前
```tsx
{/* 在 SVG 內部使用 <text> 元素 */}
<text
  x={markerX}
  y={markerY + (isBuy ? markerSize + 5 : -(markerSize + 5))}
  textAnchor="middle"
  dominantBaseline="central"
  fill="white"
  fontSize="12"
  fontWeight="bold"
  fontFamily="sans-serif"
>
  {isBuy ? 'B' : 'S'}
</text>
```

### 修改後
```tsx
{/* 在 SVG 外部使用 HTML <div> 元素 */}
{markers.map((marker, idx) => {
  const markerX = scaleX(marker.index);
  const markerY = scaleY(validPrices[marker.index]);
  const isBuy = marker.type === 1;
  const markerSize = 12;
  const markerCenterY = markerY + (isBuy ? markerSize + 5 : -(markerSize + 5));
  
  return (
    <div
      key={`label-${idx}`}
      style={{
        position: 'absolute',
        left: markerX - 6,
        top: markerCenterY - 8,
        width: 12,
        height: 16,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '12px',
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        pointerEvents: 'none',
      }}
    >
      {isBuy ? 'B' : 'S'}
    </div>
  );
})}
```

## 修復效果

✅ **解決了 ImageResponse 兼容性問題**
- 移除了不支援的 SVG `<text>` 元素
- 使用 HTML `<div>` 元素來顯示文字標記
- 保持了相同的視覺效果和位置

✅ **保持功能完整性**
- 買入點顯示 'B' 標記
- 賣出點顯示 'S' 標記
- 標記位置準確對應到圖表上的交易點

✅ **優化了渲染性能**
- HTML 文字渲染比 SVG 文字更高效
- 更好的字體渲染效果

## 測試確認

現在 trading-meta endpoint 應該可以正常生成圖片，不會再出現 `<text>` 元素相關的錯誤。

## 相關文件

- `apps/kg-xyz/components/charts/SmoothPriceChartWithMarkers.tsx` - 主要修復文件
- `apps/kg-xyz/app/token/[network]/[address]/trading-meta/[wallet]/route.tsx` - 使用該組件的 API route
