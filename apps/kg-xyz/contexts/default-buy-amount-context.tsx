'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface DefaultBuyAmountContextType {
  defaultBuyAmount: number;
  setDefaultBuyAmount: (amount: number) => void;
}

const DefaultBuyAmountContext = createContext<DefaultBuyAmountContextType | undefined>(undefined);

const DEFAULT_VALUE = 0.5;
const STORAGE_KEY = 'kg-default-buy-amount';

export function DefaultBuyAmountProvider({ children }: { children: ReactNode }) {
  const [defaultBuyAmount, setDefaultBuyAmountState] = useState<number>(DEFAULT_VALUE);

  // Initialize from localStorage on mount (client-side only)
  useEffect(() => {
    try {
      const storedValue = localStorage.getItem(STORAGE_KEY);
      if (storedValue) {
        const parsedValue = parseFloat(storedValue);
        if (!isNaN(parsedValue) && parsedValue > 0) {
          setDefaultBuyAmountState(parsedValue);
        }
      }
    } catch (error) {
      console.error('Error reading from localStorage:', error);
    }
  }, []);

  // Custom setter that updates both state and localStorage
  const setDefaultBuyAmount = (amount: number) => {
    setDefaultBuyAmountState(amount);
    try {
      localStorage.setItem(STORAGE_KEY, amount.toString());
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  };

  return (
    <DefaultBuyAmountContext.Provider value={{ defaultBuyAmount, setDefaultBuyAmount }}>
      {children}
    </DefaultBuyAmountContext.Provider>
  );
}

export function useDefaultBuyAmount() {
  const context = useContext(DefaultBuyAmountContext);
  if (context === undefined) {
    throw new Error('useDefaultBuyAmount must be used within a DefaultBuyAmountProvider');
  }
  return context;
}
