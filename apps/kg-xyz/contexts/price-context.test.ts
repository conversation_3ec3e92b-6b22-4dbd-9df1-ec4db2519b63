import { describe, it, expect } from 'vitest';

import { calculateStandardDeviation } from './price-context';

describe('calculateStandardDeviation', () => {
  it('returns 1% of the last value when array has less than 3 elements', () => {
    expect(calculateStandardDeviation([100])).toBe(1);
    expect(calculateStandardDeviation([100, 200])).toBe(2);
  });

  it('calculates standard deviation correctly for array with 3 or more elements', () => {
    // Test with 3 elements
    const values1 = [10, 12, 14];
    // Mean = 12, Variance = ((4 + 0 + 4) / 3) = 8/3, StdDev = sqrt(8/3) ≈ 1.633
    expect(calculateStandardDeviation(values1)).toBeCloseTo(1.633, 3);

    // Test with more elements
    const values2 = [2, 4, 4, 4, 5, 5, 7, 9];
    // Mean = 5, Variance = ((9 + 1 + 1 + 0 + 0 + 0 + 4 + 16) / 8) = 31/8 = 3.875
    // StdDev = sqrt(3.875) ≈ 1.9685
    expect(calculateStandardDeviation(values2)).toBeCloseTo(2, 2);
  });

  it('handles negative numbers correctly', () => {
    const values = [-5, 0, 5];
    // Mean = 0, Variance = (25 + 0 + 25) / 3 = 50/3, StdDev = sqrt(50/3) ≈ 4.0825
    expect(calculateStandardDeviation(values)).toBeCloseTo(4.0825, 4);
  });

  it('returns 0 for array with all same values', () => {
    const values = [7, 7, 7, 7];
    expect(calculateStandardDeviation(values)).toBe(0);
  });
});
