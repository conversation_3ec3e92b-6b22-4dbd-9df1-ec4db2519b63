'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface SuspectInsiderContextType {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const SuspectInsiderContext = createContext<SuspectInsiderContextType | undefined>(undefined);

export function SuspectInsiderProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  return <SuspectInsiderContext.Provider value={{ isOpen, setIsOpen }}>{children}</SuspectInsiderContext.Provider>;
}

export function useSuspectInsider() {
  const context = useContext(SuspectInsiderContext);
  if (context === undefined) {
    throw new Error('useSuspectInsider must be used within a SuspectInsiderProvider');
  }
  return context;
}
