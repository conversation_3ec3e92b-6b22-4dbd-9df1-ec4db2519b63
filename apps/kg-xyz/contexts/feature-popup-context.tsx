'use client';

import { type ReactNode, createContext, useContext, useEffect, useState } from 'react';

import { FeaturePopup } from '@/components/feature-popup';
import { useDebugWallet } from '@/hooks/use-debug-wallet';

interface FeaturePopupContextType {
  showPopup: () => void;
  hidePopup: () => void;
}

const FeaturePopupContext = createContext<FeaturePopupContextType | undefined>(undefined);

export function FeaturePopupProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const { isDebugMode } = useDebugWallet();

  useEffect(() => {
    console.log('FeaturePopupProvider mounted, isOpen:', isOpen);
    const featurePopupDismissed = localStorage.getItem('featurePopupDismissed');

    if (isDebugMode) {
      setIsOpen(false);
    } else {
      setIsOpen(featurePopupDismissed !== 'true');
    }
  }, [isDebugMode]);

  const showPopup = () => {
    console.log('showPopup called');
    setIsOpen(true);
  };

  const hidePopup = () => {
    console.log('hidePopup called');
    setIsOpen(false);
  };

  return (
    <FeaturePopupContext.Provider value={{ showPopup, hidePopup }}>
      {children}
      <FeaturePopup isOpen={isOpen} onClose={hidePopup} />
    </FeaturePopupContext.Provider>
  );
}

export const useFeaturePopup = () => {
  const context = useContext(FeaturePopupContext);
  if (context === undefined) {
    throw new Error('useFeaturePopup must be used within a FeaturePopupProvider');
  }
  return context;
};
