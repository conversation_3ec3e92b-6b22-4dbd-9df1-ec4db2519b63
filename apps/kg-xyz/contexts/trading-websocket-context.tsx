'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

import type { OKXMarketInfo } from '@/types/okx';

interface TradingWebSocketContextType {
  tradingData: {
    volumeBuy: string | null;
    volumeSell: string | null;
    txSell: string | null;
    txBuy: string | null;
    tradeNumSell: string | null;
    tradeNumBuy: string | null;
  };
  isLoading: boolean;
}

const defaultContextValue: TradingWebSocketContextType = {
  tradingData: {
    volumeBuy: null,
    volumeSell: null,
    txSell: null,
    txBuy: null,
    tradeNumSell: null,
    tradeNumBuy: null,
  },
  isLoading: true,
};

const TradingWebSocketContext = createContext<TradingWebSocketContextType>(defaultContextValue);

export function useTradingWebSocket() {
  return useContext(TradingWebSocketContext);
}

export function TradingWebSocketProvider({ children, tokenAddress }: { children: ReactNode; tokenAddress: string }) {
  const [tradingData, setTradingData] = useState<TradingWebSocketContextType['tradingData']>(
    defaultContextValue.tradingData,
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const ws = new WebSocket('wss://wsdexpri.okx.com/ws/v5/ipublic');

    ws.onopen = () => {
      console.log('WebSocket connected');

      ws.send(
        JSON.stringify({
          op: 'subscribe',
          args: [
            {
              channel: 'dex-market-tradeRealTime',
              chainId: '501',
              tokenAddress,
            },
          ],
        }),
      );
    };

    ws.onmessage = (event) => {
      const data: { data: [OKXMarketInfo] } = JSON.parse(event.data);
      setTradingData({
        volumeBuy: data?.data[0]?.volumeBuy1H || '0',
        volumeSell: data?.data[0]?.volumeSell1H || '0',
        txSell: data?.data[0]?.txsSell1H || '0',
        txBuy: data?.data[0]?.txsBuy1H || '0',
        tradeNumSell: data?.data[0]?.uniqueTradersSell1H || '0',
        tradeNumBuy: data?.data[0]?.uniqueTradersBuy1H || '0',
      });
      setIsLoading(false);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected');
    };

    return () => {
      ws.close();
    };
  }, [tokenAddress]);

  return (
    <TradingWebSocketContext.Provider value={{ tradingData, isLoading }}>{children}</TradingWebSocketContext.Provider>
  );
}
