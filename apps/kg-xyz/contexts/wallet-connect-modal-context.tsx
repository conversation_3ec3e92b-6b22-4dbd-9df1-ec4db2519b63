'use client';

import { createContext, useContext, useState } from 'react';

import { WalletConnectModal } from '@/components/wallet-connect-modal';

interface WalletConnectModalContextType {
  isOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
}

const WalletConnectModalContext = createContext<WalletConnectModalContextType>({
  isOpen: false,
  openModal: () => {},
  closeModal: () => {},
});

export function WalletConnectModalProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  return (
    <WalletConnectModalContext.Provider value={{ isOpen, openModal, closeModal }}>
      {children}
      <WalletConnectModal isOpen={isOpen} onClose={closeModal} />
    </WalletConnectModalContext.Provider>
  );
}

export const useWalletConnectModal = () => useContext(WalletConnectModalContext);
