'use client';

import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { getTokenPrices } from '@/lib/kg-api';
import { sum } from '@/lib/utils';

interface PriceHistoryEntry {
  price: number;
  timestamp: number;
}

interface PriceContextType {
  prices: { [key: string]: number };
  slippages: { [key: string]: number };
  refreshPrices: () => Promise<void>;
  registerToken: (address: string) => void;
  registerRealtimeToken: (address: string) => void;
  updateTokenPrice: (address: string, price: number) => void;
  priceHistory: { [key: string]: PriceHistoryEntry[] };
}

const PriceContext = createContext<PriceContextType>({
  prices: {},
  slippages: {},
  refreshPrices: async () => {},
  registerToken: () => {},
  registerRealtimeToken: () => {},
  updateTokenPrice: () => {},
  priceHistory: {},
});

export const usePrices = () => useContext(PriceContext);

interface PriceProviderProps {
  children: React.ReactNode;
  initialPrices?: { [key: string]: number };
}

// Utility function to calculate standard deviation
export const calculateStandardDeviation = (values: number[]): number => {
  if (values.length < 3) return 0.01 * values[values.length - 1];

  const mean = values.reduce(sum, 0) / values.length;
  const squaredDifferences = values.map((val) => Math.pow(val - mean, 2));
  const variance = squaredDifferences.reduce(sum, 0) / values.length;

  return Math.sqrt(variance);
};

export const PriceProvider: React.FC<PriceProviderProps> = ({ children, initialPrices = {} }) => {
  const [prices, setPrices] = useState<{ [key: string]: number }>(initialPrices);
  const [priceHistory, setPriceHistory] = useState<{ [key: string]: PriceHistoryEntry[] }>({});
  const [slippages, setSlippages] = useState<{ [key: string]: number }>({});
  const [tokenAddresses, setTokenAddresses] = useState<string[]>([]);
  const [realtimeTokenAddresses, setRealtimeTokenAddresses] = useState<string[]>([]);
  const [wsConnections, setWsConnections] = useState<{ [key: string]: WebSocket }>({});

  // Function to register a token address for non-realtime price updates
  const registerToken = useCallback(
    (address: string) => {
      // Check if the address is already in the realtime list
      if (realtimeTokenAddresses.includes(address)) {
        // If it's already in realtime, don't add it to non-realtime
        return;
      }

      // Check if the address is already in the non-realtime list
      if (!tokenAddresses.includes(address)) {
        setTokenAddresses((prev) => [...prev, address]);
        setSlippages((prev) => ({
          ...prev,
          [`sol:${address}`]: 0.05,
        }));
      }
    },
    [tokenAddresses, realtimeTokenAddresses],
  );

  // Function to register a token address for realtime price updates
  const registerRealtimeToken = useCallback(
    (address: string) => {
      // Check if the address is already in the non-realtime list
      if (tokenAddresses.includes(address)) {
        // Remove from non-realtime list if it exists there
        setTokenAddresses((prev) => prev.filter((addr) => addr !== address));
      }

      // Check if the address is already in the realtime list
      const isAlreadyInRealtime = realtimeTokenAddresses.includes(address);

      if (!isAlreadyInRealtime) {
        setRealtimeTokenAddresses((prev) => [...prev, address]);
        setSlippages((prev) => ({
          ...prev,
          [`sol:${address}`]: 0.05,
        }));

        // If the token wasn't in either list before, fetch its price immediately
        const isNewToken = !tokenAddresses.includes(address);

        if (isNewToken) {
          // Fetch price immediately for the new token
          getTokenPrices([address])
            .then((newPrices) => {
              setPrices((prevPrices) => ({
                ...prevPrices,
                ...newPrices,
              }));
            })
            .catch((error) => {
              console.error('Error fetching initial price for new token:', error);
            });
        }
      }
    },
    [tokenAddresses, realtimeTokenAddresses],
  );

  // Function to update a specific token's price and its history
  const updateTokenPrice = useCallback((address: string, price: number) => {
    const tokenKey = `sol:${address}`;
    const now = Date.now();

    // Update current price
    setPrices((prev) => ({
      ...prev,
      [tokenKey]: price,
    }));

    // Update price history
    setPriceHistory((prev) => {
      const tokenHistory = prev[tokenKey] || [];
      const updatedHistory = [...tokenHistory, { price, timestamp: now }];
      const filteredHistory = updatedHistory.filter((entry) => now - entry.timestamp <= 60000);
      return {
        ...prev,
        [tokenKey]: filteredHistory,
      };
    });
  }, []);

  // Calculate slippages whenever price history changes, but throttled to run at most every 3 seconds
  useEffect(() => {
    let lastRun = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    const calculateSlippages = () => {
      const now = Date.now();
      // If less than 3 seconds have passed since last run, throttle the execution
      if (now - lastRun < 3000) {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(
          () => {
            calculateSlippages();
          },
          3000 - (now - lastRun),
        );
        return;
      }

      lastRun = now;
      const newSlippages: { [key: string]: number } = {};
      Object.entries(priceHistory).forEach(([tokenKey, history]) => {
        // Filter history for the last minute
        const lastMinuteHistory = history.filter((entry) => now - entry.timestamp <= 60000);

        // Extract just the prices
        const prices = lastMinuteHistory.map((entry) => entry.price);

        // Calculate slippage based on standard deviation divided by current price
        if (prices.length > 0 && prices[prices.length - 1] > 0) {
          const stdDev = calculateStandardDeviation(prices);
          newSlippages[tokenKey] = (3 * stdDev) / prices[prices.length - 1];
          newSlippages[tokenKey] = Math.max(0.01, Math.min(Math.floor(newSlippages[tokenKey] * 1000) / 1000.0, 0.3));
        } else {
          newSlippages[tokenKey] = 0.05; // Default value if price is 0
        }
      });
      setSlippages((prev) => ({
        ...prev,
        ...newSlippages,
      }));
    };

    // Initial calculation
    calculateSlippages();

    // Clean up any pending timeouts on unmount
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [priceHistory]);

  // Clean up old price history data periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setPriceHistory((prev) => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.entries(updated).forEach(([tokenKey, history]) => {
          const filteredHistory = history.filter((entry) => now - entry.timestamp <= 60000);
          if (filteredHistory.length !== history.length) {
            updated[tokenKey] = filteredHistory;
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }, 10000); // Clean up every 10 seconds

    return () => clearInterval(cleanupInterval);
  }, []);

  // Function to refresh prices for all non-realtime registered tokens
  const refreshPrices = useCallback(async () => {
    if (tokenAddresses.length === 0) return;

    try {
      // Filter out token addresses that appear in the URL
      const currentUrl = window.location.href;
      const filteredAddresses = tokenAddresses.filter((address) => {
        return !currentUrl.includes(address);
      });
      if (filteredAddresses.length === 0) return;
      const newPrices = await getTokenPrices(filteredAddresses);

      // Update current prices
      setPrices((prevPrices) => ({
        ...prevPrices,
        ...newPrices,
      }));

      // Also update price history for these tokens
      const now = Date.now();
      setPriceHistory((prev) => {
        const updated = { ...prev };

        Object.entries(newPrices).forEach(([tokenKey, price]) => {
          const history = updated[tokenKey] || [];
          updated[tokenKey] = [...history, { price, timestamp: now }].filter((entry) => now - entry.timestamp <= 60000);
        });

        return updated;
      });
    } catch (error) {
      console.error('Error refreshing prices:', error);
    }
  }, [tokenAddresses]);

  // Set up WebSocket connections for realtime tokens
  useEffect(() => {
    // Close existing WebSocket connections that are no longer needed
    Object.entries(wsConnections).forEach(([address, ws]) => {
      if (!realtimeTokenAddresses.includes(address)) {
        ws.close();
        setWsConnections((prev) => {
          const newConnections = { ...prev };
          delete newConnections[address];
          return newConnections;
        });
      }
    });

    // Create new WebSocket connections for realtime tokens
    realtimeTokenAddresses.forEach((address) => {
      if (!wsConnections[address]) {
        const ws = new WebSocket('wss://ws.gmgn.ai/quotation');

        ws.onopen = () => {
          const message = {
            action: 'subscribe',
            channel: 'kline',
            id: `realtime-${address}`,
            data: [{ chain: 'sol', addresses: address, interval: '1m' }],
          };
          ws.send(JSON.stringify(message));
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (data.channel === 'kline' && data.data && data.data[0]) {
              const price = parseFloat(data.data[0].c); // Close price
              if (!isNaN(price)) {
                updateTokenPrice(address, price);
              }
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        ws.onerror = (error) => {
          console.error(`WebSocket error for ${address}:`, error);
        };

        ws.onclose = () => {
          setWsConnections((prev) => {
            const newConnections = { ...prev };
            delete newConnections[address];
            return newConnections;
          });
        };

        setWsConnections((prev) => ({
          ...prev,
          [address]: ws,
        }));
      }
    });

    return () => {
      // Clean up all WebSocket connections when component unmounts
      Object.values(wsConnections).forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
        }
      });
    };
  }, [realtimeTokenAddresses, wsConnections, updateTokenPrice]);

  // Set up interval to refresh non-realtime prices
  useEffect(() => {
    if (tokenAddresses.length === 0) return;

    // Initial fetch
    refreshPrices();

    // Set up interval for refreshing
    const intervalId = setInterval(refreshPrices, 3000);

    return () => clearInterval(intervalId);
  }, [tokenAddresses, refreshPrices]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      prices,
      slippages,
      refreshPrices,
      registerToken,
      registerRealtimeToken,
      updateTokenPrice,
      priceHistory,
    }),
    [prices, slippages, refreshPrices, registerToken, registerRealtimeToken, updateTokenPrice, priceHistory],
  );
  return <PriceContext.Provider value={contextValue}>{children}</PriceContext.Provider>;
};
