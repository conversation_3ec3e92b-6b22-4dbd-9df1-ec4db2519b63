## 一、核心目標與策略

### MVP 核心目標

- 提供一個極簡、方便的代幣分享與購買入口
- 減少使用者從發現到購買的操作步驟
- 建立有效的用戶推薦激勵機制

### 產品策略定位

- 主要場景：社群通訊軟體中的代幣分享與一鍵購買
- 轉化路徑：社群連結 → kryptogo.xyz 網頁→ KryptoGO App 購買
- 價值主張：最簡單的迷因幣購買方式 + 高額推薦獎勵

## 二、產品架構決策

對使用者來說有兩個交易路徑可以達到買幣的目的：

1. 網頁連接第三方錢包（如 Phantom）發送交易
2. 跳轉至 KryptoGO App 發送交易

在本次 MVP 實作中，我們選擇**跳轉至 KryptoGO App 完成交易**，原因包含以下幾點

### 手機端體驗

- 網頁連接主流錢包如 Phantom，在手機上需經過多次跳轉（網頁→錢包 DApp→重新連接→確認）
- 使用 KryptoGO App 只需兩步即可完成（網頁購買→ KryptoGO App 確認）
- Deep Link 可確保跳轉流程順暢且保留交易與推薦人資訊

### 技術路線

- 為後續功能（如 Gasless, Universal Swap）打好基礎，未來使用者可在 App 中進行任意鏈上的代幣兌換，這也是我們核心的技術優勢

### 潛在顧慮

- App 安裝以及導入錢包/資產的門檻高
- 解決方式：我們可通過展示便捷的交易流程吸引使用者，並提供高額推薦獎勵以提高使用意願。另外在社群通訊平台上的連結導引與預覽也能吸引使用者快速交易
    - 但哪種方式最有效還未知，需要進一步實驗

## 三、功能範圍與實作優先級

### MVP 範圍功能

1. **代幣資訊頁面**
    - 基礎資訊展示、交易數據和走勢圖
    - 可生成推薦別人購買一個幣種的連結
    - 一鍵購買按鈕（直接跳轉 App）
2. 代幣搜尋功能
    - 基於合約地址或 Token Symbol 搜尋代幣
3. **社群分享預覽優化**
    - 支援平台：Telegram、Discord、LINE
    - 預覽資訊：
        - 代幣 Symbol、名稱、描述
        - 市值與交易量
        - (TBD) 24 小時漲跌幅
        - (TBD) 1 天價格走勢圖
4. **推薦獎勵系統**
    - 單筆交易 50% 手續費返佣
    - 推薦獎勵儀表板
    - 推薦獎勵金額提領
5. App 內交易功能
    - Deep link 跳轉目的頁面中，可一鍵買賣 Solana 上的代幣，類似 OKX 的 Meme 交易模組

### 暫不實作功能

- Gasless 交易支援：只先考慮有 SOL 的使用者，因為已經佔了目標客群的 90% 以上
- 網頁端錢包連接：因爲 KryptoGO Web SDK 整合 Solana 開發工作較大，開發速度受限
- 完整的資產管理頁面：因為網頁的重點是導流購買，資產管理可復用既有 App 功能
- 設定與個人資料頁面：可復用既有 App 功能

## 四、推薦獎勵機制

### 基本規則

- 每個用戶可為每個代幣生成專屬推薦連結
- 任何其他用戶通過該推薦連結完成交易，推薦人獲得該筆交易 50% 手續費

### 獎勵設計

- 即時顯示推薦獎勵收益
- 推薦人可隨時領出收益

## 五、關鍵指標追蹤

- 用戶指標：日活躍用戶數量
- 轉化指標：預覽連結點擊率
- 交易指標：每日交易量
- 推薦指標：推薦連結生成數、獎勵發放金額
- 利潤指標：總交易利潤

### 兩週目標值（日期：2/7）

- 日活躍用戶數量：100
- (TBD) 預覽連結點擊率：平均每個連結被點擊 3 次以上
- 每日交易量：10,000 USD
- 每日推薦連結生成數：500
- 總獎勵發放金額：100 USD
- 總交易利潤：500 USD

## 六、實作時程

過年前完成一版，直接上線到 Prod，過年期間可以開始提供給親友小規模試用

### Mobile

- Solana 代幣交易功能：報價、手續費估算、發送買賣交易、防 MEV 攻擊、切換錢包
- 送出交易時，紀錄推薦人 UID
- 推薦獎勵頁面與提領
- 確保 Deep link 跳轉正常

### Frontend

- 代幣推薦連結分享，串接 Referral as a Service
- SEO Preview：包含代幣詳情頁面與推薦連結
- 確保 Deep link 跳轉正常
- OKX API Proxy at NextJS Backend
    - 首頁與搜尋合約地址/Token Symbol 功能
    - 代幣詳情頁面：代幣 Symbol、Icon、名稱、描述、線圖、市值、交易量、官方網站

### Backend

- 推薦獎勵紀錄與利潤計算
- 推薦獎勵提領
