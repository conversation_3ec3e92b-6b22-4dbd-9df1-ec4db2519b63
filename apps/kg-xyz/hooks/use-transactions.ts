import { useState, useMemo, useEffect } from 'react';
import { createQuery } from 'react-query-kit';

import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { fetchJupiterOrders, formatJupiterOrdersToTransactions } from '@/lib/jupiter';
import { getOkxTransactions } from '@/lib/okx';
import type { OKXTransaction } from '@/types/okx';

interface UseTransactionsOptions {
  tokenAddress: string;
  refetchInterval?: number;
  enabled?: boolean;
}

function calculateRealizedProfits(transactions: OKXTransaction[]): OKXTransaction[] {
  let currentPosition = 0;
  let totalCost = 0;
  const txsWithProfit: OKXTransaction[] = [];

  // Process transactions in chronological order to track position and cost
  transactions.toReversed().forEach((tx) => {
    const amount = parseFloat(tx.amount);
    const price = parseFloat(tx.price);
    const txCopy = { ...tx };

    if (tx.type === 1) {
      // Buy
      totalCost += amount * price;
      currentPosition += amount;
      txCopy.singleRealizedProfit = '0'; // No realized profit on buys
    } else if (tx.type === 2) {
      // Sell
      const amountToReduce = Math.min(amount, currentPosition);
      if (amountToReduce > 0) {
        // Calculate average cost for the portion being sold
        const avgCost = currentPosition > 0 ? totalCost / currentPosition : 0;
        // Calculate realized profit/loss
        const realizedProfit = amountToReduce * (price - avgCost);
        txCopy.singleRealizedProfit = realizedProfit.toString();

        // Update position and cost
        const costReduction = (amountToReduce / currentPosition) * totalCost;
        totalCost -= costReduction;
        currentPosition -= amountToReduce;
      }
    }
    txsWithProfit.push(txCopy);
  });

  // Return transactions in original order
  return txsWithProfit.toReversed();
}

export function useTransactions({ tokenAddress, refetchInterval, enabled = true }: UseTransactionsOptions) {
  const { address: publicKey } = useDebugWallet();
  const [jupiterTransactions, setJupiterTransactions] = useState<OKXTransaction[]>([]);

  // Create typed queries using react-query-kit
  const useOkxTransactions = createQuery<OKXTransaction[], { publicKey: string; tokenAddress: string }>({
    queryKey: ['okx-transactions'],
    fetcher: ({ publicKey, tokenAddress }) => getOkxTransactions(publicKey || '', '501', tokenAddress),
  });

  const useJupiterOrders = createQuery<Awaited<ReturnType<typeof fetchJupiterOrders>>, { publicKey: string }>({
    queryKey: ['jupiter-transactions'],
    fetcher: ({ publicKey }) => fetchJupiterOrders(publicKey || '', 'history'),
  });

  // Fetch OKX transactions
  const { data: okxTransactions } = useOkxTransactions({
    variables: { publicKey: publicKey || '', tokenAddress },
    enabled: enabled && !!publicKey,
    refetchInterval,
    retry: 10,
    retryDelay: () => Math.floor(Math.random() * 5000) + 1000,
  });

  // Fetch Jupiter transactions
  const { data: jupiterOrders } = useJupiterOrders({
    variables: { publicKey: publicKey || '' },
    enabled: enabled && !!publicKey,
    refetchInterval,
    retry: 10,
    retryDelay: () => Math.floor(Math.random() * 5000) + 1000,
  });

  useEffect(() => {
    if (!jupiterOrders) return;
    formatJupiterOrdersToTransactions(
      jupiterOrders.orders,
      publicKey || '',
      tokenAddress,
      '501',
      true, // Only include completed orders
    ).then(setJupiterTransactions);
  }, [jupiterOrders, publicKey, tokenAddress]);

  // Combine and process transactions
  const transactions = useMemo(() => {
    if (!okxTransactions || !jupiterTransactions) {
      return [];
    }
    const allJupiterTxs = jupiterTransactions?.flatMap(({ internalTrading: internalTradingTxList }) =>
      internalTradingTxList?.map(({ txId }) => txId),
    );

    const allTransactions = [
      ...(jupiterTransactions || []),
      ...(okxTransactions.filter(({ txHash }) => !allJupiterTxs?.includes(txHash)) || []),
    ];
    allTransactions.sort((a: OKXTransaction, b: OKXTransaction) => b.blockTime - a.blockTime);

    // Recalculate realized profits for all transactions
    return calculateRealizedProfits(allTransactions);
  }, [okxTransactions, jupiterTransactions]);

  return {
    transactions,
  };
}
