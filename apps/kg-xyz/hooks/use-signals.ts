import { createQuery } from 'react-query-kit';

import { SignalResponse, SellSignalResponse, SignalStatusResponse } from '@/types/kg-api';

// Create typed query for buy signals
const useBuySignalsQuery = createQuery<SignalResponse[], void>({
  queryKey: ['buy-signal'],
  fetcher: async () => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_signal/buy`);
    const data: { data: SignalResponse[] } = await res.json();
    return data.data;
  },
});

/**
 * Custom hook to fetch buy signals from the API
 * @param options Optional query options
 * @returns Query result containing buy signals data
 */
export function useBuySignals(options?: { refetchInterval?: number; enabled?: boolean }) {
  return useBuySignalsQuery({
    refetchInterval: options?.refetchInterval ?? 3000,
    enabled: options?.enabled ?? true,
  });
}

// Create typed query for sell signals
const useSellSignalsQuery = createQuery<SellSignalResponse[], void>({
  queryKey: ['sell-signal'],
  fetcher: async () => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_signal/sell`);
    const data: { data: SellSignalResponse[] } = await res.json();
    return data.data;
  },
});

/**
 * Custom hook to fetch sell signals from the API
 * @param options Optional query options
 * @returns Query result containing sell signals data
 */
export function useSellSignals(options?: { refetchInterval?: number; enabled?: boolean }) {
  return useSellSignalsQuery({
    refetchInterval: options?.refetchInterval ?? 3000,
    enabled: options?.enabled ?? true,
  });
}

// Create typed query for signal stats
const useSignalStatsQuery = createQuery<SignalStatusResponse, void>({
  queryKey: ['signal-status'],
  fetcher: async () => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_signal/stats`);
    const data: { data: SignalStatusResponse } = await res.json();
    return data.data;
  },
});

/**
 * Custom hook to fetch signal stats from the API
 * @param options Optional query options
 * @returns Query result containing signal stats data
 */
export function useSignalStats(options?: { refetchInterval?: number; enabled?: boolean }) {
  return useSignalStatsQuery({
    refetchInterval: options?.refetchInterval,
    enabled: options?.enabled ?? true,
  });
}
