import { useState } from 'react';

import { TRADING_CONFIG } from '@/config/trading';
import { useToast } from '@/hooks/use-toast';
import { useTokenAnalysisCredits } from '@/hooks/use-token-analysis-credits';
import { checkTxConfirmation } from '@/lib/utils';
import * as Sentry from '@sentry/nextjs';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, Transaction } from '@solana/web3.js';

import { useDebugWallet } from './use-debug-wallet';

interface CreditPurchaseOptions {
  credits: number;
  solAmount: number;
  walletAddress: string;
}

interface UseCreditPurchaseReturn {
  purchaseCredits: (options: CreditPurchaseOptions) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

// This would be your treasury wallet address where payments are sent
// For demo purposes, using a placeholder address - replace with your actual treasury wallet
const TREASURY_WALLET = TRADING_CONFIG.purchaseWalletAccount;

export function useCreditPurchase(): UseCreditPurchaseReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { connection } = useConnection();
  const { address: walletAddress } = useDebugWallet();
  const { sendTransaction, publicKey } = useWallet();
  const { toast } = useToast();
  const { refetch: refetchCredits } = useTokenAnalysisCredits(walletAddress, { enabled: false });

  const purchaseCredits = async (options: CreditPurchaseOptions): Promise<boolean> => {
    if (!publicKey || !sendTransaction) {
      setError('Wallet not connected');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      // Create SOL transfer transaction
      const lamports = Math.floor(options.solAmount * 1e9); // Convert SOL to lamports
      const treasuryPublicKey = new PublicKey(TREASURY_WALLET);

      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: publicKey,
          toPubkey: treasuryPublicKey,
          lamports,
        }),
      );

      // Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = publicKey;

      // Send transaction
      const signature = await sendTransaction(transaction, connection);

      toast({
        title: 'Payment Submitted',
        description: (
          <a href={`https://solscan.io/tx/${signature}`} target="_blank" rel="noopener noreferrer">
            Transaction:{' '}
            <span className="underline">
              {signature.slice(0, 8)}...{signature.slice(-7)}
            </span>
          </a>
        ),
      });

      // Wait for confirmation
      const txResult = await checkTxConfirmation(signature, connection);

      if (txResult.status === 'confirmed') {
        // Call backend to credit the user's account
        const response = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_analysis/purchase_credits`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            wallet_address: options.walletAddress,
            tx_hash: signature,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to credit account');
        }

        toast({
          title: 'Credits Purchased Successfully',
          description: `${options.credits} credits have been added to your account`,
        });

        return true;
      } else {
        throw new Error('Transaction failed to confirm');
      }
    } catch (err) {
      const errorMessage = (err as Error).message || 'Failed to purchase credits';
      setError(errorMessage);

      Sentry.captureException(err);

      toast({
        variant: 'destructive',
        title: 'Purchase Failed',
        description: errorMessage,
      });

      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    purchaseCredits,
    loading,
    error,
  };
}
