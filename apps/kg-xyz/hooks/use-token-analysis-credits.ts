import { createQuery } from 'react-query-kit';

/**
 * Custom hook to fetch token analysis credits for a wallet address
 * @param walletAddress The wallet address to check credits for
 * @param options Optional query options
 * @returns Query result containing the number of available credits
 */
// Create typed query using react-query-kit
const useTokenAnalysisCreditsQuery = createQuery<{ credits: number; tradingVolume: number }, { walletAddress: string }>(
  {
    queryKey: ['token-analysis-credits'],
    fetcher: async ({ walletAddress }) => {
      if (!walletAddress) return { credits: 0, tradingVolume: 0 };

      const res = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/token_analysis/credits/${walletAddress}`);

      if (!res.ok) {
        throw new Error(`Failed to fetch token analysis credits: ${res.status}`);
      }

      const data = await res.json();
      return {
        credits: data.data.credits,
        tradingVolume: data.data.trading_volume ?? 0,
      };
    },
  },
);

export function useTokenAnalysisCredits(
  walletAddress: string | undefined,
  options?: {
    refetchInterval?: number;
    enabled?: boolean;
  },
) {
  const queryResult = useTokenAnalysisCreditsQuery({
    variables: { walletAddress: walletAddress || '' },
    refetchInterval: options?.refetchInterval ?? 10000, // Default to 10 seconds
    enabled: !!walletAddress && (options?.enabled ?? true),
  });

  return queryResult;
}
