'use client';

import { useSearchParams } from 'next/navigation';

import { useWallet } from '@solana/wallet-adapter-react';

/**
 * A hook that returns the wallet address from useAccount()
 * or the debugWallet parameter from the URL if present
 */
export function useDebugWallet() {
  const { publicKey } = useWallet();
  const searchParams = useSearchParams();
  const debugWallet = searchParams.get('debugWallet');

  return {
    address: debugWallet ?? publicKey?.toBase58(),
    isDebugMode: !!debugWallet,
    debugWallet,
  };
}
