import { createQuery } from 'react-query-kit';

import { TRADING_CONFIG } from '@/config/trading';
import { SOL_ADDRESS, USDC_ADDRESS } from '@/lib/chains';
import { fetchJupiterOrders } from '@/lib/jupiter';
import { getAssetByAddress, getTokenInfo, getTokenOverview, getOkxTransactions } from '@/lib/okx';
import { jupiterQuoteApi } from '@/lib/sign-transaction';

/**
 * Centralized API queries file
 *
 * This file contains all the API queries used in the application.
 * Each query is defined using the createQuery function from react-query-kit.
 *
 * To use these queries in components:
 * 1. Import the query hook from this file
 * 2. Use the hook in your component with the appropriate variables
 *
 * Example:
 * ```
 * import { useAssetsQuery } from '@/hooks/api-queries';
 *
 * function MyComponent() {
 *   const { data: assets } = useAssetsQuery({
 *     variables: { publicKey: publicKey || '' },
 *     enabled: !!publicKey,
 *   });
 * }
 * ```
 */

// Assets queries
export const useAssetsQuery = createQuery<Awaited<ReturnType<typeof getAssetByAddress>>, { publicKey: string }>({
  queryKey: ['assets'],
  fetcher: ({ publicKey }) => getAssetByAddress(publicKey || '', '501'),
});

// Jupiter orders queries
export const useActiveLimitOrdersQuery = createQuery<
  Awaited<ReturnType<typeof fetchJupiterOrders>>,
  { publicKey: string }
>({
  queryKey: ['activeLimitOrders'],
  fetcher: ({ publicKey }) => fetchJupiterOrders(publicKey || '', 'active'),
});

export const useJupiterOrdersHistoryQuery = createQuery<
  Awaited<ReturnType<typeof fetchJupiterOrders>>,
  { publicKey: string }
>({
  queryKey: ['jupiter-transactions'],
  fetcher: ({ publicKey }) => fetchJupiterOrders(publicKey || '', 'history'),
});

// OKX transactions query
export const useOkxTransactionsQuery = createQuery<
  Awaited<ReturnType<typeof getOkxTransactions>>,
  { publicKey: string; tokenAddress: string }
>({
  queryKey: ['okx-transactions'],
  fetcher: ({ publicKey, tokenAddress }) => getOkxTransactions(publicKey || '', '501', tokenAddress),
});

// Token info queries
export const useTokenInfoQuery = createQuery<Awaited<ReturnType<typeof getTokenInfo>>, { address: string }>({
  queryKey: ['token'],
  fetcher: ({ address }) => getTokenInfo(address, '501'),
});

export const useTokenInfoWithOptionalAddressQuery = createQuery<
  Awaited<ReturnType<typeof getTokenInfo>> | undefined,
  { tokenAddress: string }
>({
  queryKey: ['token'],
  fetcher: ({ tokenAddress }) => (tokenAddress ? getTokenInfo(tokenAddress, '501') : Promise.resolve(undefined)),
});

export const useTokenOverviewQuery = createQuery<Awaited<ReturnType<typeof getTokenOverview>>, { address: string }>({
  queryKey: ['token-overview'],
  fetcher: ({ address }) => getTokenOverview(address, '501'),
});

// Suspect wallet balance query
export const useSuspectWalletBalanceQuery = createQuery<
  Array<{ timestamp: number; balance: number }>,
  { tokenAddress: string }
>({
  queryKey: ['suspect-wallet-balance'],
  fetcher: async ({ tokenAddress }) => {
    const response = await fetch(`/api/suspect-wallet-balance?token=${encodeURIComponent(tokenAddress)}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch suspect wallet balance: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data?.balance_history?.balance_points) {
      return data.data.balance_history.balance_points.sort((a: any, b: any) => a.timestamp - b.timestamp);
    }

    return [{ timestamp: 0, balance: 0 }];
  },
});

// Jupiter quote query
export const useJupiterQuoteQuery = createQuery<
  Awaited<ReturnType<typeof jupiterQuoteApi.quoteGet>>,
  {
    publicKey: string;
    activeTab: string;
    quoteAmount: string;
    selectedBaseToken: string;
    isSellMode: boolean;
    tokenAddress: string;
    tokenDecimals: number;
    slippages: Record<string, number>;
  }
>({
  queryKey: ['quote'],
  fetcher: ({ quoteAmount, selectedBaseToken, isSellMode, tokenAddress, tokenDecimals, slippages }) => {
    const inputMint = isSellMode ? tokenAddress : selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS;
    const outputMint = isSellMode ? (selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS) : tokenAddress;
    const decimals = isSellMode ? tokenDecimals : selectedBaseToken === 'SOL' ? 9 : 6; // SOL has 9 decimals, USDC has 6

    return jupiterQuoteApi.quoteGet({
      inputMint,
      outputMint,
      amount: Math.floor((+quoteAmount || 0) * 10 ** decimals),
      slippageBps: slippages[selectedBaseToken] || 100,
      platformFeeBps: TRADING_CONFIG.platformFeeBps,
    });
  },
});
