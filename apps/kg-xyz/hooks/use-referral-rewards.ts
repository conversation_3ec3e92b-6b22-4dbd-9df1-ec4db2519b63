import { createQuery } from 'react-query-kit';

interface ReferralResponse {
  code: number;
  data: ReferralData;
}

interface ReferralData {
  total_rewards: string;
  available_rewards: string;
  withdrawn_rewards: string;
}

// Create typed query for referral rewards
const useReferralRewardsQuery = createQuery<ReferralData | null, { token: string }>({
  queryKey: ['referralRewards'],
  fetcher: async ({ token }) => {
    if (!token) return null;
    const res = await fetch(`${process.env.NEXT_PUBLIC_KG_API_BASE_URL}/v1/referral/rewards`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const data: ReferralResponse = await res.json();
    return data.data;
  },
});

/**
 * Custom hook to fetch referral rewards from the API
 * @param options Query options including token and enabled state
 * @returns Query result containing referral rewards data
 */
export function useReferralRewards(options: { token?: string; isOpen: boolean }) {
  const { token, isOpen } = options;

  return useReferralRewardsQuery({
    variables: { token: token || '' },
    enabled: !!token && isOpen,
  });
}

// Export types for reuse
export type { ReferralData };
