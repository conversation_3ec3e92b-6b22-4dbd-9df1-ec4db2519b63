import { useEffect, useState } from 'react';

// @ts-ignore
import TW from '../tailwind.config';

// Tailwind breakpoints from the config
export const BREAKPOINTS = TW.default.theme.screens;
type BreakpointKey = keyof typeof BREAKPOINTS;

/**
 * Custom hook to detect if the current viewport matches a specific breakpoint condition
 * @param breakpoint - The breakpoint to check against (default: 'md')
 * @param type - The type of check to perform ('max' for smaller than, 'min' for larger than)
 * @returns boolean indicating if the condition is met
 */
export const useBreakpoint = (breakpoint: BreakpointKey = 'md', type: 'max' | 'min' = 'max'): boolean => {
  // Default to false for SSR
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') return;

    const breakpointValue = BREAKPOINTS[breakpoint];

    // Function to check if the current viewport matches the breakpoint condition
    const checkBreakpoint = () => {
      if (type === 'max') {
        // max-width: smaller than the breakpoint (mobile first)
        setMatches(window.innerWidth < parseInt(breakpointValue.replace('px', '')));
      } else {
        // min-width: larger than or equal to the breakpoint
        setMatches(window.innerWidth >= parseInt(breakpointValue.replace('px', '')));
      }
    };

    // Set initial value
    checkBreakpoint();

    // Add event listener for resize
    window.addEventListener('resize', checkBreakpoint);

    // Clean up
    return () => window.removeEventListener('resize', checkBreakpoint);
  }, [breakpoint, type]);

  return matches;
};

/**
 * Custom hook to detect if the current viewport is mobile based on tailwindcss breakpoints
 * Uses the md breakpoint (768px) from the tailwind config
 * @returns boolean true if viewport width is less than 768px (md breakpoint)
 */
export const useIsMobile = (): boolean => {
  return useBreakpoint('md', 'max');
};

/**
 * Custom hook to detect if the current viewport is tablet or larger
 * @returns boolean true if viewport width is at least 768px (md breakpoint)
 */
export const useIsTabletOrLarger = (): boolean => {
  return useBreakpoint('md', 'min');
};

/**
 * Custom hook to detect if the current viewport is desktop
 * @returns boolean true if viewport width is at least 1280px (lg breakpoint)
 */
export const useIsDesktop = (): boolean => {
  return useBreakpoint('lg', 'min');
};

export default useIsMobile;
