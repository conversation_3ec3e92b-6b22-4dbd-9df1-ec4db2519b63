{"signal-card": {"mcap": "MCap", "max-gain-after-alert": "<PERSON> After Alert", "max-gain-after-alert-desc": "This shows the highest gain after the signal was triggered.", "win-rate": "Win Rate", "buy": "Buy (SOL)", "sell": "<PERSON><PERSON> (SOL)", "trading": "Trading", "no-signal-detected": "No Signal Detected", "social": {"twitter": "Twitter", "telegram": "Telegram", "website": "Official Website", "share": "Share", "title": "Social Media", "no-social-media": "No social medias found."}, "price-since-signal": "Price Since Signal", "price-since-signal-desc": "This shows the price change since the signal was triggered.", "price-since-signal-detail": "A positive percentage indicates the token price has increased since the signal, while a negative percentage indicates the price has decreased.", "loading": "Loading...", "smart-money-holdings": "Smart Money Average Holdings", "smart-money-holdings-desc": "This shows how much of this token is currently held by Smart Money. Higher % means more confidence.", "smart-money-holdings-detail": "A higher percentage indicates stronger interest from experienced traders and investors. Values above 70% are highlighted as particularly significant.", "smart-money-win-rate": "Smart Money Past Win Rate", "smart-money-win-rate-desc": "This shows how often smart wallets made profitable trades in the past. A higher rate means more reliable trades.", "smart-money-win-rate-detail": "Win rates above 70% are highlighted as they indicate a strong track record of successful trading by smart money wallets.", "signal-duration": "Signal Duration", "signal-duration-desc": "This shows time duration after announced a signal.", "low-mcap-warning": "This token's market cap is below $30K. Please trade with extra caution.", "title": "Signal", "view-signal": "View Signal"}, "signal-banner": {"last-7d-2x-accuracy": "Last 7 Days 2x Signal Accuracy", "avg-max-gain": "Avg <PERSON> of 2x Signals", "todays-top-signal": "Today's Top Signal"}, "market-stats": {"title": "Market Stats", "token-info": "Token Info", "market-cap": "Market Cap", "24h-volume": "24h Volume", "liquidity": "Liquidity", "circulating-supply": "Circulating Supply", "holders": "Holders", "top-10-holders": "Top 10 Holders %"}, "top-signals": {"too-late-now": "Too late now", "recent-top-performing": "Recent Top 10 Performing Signals 🚀"}, "signal-tooltip": {"smart-money-holdings": "Smart Money Average Holdings", "price-since-signal": "Price Since Signal", "smart-money-win-rate": "Smart Money Past Win Rate", "signal-actions": {"bigBuy": {"actionText": "Strong Buy", "message": "Significant smart money holdings with minimal recent price increase. This could be a good entry point!"}, "smallBuy": {"actionText": "Strategic Entry", "message": "Smart money shows interest, but the price has already gone up. Consider a small position to test the waters."}, "smallSell": {"actionText": "Reduce Position", "message": "Smart money is reducing holdings, and the price has risen significantly. Recommend taking profits gradually."}, "bigSell": {"actionText": "Smart Money Exit", "message": "Smart money holdings have dropped sharply, and the price is already high. If you're still holding, it's advisable to exit soon to manage risk."}}}, "token-page": {"tabs": {"charts": "Charts", "info": "Info", "buy": "Buy", "my-position": "My Position", "token-info": "Token Info", "1hr-stats": "1hr Stats", "my-holdings": "My Holdings", "my-activities": "My Activities", "limit-orders": "Limit Orders"}}, "search": {"search": "Search", "search-placeholder": "Search token name or CA", "search-input-placeholder": "Search token name or CA...", "paste": "Paste", "esc": "ESC", "close-search": "Close search", "loading": "Loading...", "no-results": "No results found.", "search-results": "Search Results"}, "trading-section": {"trade": "Trade", "buy": "Buy", "sell": "<PERSON>ll", "buy-now": "Buy Now", "buy-dip": "Buy Dip", "sell-now": "Sell Now", "auto-sell": "Auto Sell", "sell-at-roi": "Sell at ROI", "sell-at-price": "Sell at Price", "buy-at-price": "Buy at Price", "amount": "Amount", "quote": "Quote", "current-price": "Current Price", "processing": "Processing...", "buy-token": "Buy {token}", "sell-token": "Sell {token} {timing}", "roi-above-target": "Your ROI is above target.", "sell-now-or-raise-roi": "Sell now to secure gains or raise your Auto Sell ROI.", "tx-submitted": "Transaction Submitted", "tx-confirmed": "Transaction Confirmed", "tx-failed": "Transaction Failed", "set-default-buy-amount": "Set Default Buy Amount", "connect-wallet": "Connect Wallet", "order-submitted": "Order Submitted", "order-confirmed": "Order Confirmed", "order-failed": "Order Failed", "tx-hash": "Tx Hash:", "confirmed-message": "Your transaction has been confirmed on the blockchain.", "failed-message": "Your transaction failed to confirm on the blockchain.", "current-roi": "Current ROI", "immediately": "Immediately", "estimated-sell-price": "Estimated <PERSON><PERSON>", "place-limit-order": "Place Limit Order", "request-error": "Request Error", "receive-token-type": "Receive Token Type", "balance": "Balance"}, "buy-button": {"buy": "Buy {amount} SOL", "confirming": "Confirming..."}, "homepage": {"dont-miss-out": "Don't miss out this time", "latest-hot-signals": "Latest Hot Signals 🔥"}, "bottom-banner": {"tutorial": "Tutorial", "get-instant-signal": "Get Instant Signal", "app": "App"}, "trading-stats-panel": {"unrealized-roi": "Unrealized ROI", "set-auto-sell": "Set Auto Sell", "no-tokens-found": "No tokens found in your wallet", "no-limit-orders-found": "No active limit orders found", "tab-holdings": "My Holdings", "tab-limit-orders": "Limit Orders", "no-holding": "You do not hold this token at the moment.", "my-holdings": "My Holdings", "smart-money-exit": "🚨 Smart Money Exit", "reduce-position": "Reduce Position", "balance-usd": "Value (USD)", "unrealized-pnl": "Unrealized PNL(USD)", "sell": "<PERSON>ll", "wallet-not-connected": "You haven't connect your wallet yet", "connect-wallet": "Connect Wallet", "total-value": "Total Value", "total-unrealized-pnl": "Total Unrealized PNL"}, "token-item": {"roi": "ROI", "trading": "Trading"}, "trading-info": {"tab-title": "1hr Stats", "transactions": "Transactions (1hr)", "turnover": "Turnover (1hr)", "traders": "Traders (1hr)", "buy": "Buy", "sell": "<PERSON>ll", "buyers": "Buyers", "sellers": "Sellers"}, "token-info": {"about": "About"}, "my-activities": {"my-activities": "My Activities", "limit-orders": "Limit Orders", "my-balance": "Current Value (USD)", "total-pnl": "Total PnL", "type-time": "Type / Time", "total-usd": "Total USD", "amount": "Amount", "price": "Price", "total-profit": "Total Profit", "buy": "Buy", "sell": "<PERSON>ll", "base-token": "Base Token", "limit-order": "Limit", "partial-fill": "Partial Fill"}, "limit-orders": {"title": "Limit Orders", "loading": "Loading limit orders...", "no-orders": "No active limit orders found", "type-time": "Type / Created Time", "total-usd": "Total USD", "amount": "Amount", "price": "Price", "cancel-order": "Cancel Order", "limit-buy": "Limit / Buy", "limit-sell": "Limit / Sell", "cancel": "Cancel", "request-error": "Request Error", "jupiter-swap-error": "Jupiter Swap Error", "partial-fill": "Partial Fill"}, "wallet-connect-modal": {"connect-wallet": "Connect a Wallet", "installed": "Installed", "supported": "Supported", "powered-by": "POWERED BY", "get-best-experience": "Get the best experience", "download-description": "Download the KryptoGO app for instant signals and seamless trading", "download-button": "Download KryptoGO App", "trade-button": "Trade in KryptoGO App"}, "header": {"explore": "Explore", "connect": "Connect", "wrong-network": "Wrong network", "wallet-icon": "Wallet icon", "tutorial": "Tutorial", "get-instant-signal": "Get instant signal", "app": "App"}, "suspect-insider": {"analysis": "Suspect Insider Analysis", "new-tag": "NEW", "analyze-cta": "Analyze Suspect Insider Holdings with One Click", "analysis-cost": "Each analysis costs 1 credit.", "analyzing": "Analyzing...", "analyzing-wait": "It may takes 1-2 mins to analyze", "analyzing-notice": "Please do not close this page.", "analyzing-error": "Analysis failed", "try-it-out": "Try It Out", "credits-left": "{credits} credits left", "remaining-balance": "Remaining Balance", "suspect-insider-360": "Suspect Insider (360)", "high": "High", "extremely-high": "Extremely High", "credits-title": "How to get credits?", "credits-description": "Trade volume 500 USD, will get 1 credit", "wallet-required": "Wallet connection required", "no-data": "No Data", "error-title": "Error", "sign-message": "Please sign the message to start analysis", "holdings": "Holdings", "total-holders": "Total Holders", "holdings-distribution": "Holdings Distribution", "remaining-solana-balance": "Suspect Insiders' Remaining SOL Balance", "network-error": "Network Error", "categories": {"suspect-insiders": "Suspect Insiders", "kol-traders": "KOL Traders", "individual-traders": "Individual Traders", "liquidity-pools": "Liquidity Pools", "cex": "CEX", "others": "Others"}}, "feature-popup": {"title": "Mixed Battlefield", "description": "Monitor multiple platforms simultaneously, never miss any golden dogs!", "next-button": "Next", "back-button": "Back", "done-button": "Done", "dont-show-again": "Don't show again", "page1-title": "Real-Time Alpha Signals", "page1-description": "We sift through the noise to deliver high-potential meme coins, so you can catch the wave with confidence.", "page2-title": "Whale Insider Analysis", "page2-description": "Trade like a top trader with just a 1-click analysis of whale insider addresses.", "page3-title": "Crystal-Clear Trade History", "page3-description": "Track every swap and limit order with diamond-hand clarity for data-driven decisions.", "page4-title": "Ultimate App Trading Experience", "page4-description": "Download KryptoGO for a silky-smooth trading ecosystem—every move, signal, and swap crafted for degens who demand the best.", "page5-title": "Instant Signal Alerts", "page5-description": "Get Telegram pings to stay ahead of the meme coin game—never miss a hot signal.", "page6-title": "Multi-Wallet Support", "page6-description": "Plug in your favorite wallets and stack sats with a smooth, secure trading vibe."}, "credit-system": {"available-credits": "Available Credits", "ways-to-get-credits": "Ways to get credits", "daily-free-1-credit": "Daily Free 1-credit", "daily-free-description": "Claim your free daily credit", "tx-volume": "Tx Volume", "tx-volume-description": "200 USD = Get 1 credit", "usd-left": "USD left", "instant-purchase": "Instant Purchase", "credits": "credits", "contact-customer-support": "Contact Customer Support", "support-description": "Drop a message in the group to claim 5 free credits.", "claim": "<PERSON><PERSON><PERSON>", "claiming": "Claiming...", "buy": "Buy", "processing": "Processing...", "loading": "...", "contact": "Contact", "daily-credit-claimed": "Daily Credit Claimed", "daily-credit-success": "You have successfully claimed your daily credit!", "daily-credit-error": "Failed to claim daily credit. Please try again.", "purchase-initiated": "Purchase Initiated", "purchase-description": "{credits} credits for {amount} SOL", "purchase-error": "Failed to purchase credits. Please try again.", "error": "Error"}}