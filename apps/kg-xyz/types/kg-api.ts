export interface SignalResponse {
  token_address: string;
  smart_wallet_count: number;
  buy_entry_price: number;
  emit_time: number;
  telegram_link: string;
  win_rate: number;
  average_win_rate: number;
  average_holding: number;
  highest_price: number;
  buy_entry_time?: number;
}

export type SellSignalResponse = Pick<
  SignalResponse,
  'token_address' | 'emit_time' | 'telegram_link' | 'buy_entry_time'
>;

export interface SignalStatusResponse {
  past_top_signals: {
    token_address: string;
    highest_gain: number;
  }[];
  last_7d_2x_ratio: number;
  last_7d_2x_avg_gain: number;
  today_top_signal: {
    token_address: string;
    highest_gain: number;
  };
}
