/**
 * Type definition for Jupiter orders API response
 */
export type JupiterOrdersResponse = {
  orders: JupiterLimitedOrder[];
  orderStatus: string;
  user: string;
  page: number;
  totalItems: number;
  totalPages: number;
};

/**
 * Type definition for Jupiter orders cache metadata
 */
export type JupiterOrdersCacheMetadata = {
  totalPages: number;
  lastUpdated: number;
  user: string;
  orderStatus: string;
  totalItemsLength: number;
};
export interface JupiterLimitedOrder {
  userPubkey: string;
  orderKey: string;
  inputMint: string;
  outputMint: string;
  makingAmount: string;
  takingAmount: string;
  remainingMakingAmount: string;
  remainingTakingAmount: string;
  rawMakingAmount: string;
  rawTakingAmount: string;
  rawRemainingMakingAmount: string;
  rawRemainingTakingAmount: string;
  slippageBps: string;
  expiredAt: null;
  createdAt: Date;
  updatedAt: Date;
  status: string;
  openTx: string;
  closeTx: string;
  programVersion: string;
  trades: JupiterInternalTrade[];
}

export interface JupiterInternalTrade {
  orderKey: string;
  keeper: string;
  inputMint: string;
  outputMint: string;
  inputAmount: string;
  outputAmount: string;
  rawInputAmount: string;
  rawOutputAmount: string;
  feeMint: string;
  feeAmount: string;
  rawFeeAmount: string;
  txId: string;
  confirmedAt: Date;
  action: string;
  productMeta: null;
}
