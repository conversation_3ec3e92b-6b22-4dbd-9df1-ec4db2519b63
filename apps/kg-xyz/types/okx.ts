import type { JupiterInternalTrade } from './jupiter';

export interface OKXTokenSearchResponse {
  code: string;
  msg: string;
  data: OKXSearchTokensResult;
}

export type OKXSearchTokensResult = SearchedToken[];

export interface SearchedToken {
  chainId: string;
  chainLogoUrl: string;
  chainName: string;
  change: string;
  collectionToken: string;
  decimal: string;
  explorerUrl: string;
  holderAmount: string;
  isCustomToken: string;
  isNativeToken: string;
  isSubscribe: string;
  liquidity: string;
  marketCap: string;
  matchType: string;
  price: string;
  quoteLiquidity: string;
  tagList: string[][];
  tokenContractAddress: string;
  tokenLogoUrl: string;
  tokenName: string;
  tokenSupportTradeModeVO: {
    supportMemeMode: string;
  };
  tokenSymbol: string;
  volume: string;
}

export interface OKXCandleResponse {
  code: string;
  msg: string;
  data: OKXCandleResult;
}

export type OKXCandleResult = [
  timestamp: string,
  open: string,
  high: string,
  low: string,
  close: string,
  volume: string,
  volumeUsd: string,
  confirm: string,
][];

export interface OKXTokenDetailResponse {
  code: number;
  data: OKXTokenDetail;
  detailMsg: string;
  error_code: string;
  error_message: string;
  msg: string;
}

export interface OKXTokenDetail {
  chainBWLogoUrl: string;
  chainLogoUrl: string;
  chainName: string;
  change: string;
  change1H: string;
  change4H: string;
  change5M: string;
  circulatingSupply: string;
  dappList: any[];
  holders: string;
  isCollected: string;
  isNotSupportTxNativeToken: string;
  isSubscribe: string;
  isSupportBlinksShareUrl: string;
  isSupportHolder: string;
  isSupportMarketCapKline: string;
  isTxPrice: string;
  liquidity: string;
  marketCap: string;
  maxPrice: string;
  minPrice: string;
  moduleType: string;
  price: string;
  riskLevel: string;
  supportLimitOrder: string;
  supportMemeMode: string;
  supportSingleChainSwap: string;
  supportSwap: string;
  tagList: string[][];
  tokenContractAddress: string;
  tokenLogoUrl: string;
  tokenName: string;
  tokenSymbol: string;
  top10HoldAmountPercentage: string;
  tradeNum: string;
  transactionNum: string;
  volume: string;
  wrapperTokenContractAddress: string;
}

export interface OKXTokenOverview {
  basicInfo: BasicInfo;
  learnMore: LearnMore;
  marketInfo: MarketInfo;
  memeInfo: MemeInfo;
  socialMedia: SocialMedia;
  tagList: Array<string[]>;
  tokenTagVO: TokenTagVO;
  tokenThirdPartInfo: TokenThirdPartInfo;
}

export interface MemeInfo {
  createTime: string;
  creatorAddress: string;
  hotSpotTagList: HotSpotTagList[];
  progress: string;
  tokenSymbol: string;
  transactions: string;
  volume: string;
}

export interface HotSpotTagList {
  linkUrl: string;
  logoUrl: string;
  moduleTagName: string;
  moduleType: string;
}

export interface BasicInfo {
  chainLogoUrl: string;
  chainName: string;
  isMeme: string;
  isNativeToken: string;
  isNotSupportTxNativeToken: string;
  tokenContractAddress: string;
}

export interface LearnMore {
  explorer: string;
  officialWebSite: string;
  whitePaper: string;
}

export interface MarketInfo {
  burntLiquidity: string;
  circulatingSupply: string;
  holders: string;
  marketCap: string;
  maxSupply: string;
  priceChange1H: string;
  priceChange24H: string;
  priceChange4H: string;
  priceChange5M: string;
  riskLevel: string;
  snipersClear: string;
  snipersTotal: string;
  suspiciousRatio: string;
  totalLiquidity: string;
}

export interface SocialMedia {
  description: string;
  discord: string;
  facebook: string;
  github: string;
  medium: string;
  officialWebsite: string;
  reddit: string;
  telegram: string;
  twitter: string;
}

export interface TokenTagVO {
  snipersTotal: CommunityRecognized;
  communityRecognized: CommunityRecognized;
  suspiciousHoldingRatio: CommunityRecognized;
  devHoldingRatio: CommunityRecognized;
  snipersClear: CommunityRecognized;
  devHoldingStatus: CommunityRecognized;
}

export interface CommunityRecognized {
  tagExt: null;
  tagType: string;
  tagValue: string;
}

export interface TokenThirdPartInfo {
  bubbleMapsUrl: string;
}

export interface OKXMarketInfo {
  chainId: string;
  change: string;
  change1H: string;
  change4H: string;
  change5M: string;
  maxPrice: string;
  minPrice: string;
  tokenContractAddress: string;
  tradeNum: string;
  tradeNum1H: string;
  tradeNum4H: string;
  tradeNum5M: string;
  tradeNumBuy: string;
  tradeNumBuy1H: string;
  tradeNumBuy4H: string;
  tradeNumBuy5M: string;
  tradeNumSell: string;
  tradeNumSell1H: string;
  tradeNumSell4H: string;
  tradeNumSell5M: string;
  txs: string;
  txs1H: string;
  txs4H: string;
  txs5M: string;
  txsBuy: string;
  txsBuy1H: string;
  txsBuy4H: string;
  txsBuy5M: string;
  txsSell: string;
  txsSell1H: string;
  txsSell4H: string;
  txsSell5M: string;
  uniqueTraders: string;
  uniqueTraders1H: string;
  uniqueTraders4H: string;
  uniqueTraders5M: string;
  uniqueTradersBuy: string;
  uniqueTradersBuy1H: string;
  uniqueTradersBuy4H: string;
  uniqueTradersBuy5M: string;
  uniqueTradersSell: string;
  uniqueTradersSell1H: string;
  uniqueTradersSell4H: string;
  uniqueTradersSell5M: string;
  volume: string;
  volume1H: string;
  volume4H: string;
  volume5M: string;
  volumeBuy: string;
  volumeBuy1H: string;
  volumeBuy4H: string;
  volumeBuy5M: string;
  volumeSell: string;
  volumeSell1H: string;
  volumeSell4H: string;
  volumeSell5M: string;
}

export interface OKXAssetData {
  chainIndex: string;
  symbol: string;
  balance: string;
  tokenPrice: string;
  isRiskToken: boolean;
  rawBalance: string;
  address: string;
  tokenContractAddress: string;
}

export interface OKXRecentTransaction {
  hasNext: boolean;
  isHistorySupported: boolean;
  transactions: OKXTransaction[];
}

export interface OKXTransaction {
  amount: string;
  blockHeight: number;
  blockTime: number;
  chainId: number;
  flag: boolean;
  globalIndex: string;
  id: number;
  price: string;
  singleRealizedProfit: string;
  turnover: string;
  txHash: string;
  txHashUrl: string;
  type: number;
  walletAddress: string;
  internalTrading?: JupiterInternalTrade[];
  isLimitedOrder?: boolean;
  isPartialFill?: boolean;
  internalTradingFill?: string;
}
