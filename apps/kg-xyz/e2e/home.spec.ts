import { test, expect } from '@playwright/test';

test.describe('Home Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page before each test
    await page.goto('/');
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
  });

  test('should display the correct header and navigation elements', async ({ page }) => {
    // Check if the header is visible
    const header = page.locator('header');
    await expect(header).toBeVisible();

    // Check if the logo is visible in the header - using a more flexible selector
    const logo = page.locator('header').getByRole('img').first();
    await expect(logo).toBeVisible();
  });

  test('should display main content sections', async ({ page }) => {
    // Check if the main content area is visible
    const mainContent = page.locator('main').first();
    await expect(mainContent).toBeVisible({ timeout: 10000 });

    // Check for a heading in the main content - using a more generic approach
    const anyHeading = page.locator('main h1, main h2, main h3').first();
    await expect(anyHeading).toBeVisible({ timeout: 5000 });

    // Check for any button in the main content area
    const anyButton = page.locator('main button').first();
    await expect(anyButton).toBeVisible({ timeout: 5000 });
  });

  test('should display card components when available', async ({ page }) => {
    // Wait for the page to be fully loaded with a reasonable timeout
    await page.waitForTimeout(2000);

    // Look for any card-like elements using common class patterns
    const cardElements = page.locator('div.rounded-lg, div.border, div.shadow');

    // Skip the test if no card elements are found
    if ((await cardElements.count()) === 0) {
      test.skip();
      console.log('No card elements found on the page');
      return;
    }

    // If we reach here, card elements should be visible
    await expect(cardElements.first()).toBeVisible();

    // Check for interactive elements inside cards
    const interactiveElements = page.locator('div.rounded-lg button, div.border button, div.shadow button');
    if ((await interactiveElements.count()) > 0) {
      await expect(interactiveElements.first()).toBeVisible();
    }
  });
});
