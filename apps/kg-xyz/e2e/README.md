# End-to-End Testing with <PERSON><PERSON>

This directory contains end-to-end tests for the KryptoGO XYZ application using Playwright.

## Test Files

- `home.spec.ts`: Tests for the home page functionality
- `token.spec.ts`: Tests for the token detail page functionality
- `search.spec.ts`: Tests for the search component functionality
- `components.spec.ts`: Tests for various UI components

## Running Tests

You can run the tests using the following npm scripts:

```bash
# Run all tests
pnpm test:e2e

# Run tests with UI mode (visual test runner)
pnpm test:e2e:ui

# Run tests in debug mode
pnpm test:e2e:debug
```

## Testing with Different Base URLs

There are several ways to test with different base URLs:

### 1. Using Environment Variables

```bash
# Test against a staging environment
BASE_URL=https://staging.kryptogo.xyz pnpm test:e2e

# Test against production
BASE_URL=https://kryptogo.xyz pnpm test:e2e
```

### 2. Using Project-Specific Configurations

The Playwright config includes multiple projects with different baseURLs:

```bash
# Test against local environment
pnpm playwright test --project=chromium-local

# Test against staging environment
pnpm playwright test --project=chromium-staging

# Test against production environment
pnpm playwright test --project=chromium-production
```

### 3. Using the Helper Script

We've created a helper script to make it easier to run tests with different base URLs:

```bash
# Run all tests against staging
./scripts/test-with-baseurl.sh staging

# Run a specific test file against production
./scripts/test-with-baseurl.sh production e2e/home.spec.ts
```

## Test Configuration

The Playwright configuration is in `playwright.config.ts` at the root of the project. It includes:

- Multiple browser configurations (Chromium, Firefox, Safari)
- Mobile device testing configurations
- Automatic web server startup for testing

## Writing New Tests

When writing new tests, follow these guidelines:

1. Create a new file with the `.spec.ts` extension in the `e2e` directory
2. Use descriptive test names that explain what is being tested
3. Group related tests using `test.describe()`
4. Use page objects or component selectors that are resilient to UI changes
5. Add appropriate assertions to verify expected behavior

## CI Integration

These tests can be integrated into your CI pipeline by running:

```bash
pnpm test:e2e
```

## Debugging Failed Tests

When tests fail, Playwright automatically captures:

- Screenshots of the failure
- Full page HTML at the time of failure
- Browser console logs

You can find these artifacts in the `test-results` directory after running the tests.
