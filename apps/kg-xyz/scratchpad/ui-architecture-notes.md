# UI/UX Architecture Notes

## Design System
- Dark theme by default
- Color palette:
  - Background: Dark (near black)
  - Accent: Purple (#7C3AED for buttons/CTAs)
  - Text: White/Gray scale
  - Success: Green (for positive % changes)
  - Error: Red (for negative % changes)
  - Gold/Yellow for special features

## Layout Structure
1. Global Components
   - Navigation bar (sticky)
   - Search bar (global token/address search)
   - User menu (profile, notifications, etc)
   - Modal system for dialogs

2. Page-Specific Layouts
   - Home: Centered search with minimal UI
   - Wallet: Grid layout with token balances
   - Explore: Card-based grid with categories
   - Profile: User stats + token list

## Component Architecture
1. Core Components (@kryptogo/2b)
   - Button variants (primary, secondary)
   - Input fields
   - Cards
   - Tables
   - Modal/Dialog
   - Icons

2. Feature Components
   - TokenCard
   - PriceDisplay (with % change)
   - AddressDisplay (with copy)
   - QRCode
   - Charts/Graphs

## Data Requirements
1. Real-time Data
   - Token prices
   - Wallet balances
   - Transaction status

2. Static Data
   - Token metadata
   - User profile
   - App configurations

## Technical Implementation
1. Framework
   - Next.js 13+ with App Router
   - TypeScript for type safety
   - Tailwind + shadcn/ui for styling
   - @kryptogo/2b for shared components

2. State Management
   - Server components for static data
   - Client components for interactive elements
   - Real-time updates via WebSocket

3. Performance Considerations
   - Image optimization
   - Code splitting
   - Progressive loading
   - Caching strategies

## Accessibility & UX
1. Core Features
   - Mobile responsive
   - Dark theme optimized
   - Copy to clipboard
   - QR code generation
   - Toast notifications

2. User Flows
   - Wallet connection
   - Token search/selection
   - Transaction flow
   - Profile management

## Search Implementation

### Requirements
1. Search Functionality
   - Global search bar in navigation
   - Main search on homepage
   - Both use same search logic
   - Prefix-matching suggestions for tokens
   - Simple address validation using `isAddress`

2. Error Handling
```typescript
const searchErrors = {
  INVALID_ADDRESS: 'Invalid contract address. Please check and try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.'
};
```

3. Analytics Integration
```typescript
// Google Analytics Events
const trackingEvents = {
  SEARCH_ATTEMPT: 'search_attempt',
  SEARCH_SUCCESS: 'search_success',
  SEARCH_ERROR: 'search_error'
};
```

### Search Component Structure
```typescript
interface SearchProps {
  placeholder?: string;
  onSearch: (value: string) => void;
  onSuggestionSelect?: (suggestion: string) => void;
}

interface SearchState {
  value: string;
  suggestions: string[];
  error: string | null;
  isLoading: boolean;
}
```

## Search Behavior Flow

### 1. Search Input Handling
```typescript
interface SearchHandlers {
  onSearch: (value: string) => void;
  onInputChange: (value: string) => void;
  onSuggestionSelect: (suggestion: string) => void;
}
```

### 2. Validation & Processing
```typescript
const validateInput = (input: string) => {
  // For addresses
  if (input.startsWith('0x')) {
    return isAddress(input);
  }
  // For token names/symbols
  return true;
};
```

### 3. Navigation Flow
```typescript
const handleSearch = async (value: string) => {
  // 1. Validate input
  const isValid = validateInput(value);
  if (!isValid) {
    setError('Invalid address format');
    return;
  }

  // 2. Track search attempt
  gtag('event', 'search_attempt', {
    search_term: value
  });

  // 3. Navigate based on input type
  if (isAddress(value)) {
    router.push(`/token/${value}`);
  } else {
    router.push(`/search?q=${encodeURIComponent(value)}`);
  }
};
```

### 4. Page Components
```typescript
// app/token/[address]/page.tsx
interface TokenPageProps {
  params: {
    address: string;
  };
}

// app/search/page.tsx
interface SearchPageProps {
  searchParams: {
    q: string;
  };
}
```

### 5. Data Fetching
```typescript
// Token data fetching
const fetchTokenData = async (address: string) => {
  const response = await fetch(`/api/token/${address}`);
  return response.json();
};

// Search results fetching
const fetchSearchResults = async (query: string) => {
  const response = await fetch(`/api/search?q=${query}`);
  return response.json();
};
```

### 6. Loading States
```typescript
interface LoadingStates {
  isValidating: boolean;
  isLoading: boolean;
  error: string | null;
}

// Using React Suspense
<Suspense fallback={<TokenPageSkeleton />}>
  <TokenPageContent address={address} />
</Suspense>
```

### API Integration
1. Token Data API
   - Endpoint: `/api/token/[address]`
   - Returns: Token details, price, volume, etc.
   - Uses server-side caching

2. Search API
   - Endpoint: `/api/search`
   - Returns: Token list matching query
   - Supports pagination and filtering

3. Error Handling
```typescript
interface APIError {
  code: string;
  message: string;
  details?: unknown;
}

const errorCodes = {
  TOKEN_NOT_FOUND: 'token_not_found',
  INVALID_ADDRESS: 'invalid_address',
  RATE_LIMIT: 'rate_limit',
  NETWORK_ERROR: 'network_error'
} as const;
```

## UI/UX Decisions
1. Search Interactions
   - No additional features (QR code/scan)
   - No animations required
   - No keyboard shortcuts needed

2. Error Display
   - Inline error messages
   - Toast notifications for system errors
   - Clear validation feedback

3. Performance Considerations
   - Client-side address validation
   - No search history caching
   - Immediate feedback for invalid inputs

## SDK Integration Notes

### Core Setup
```typescript
// Provider Setup
import { KryptogoKitProvider, darkTheme } from '@kryptogo/kryptogokit-sdk-react';

const App = ({ children }) => (
  <KryptogoKitProvider
    clientId={process.env.NEXT_PUBLIC_KRYPTOGO_CLIENT_ID}
    mode="payment"
    theme={darkTheme()}
  >
    {children}
  </KryptogoKitProvider>
);
```

### Authentication Flow
1. User clicks "Login with KryptoGO"
2. ConnectModal opens
   - Shows ConnectOptions (mobile/desktop variants)
   - Handles wallet connection states
3. After connection:
   - Initializes auth with method (google/email/phone)
   - Performs asyncLogin
   - Retrieves user info
   - Stores access token and UID
4. WebSocket authentication for real-time updates

### State Management
```typescript
// Authentication States
type ConnectionStatus =
  | 'disconnected'    // Initial state
  | 'connecting'      // During connection
  | 'connected'       // Wallet connected
  | 'unauthenticated' // Needs authentication

// Storage Keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'kryptogo_auth_access_token',
  USER_UID: 'kryptogo_user_uid'
};
```

### Error Handling
- Authentication failures
- Connection issues
- Token validation
- Network errors

### Available Hooks
```typescript
// Modal Management
const { open: openConnectModal } = useConnectModal();
const { open: openAccountModal } = useAccountModal();
const { open: openChainModal } = useChainModal();

// Payment Processing
const { initiatePayment } = usePayment();

// Transaction Management
const { addTransaction } = useAddRecentTransaction();
```

## Logged-in State UI

### Navigation Changes
1. Login Button Replacement
   - Replaced "Login with KryptoGO" with connected address
   - Address shown in pill-style button (e.g., "kordan.eth")
   - Background color: gold/yellow (#F6C549)

2. Header Components
```typescript
interface HeaderProps {
  navigation: {
    items: Array<{
      name: string;
      href: string;
      current: boolean;
    }>;
  };
  searchBar: {
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
  };
  userMenu: {
    address?: string;
    ensName?: string;
    avatarUrl?: string;
  };
}
```

3. Utility Icons
```typescript
interface UtilityIcons {
  download: {
    onClick: () => void;
    tooltip: string;
  };
  gift: {
    onClick: () => void;
    tooltip: string;
    hasNewItem?: boolean;
  };
  settings: {
    onClick: () => void;
    tooltip: string;
  };
  profile: {
    onClick: () => void;
    tooltip: string;
    isConnected: boolean;
  };
}
```

### State Management
```typescript
interface AppState {
  user: {
    address: string;
    ensName?: string;
    isConnected: boolean;
  };
  navigation: {
    currentPath: string;
    searchValue: string;
  };
  theme: {
    isDarkMode: boolean;
  };
}
```

### Component Behavior
1. Address Display
   - Show ENS name if available
   - Truncate long addresses
   - Hover state shows full address
   - Click opens account modal

2. Search Bar
   - Persistent across all pages
   - Maintains value during navigation
   - Same functionality as homepage search

3. Utility Icons
   - Download: App download options
   - Gift: Rewards/referral program
   - Settings: Theme, language, network
   - Profile: Account management

## Next Steps
1. Setup Phase
   - Initialize Next.js with TypeScript
   - Configure Tailwind + shadcn/ui
   - Set up @kryptogo/2b integration
   - Create base layout structure

2. Component Development
   - Build core components
   - Implement layout structure
   - Add interactive features
   - Connect to backend services

3. Testing & Optimization
   - Component testing
   - E2E testing
   - Performance optimization
   - Cross-browser testing
