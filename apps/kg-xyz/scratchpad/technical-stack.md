# Technical Stack & Implementation Notes

## Core SDK Integration (@kryptogo/kryptogokit-sdk-react)

### 1. Base Components
- `KryptogoKitProvider` - Core provider for wallet integration
- `ConnectButton` - Pre-built wallet connection UI
- Wallet connectors for multiple wallets:
  - WalletConnect
  - Coinbase Wallet
  - KryptoGO Wallet
  - OKX Wallet
  - Injected wallets (MetaMask)

### 2. Technical Setup

## Project Structure

```
apps/kg-xyz/
├── app/              # Next.js App Router structure
│   ├── layout.tsx    # Root layout
│   ├── page.tsx      # Home page
│   ├── providers.tsx # Client-side providers
│   └── (routes)/     # Route groups
├── components/       # Reusable UI components
│   ├── ui/          # shadcn/ui components
│   └── shared/      # Custom shared components
├── lib/             # Utility functions and configurations
├── styles/          # Global styles and themes
└── types/           # TypeScript type definitions
```

## Technical Setup (App Router)

```typescript
// app/providers.tsx
'use client'

import {
  KryptogoKitProvider,
  connectorsForWallets,
  kryptogoConnector
} from '@kryptogo/kryptogokit-sdk-react';

export function Providers({ children }: { children: React.ReactNode }) {
  const connectors = connectorsForWallets([
    {
      groupName: 'Recommended',
      wallets: [
        walletConnectWallet,
        coinbaseWallet,
        kryptogoWallet
      ]
    }
  ]);

  return (
    <KryptogoKitProvider
      clientId={process.env.NEXT_PUBLIC_KRYPTOGO_CLIENT_ID}
      projectId={process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID}
    >
      {children}
    </KryptogoKitProvider>
  );
}

// app/layout.tsx
import { Providers } from './providers'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
```

### 3. Features Available
- Multi-chain support
- i18n support (5+ languages)
- Theming system (light/dark mode)
- TypeScript support

### 4. Integration Points
- Wagmi hooks for blockchain interactions
- Viem for Ethereum interactions
- React Query for data management

## SDK Configuration Requirements

1. Environment Variables
```env
NEXT_PUBLIC_KRYPTOGO_CLIENT_ID=     # Required: KryptoGO client ID
NEXT_PUBLIC_KG_ACCOUNT_BASE_URL=    # Optional: Custom account service URL
```

2. Dependencies
```json
{
  "dependencies": {
    "@kryptogo/kryptogokit-sdk-react": "latest",
    "@kryptogo/auth": "latest",
    "wagmi": "^1.0.0",
    "viem": "^1.0.0"
  }
}
```

3. Provider Configuration
```typescript
interface SDKConfig {
  mode: 'dapp' | 'payment';  // Default: payment
  theme?: {
    lightMode: ThemeVars;
    darkMode: ThemeVars;
  };
  showRecentTransactions?: boolean;
  locale?: string;          // i18n support
}
```

4. Authentication Methods
- KryptoGO Wallet
- WalletConnect
- Coinbase Wallet
- Injected wallets (MetaMask)
- Web2 methods:
  - Google OAuth
  - Email
  - Phone

5. Network Support
- Multiple chain support
- RPC configuration
- Network switching
- Transaction handling

## UI Framework
- NextJS
- shadcn/ui
- TailwindCSS

## Development Notes
1. Use Server Components by default
2. Client Components when needed (marked with 'use client')
3. Leverage React Server Components for:
   - Data fetching
   - Static content
   - SEO optimization
4. Use Client Components for:
   - Wallet interactions
   - Interactive UI elements
   - State management
5. Keep blockchain interactions through SDK hooks in client components

## TODO
- [ ] Set up environment variables
- [ ] Configure chains and RPC endpoints
- [ ] Implement wallet connection flow
- [ ] Set up theme configuration
- [ ] Configure i18n
