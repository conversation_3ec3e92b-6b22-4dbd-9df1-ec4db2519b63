import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const NETWORK_TO_CHAIN_ID: Record<string, string> = {
  eth: '1',
  sol: '501',
  // Add more mappings as needed
};

// Allowed origins for CORS
const allowedOrigins = [
  'https://www-dev.kryptogo.xyz',
  'https://app-dev.kryptogo.xyz',
  'https://www.kryptogo.xyz',
  'https://app.kryptogo.xyz',
  // Add more origins as needed
];

// Add development origins if not in production
if (process.env.NODE_ENV !== 'production') {
  allowedOrigins.push('http://localhost:3000');
}

export function middleware(request: NextRequest) {
  // Handle CORS
  const response = handleCORS(request);
  if (response) return response;

  // Handle network to chain ID mapping
  const path = request.nextUrl.pathname;
  const networkMatch = path.match(/^\/token\/([\w]+)\//);

  if (networkMatch) {
    const network = networkMatch[1];
    const chainId = NETWORK_TO_CHAIN_ID[network];

    if (chainId) {
      // Replace network name with chainId in the path
      const newPath = path.replace(`/token/${network}/`, `/token/${chainId}/`);
      const rewriteResponse = NextResponse.rewrite(new URL(newPath, request.url));

      // Add CORS headers to the rewrite response
      addCORSHeaders(rewriteResponse, request);
      return rewriteResponse;
    }
  }

  // Continue with default behavior
  const nextResponse = NextResponse.next();
  addCORSHeaders(nextResponse, request);
  return nextResponse;
}

/**
 * Handle CORS preflight requests
 */
function handleCORS(request: NextRequest) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    const response = new NextResponse(null, { status: 204 });
    addCORSHeaders(response, request);
    return response;
  }
  return null;
}

/**
 * Add CORS headers to a response
 */
function addCORSHeaders(response: NextResponse, request: NextRequest) {
  const origin = request.headers.get('origin');

  // Check if the origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else {
    // For requests without origin or from non-allowed origins
    response.headers.set('Access-Control-Allow-Origin', allowedOrigins[0]);
  }

  // Set other CORS headers
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours

  return response;
}

export const config = {
  // Update matcher to include all routes that need CORS
  matcher: ['/token/:network/:address*', '/api/:path*'],
};
