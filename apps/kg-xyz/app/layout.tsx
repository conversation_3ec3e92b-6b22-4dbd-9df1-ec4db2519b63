import type { Metadata, Viewport } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { Inter } from 'next/font/google';
import { headers } from 'next/headers';

import { Toaster } from '@/components/ui/toaster';
import { GoogleTagManager } from '@next/third-parties/google';

import './globals.css';
import { Providers } from './providers';

const inter = Inter({ subsets: ['latin'] });

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export async function generateMetadata(): Promise<Metadata> {
  const headersList = await headers();
  const pathname = headersList.get('x-pathname') || '/';

  // Determine the deep link based on the current path
  let appArgument = 'kryptogo://';
  if (pathname.startsWith('/token/')) {
    // For token pages, the path format should be /token/{chainId}/{address}
    appArgument = `kryptogo://${pathname}`;
  }

  return {
    manifest: '/manifest.json',
    title: 'KryptoGO | Your Ultimate Stablecoin Wallet',
    description:
      'Tired of scattered assets? KryptoGO lets you transfer, swap, trade, invest, and manage stablecoins effortlessly—with a unified balance, no chain barriers, and no need to allocate native tokens.',
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon.ico',
      apple: '/favicon.ico',
    },
    appleWebApp: {
      capable: true,
      statusBarStyle: 'default',
    },
    openGraph: {
      title: 'KryptoGO | Your Ultimate Stablecoin Wallet',
      description:
        'Tired of scattered assets? KryptoGO lets you transfer, swap, trade, invest, and manage stablecoins effortlessly—with a unified balance, no chain barriers, and no need to allocate native tokens.',
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_BASEURL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: 'KryptoGO',
        },
      ],
    },
    other: {
      'apple-itunes-app': `app-id=1593830910, app-argument=${appArgument}`,
      'google-play-app': `app-id=com.kryptogo.walletapp, app-argument=${appArgument}`,
    },
  };
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <GoogleTagManager gtmId="GTM-5VX7QW7N" />
      <body className={`${inter.className} bg-zinc-950 text-white antialiased`} suppressHydrationWarning>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Providers>{children}</Providers>
          <Toaster />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
