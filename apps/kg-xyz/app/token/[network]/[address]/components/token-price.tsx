'use client';

import { usePrices } from '@/contexts/price-context';
import { formatNumber } from '@/lib/format';

interface TokenPriceProps {
  tokenAddress: string;
  initialChange: string;
  className?: string;
}

export function TokenPrice({ tokenAddress, initialChange, className = '' }: TokenPriceProps) {
  const { prices } = usePrices();
  const price = prices[`sol:${tokenAddress}`] || 0;

  return (
    <div className={`flex flex-col ${className}`}>
      <div className="flex items-center justify-between">
        <span className="mb-0.5 text-base font-bold text-white">
          ${formatNumber(price, { notation: 'compact', maximumFractionDigits: 6 })}
        </span>
      </div>
      <div className="flex justify-end">
        <span
          className={`text-xs font-medium ${!isNaN(parseFloat(initialChange)) && parseFloat(initialChange) >= 0 ? 'text-[#00B38C]' : 'text-pink-600'}`}
        >
          {!isNaN(parseFloat(initialChange))
            ? (parseFloat(initialChange) >= 0 ? '+' : '') + parseFloat(initialChange).toFixed(2) + '% / 24h'
            : '- / 24h'}
        </span>
      </div>
    </div>
  );
}

// Export the updatePrice function so it can be called from outside
export type TokenPriceRef = {
  updatePrice: (price: string) => void;
};
