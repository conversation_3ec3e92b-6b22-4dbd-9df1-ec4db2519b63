'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState, useRef } from 'react';

import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import { usePrices } from '@/contexts/price-context';
import { useBuySignals } from '@/hooks/use-signals';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import { formatTimeAgo } from '@/utils/time';

interface SignalSectionProps {
  tokenAddress: string;
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

// 信號詳細內容組件 - 可在大螢幕和小螢幕的底部彈出框中重複使用
interface SignalDetailContentProps {
  currentSignal: any;
  maxGainAfterSignal: number;
  priceSinceSignal: number;
  averageHolding: number;
  averageWinRate: number;
  t: any;
}

function SignalDetailContent({
  currentSignal,
  maxGainAfterSignal,
  priceSinceSignal,
  averageHolding,
  averageWinRate,
  t,
}: SignalDetailContentProps) {
  return (
    <>
      <div className="mb-2 grid grid-cols-2 gap-2">
        <div className="content-around rounded border border-neutral-800 p-2">
          <p className="mb-1 text-center text-xs text-zinc-400 md:text-[0.7rem]">{t('max-gain-after-alert')}</p>
          <p className="text-center text-lg font-medium text-[#00B38C] md:text-lg">
            +{formatNumber(maxGainAfterSignal, { maximumFractionDigits: 0 })}%
          </p>
        </div>
        <div className="content-around rounded border border-neutral-800 p-2">
          <p className="mb-1 text-center text-xs text-zinc-400 md:text-[0.7rem]">{t('price-since-signal')}</p>
          <p
            className={cn(
              'text-center text-lg font-medium md:text-lg',
              priceSinceSignal >= 0 ? 'text-emerald-400' : 'text-pink-500',
            )}
          >
            {priceSinceSignal >= 0 ? '+' : ''}
            {formatNumber(priceSinceSignal, { maximumFractionDigits: 2 })}%
          </p>
        </div>
      </div>

      <div className="mb-2 mt-2 grid grid-cols-2 gap-2">
        <div className="content-around rounded border border-neutral-800 p-2">
          <p className="mb-1 text-center text-xs text-zinc-400 md:text-[0.7rem]">{t('smart-money-holdings')}</p>
          <p className="text-center text-lg font-medium md:text-lg">
            {formatNumber(averageHolding * 100, { maximumFractionDigits: 0 })}%
          </p>
        </div>
        <div className="content-around rounded border border-neutral-800 p-2">
          <p className="mb-1 text-center text-xs text-zinc-400 md:text-[0.7rem]">{t('smart-money-win-rate')}</p>
          <p className={cn('text-center text-lg font-medium md:text-lg', averageWinRate > 0.7 && 'text-yellow-500')}>
            {formatNumber(averageWinRate * 100, { maximumFractionDigits: 0 })}%
          </p>
        </div>
      </div>

      <button
        onClick={() => window.open(currentSignal?.telegram_link, '_blank')}
        className="flex w-full cursor-pointer items-center justify-center rounded-md p-4 text-neutral-400 hover:bg-zinc-800/50 md:p-2"
      >
        <span className="mr-2 text-sm md:text-xs">{t('view-signal')}</span>
        <svg width="12" height="12" viewBox="0 0 24 24" fill="grey" xmlns="http://www.w3.org/2000/svg" className="mr-0">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.7587 2L10 2C10.5523 2 11 2.44772 11 3C11 3.55229 10.5523 4 10 4H7.8C6.94342 4 6.36113 4.00078 5.91104 4.03755C5.47262 4.07337 5.24842 4.1383 5.09202 4.21799C4.7157 4.40973 4.40973 4.7157 4.21799 5.09202C4.1383 5.24842 4.07337 5.47262 4.03755 5.91104C4.00078 6.36113 4 6.94342 4 7.8V16.2C4 17.0566 4.00078 17.6389 4.03755 18.089C4.07337 18.5274 4.1383 18.7516 4.21799 18.908C4.40973 19.2843 4.7157 19.5903 5.09202 19.782C5.24842 19.8617 5.47262 19.9266 5.91104 19.9624C6.36113 19.9992 6.94342 20 7.8 20H16.2C17.0566 20 17.6389 19.9992 18.089 19.9624C18.5274 19.9266 18.7516 19.8617 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C19.8617 18.7516 19.9266 18.5274 19.9624 18.089C19.9992 17.6389 20 17.0566 20 16.2V14C20 13.4477 20.4477 13 21 13C21.5523 13 22 13.4477 22 14V16.2413C22 17.0463 22 17.7106 21.9558 18.2518C21.9099 18.8139 21.8113 19.3306 21.564 19.816C21.1805 20.5686 20.5686 21.1805 19.816 21.564C19.3306 21.8113 18.8139 21.9099 18.2518 21.9558C17.7106 22 17.0463 22 16.2413 22H7.75868C6.95372 22 6.28936 22 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43598 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413V7.7587C1.99999 6.95373 1.99998 6.28937 2.04419 5.74817C2.09012 5.18608 2.18868 4.66937 2.43597 4.18404C2.81947 3.43139 3.43139 2.81947 4.18404 2.43597C4.66937 2.18868 5.18608 2.09012 5.74817 2.04419C6.28937 1.99998 6.95373 1.99999 7.7587 2ZM14 3.00001C14 2.44773 14.4477 2.00001 15 2.00001H21C21.5523 2.00001 22 2.44773 22 3.00001L22 9.00001C22 9.55229 21.5523 10 21 10C20.4477 10 20 9.5523 20 9.00001L20 5.41422L12.7071 12.7071C12.3166 13.0976 11.6834 13.0976 11.2929 12.7071C10.9024 12.3166 10.9024 11.6834 11.2929 11.2929L18.5858 4.00001H15C14.4477 4.00001 14 3.5523 14 3.00001Z"
          />
        </svg>
      </button>
    </>
  );
}

export function SignalSection({ tokenAddress }: SignalSectionProps) {
  const t = useTranslations('signal-card');

  const { data: buySignals } = useBuySignals();
  const { prices, registerToken } = usePrices();
  const [isSignalExpanded, setIsSignalExpanded] = useState(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [elapsedSeconds, setElapsedSeconds] = useState<number | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const currentSignal = buySignals?.find((signal) => signal.token_address === tokenAddress);

  useEffect(() => {
    registerToken(tokenAddress);
  }, [tokenAddress, registerToken]);

  // 動態計時器效果
  useEffect(() => {
    if (!currentSignal?.emit_time) return;

    const emitTimeMs = currentSignal.emit_time * 1000;
    const updateElapsed = () => {
      const now = Date.now();
      const seconds = Math.floor((now - emitTimeMs) / 1000);
      setElapsedSeconds(seconds);
    };

    updateElapsed();

    // 只有在 10 分鐘內才更新計時器
    if (Date.now() - emitTimeMs < 600000) {
      timerRef.current = setInterval(updateElapsed, 1000);
      return () => {
        if (timerRef.current) clearInterval(timerRef.current);
      };
    }
  }, [currentSignal?.emit_time]);

  // Get the token price from the PriceProvider
  const tokenKey = `sol:${tokenAddress}`;
  const tokenPrice = prices[tokenKey] || 0;
  const buyEntryPrice = currentSignal?.buy_entry_price || 0;
  const priceSinceSignal =
    currentSignal && buyEntryPrice > 0 && tokenPrice > 0 ? ((tokenPrice - buyEntryPrice) / buyEntryPrice) * 100 : 0;

  // Format the values
  const averageHolding = currentSignal ? currentSignal.average_holding : 1;
  const averageWinRate = currentSignal ? currentSignal.average_win_rate : 0.57;

  // 格式化秒數為 MM:SS 格式
  const formatMMSS = (totalSeconds: number) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  const signalDuration =
    currentSignal && currentSignal.emit_time
      ? formatTimeAgo(Date.now() - new Date(+currentSignal.emit_time * 1000).getTime())
      : null;

  const maxGainAfterSignal =
    currentSignal && +currentSignal.highest_price > 0 && +currentSignal.buy_entry_price > 0
      ? ((+currentSignal.highest_price - +currentSignal.buy_entry_price) / +currentSignal.buy_entry_price) * 100
      : 0.0;

  // 檢查是否在 10 分鐘內
  const underTenMinis = elapsedSeconds !== null && elapsedSeconds < 600;

  // 即使沒有 signal 也顯示區域，但狀態不同
  // 使用 useEffect 監聽窗口大小變化
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth >= 768 && isDrawerOpen) {
          setIsDrawerOpen(false);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isDrawerOpen]);

  // 處理點擊事件
  const handleSignalClick = () => {
    if (!currentSignal) return;

    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      // 小螢幕：打開底部彈出框
      setIsDrawerOpen(true);
    } else {
      // 大螢幕：展開/收起內容
      setIsSignalExpanded(!isSignalExpanded);
    }
  };

  return (
    <div className="overflow-hidden rounded-none bg-zinc-900/50 md:rounded-md">
      <div className="border-b border-zinc-800/50 px-3 py-2 md:border-b-0 md:p-4">
        <div
          className={cn('flex items-center justify-between', currentSignal ? 'cursor-pointer' : 'cursor-default')}
          onClick={handleSignalClick}
        >
          <div className="flex items-center space-x-2">
            {currentSignal ? (
              <div className="relative mx-2">
                <div
                  className="loader"
                  style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '100%',
                    background: '#10b981',
                    boxShadow: '0 0 0 0 rgba(16, 185, 129, 0.3)',
                  }}
                />
                <style jsx global>{`
                  .loader {
                    animation: l1 1s infinite;
                  }
                  @keyframes l1 {
                    100% {
                      box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
                    }
                  }
                `}</style>
              </div>
            ) : (
              <div className="mr-0 h-3 w-3 rounded-full bg-zinc-600"></div>
            )}
            <span className="text-sm font-medium text-zinc-300 md:text-sm">{t('title')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              {currentSignal ? (
                <>
                  <span
                    className={cn(
                      'mr-4 flex items-center text-sm font-semibold md:text-sm',
                      underTenMinis ? 'text-[#FFC211]' : 'text-white',
                    )}
                  >
                    {underTenMinis && <span className="mr-1 text-amber-500">🔥</span>}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    {elapsedSeconds !== null && elapsedSeconds < 600 ? formatMMSS(elapsedSeconds) : signalDuration}
                  </span>
                  <span className="flex items-center text-sm font-semibold text-[#00B38C] md:text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                      <polyline points="17 6 23 6 23 12"></polyline>
                    </svg>
                    {formatNumber(maxGainAfterSignal, { maximumFractionDigits: 0 })}%
                  </span>
                </>
              ) : (
                <span className="mr-1 rounded bg-zinc-800/50 px-1.5 py-0.5 text-sm font-semibold text-neutral-600">
                  {t('no-signal-detected')}
                </span>
              )}
            </div>
            {currentSignal && (
              <motion.div animate={{ rotate: isSignalExpanded ? 180 : 0 }} transition={{ duration: 0.3 }}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-zinc-400"
                >
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Expandable Content - Desktop View */}
      {currentSignal && isSignalExpanded && typeof window !== 'undefined' && window.innerWidth >= 768 ? (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="p-3"
        >
          <SignalDetailContent
            currentSignal={currentSignal}
            maxGainAfterSignal={maxGainAfterSignal}
            priceSinceSignal={priceSinceSignal}
            averageHolding={averageHolding}
            averageWinRate={averageWinRate}
            t={t}
          />
        </motion.div>
      ) : null}

      {/* Signal Details Drawer - Mobile View */}
      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent className="border-zinc-800/50 bg-zinc-950">
          <DrawerHeader>
            <DrawerTitle className="text-white">{t('title')}</DrawerTitle>
          </DrawerHeader>
          {currentSignal && (
            <div className="px-4 pb-6">
              <SignalDetailContent
                currentSignal={currentSignal}
                maxGainAfterSignal={maxGainAfterSignal}
                priceSinceSignal={priceSinceSignal}
                averageHolding={averageHolding}
                averageWinRate={averageWinRate}
                t={t}
              />
            </div>
          )}
        </DrawerContent>
      </Drawer>
    </div>
  );
}
