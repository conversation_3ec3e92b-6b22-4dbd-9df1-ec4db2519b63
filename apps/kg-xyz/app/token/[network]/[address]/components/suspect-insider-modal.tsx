'use client';

import bs58 from 'bs58';
import { CircleHelp } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';

import { ResponsiveTooltipDrawer } from '@/components/responsive-tooltip-drawer';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { usePrices } from '@/contexts/price-context';
import { useSuspectInsider } from '@/contexts/suspect-insider-context';
import { useWalletConnectModal } from '@/contexts/wallet-connect-modal-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { useTokenAnalysisCredits } from '@/hooks/use-token-analysis-credits';
import { useIsMobile } from '@/hooks/useIsMobile';
import { SOL_ADDRESS } from '@/lib/chains';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import type { OKXTokenDetail } from '@/types/okx';
import { sendGTMEvent } from '@next/third-parties/google';
import { useWallet } from '@solana/wallet-adapter-react';

interface StoredSignature {
  signature: string;
  timestamp: number;
  message: string;
  walletAddress: string;
}

const SIGNATURE_STORAGE_KEY = 'kg-suspect-insider-signature';
const ONE_DAY_IN_SECONDS = 86400;

interface SuspectInsiderModalProps {
  open: boolean;
  onCloseAction: () => void;
  chainId: string;
  token: OKXTokenDetail;
}

interface TokenAnalysisResult {
  cex: number;
  individual_traders: number;
  kol_traders: number;
  liquidity_pools: number;
  others: number;
  suspect_insiders: number;
  suspect_insiders_sol_balance: number;
}

interface ChartDataEntry {
  name: string;
  value: number;
  id: keyof typeof COLORS;
}

const COLORS = {
  suspect_insiders: '#FFC211',
  kol_traders: '#7E52DF',
  liquidity_pools: '#5ABAF9',
  cex: '#0B8990',
  individual_traders: '#F7931A',
  others: '#666666',
} as const;

export const SuspectInsiderModal: React.FC<SuspectInsiderModalProps> = ({ open, onCloseAction, chainId, token }) => {
  const { openModal } = useWalletConnectModal();
  const { setIsOpen } = useSuspectInsider();
  useEffect(() => {
    setIsOpen(open);
  }, [open, setIsOpen]);

  const { signMessage } = useWallet();
  const { address: publicKey } = useDebugWallet();
  const { data: { credits } = { credits: 0 }, refetch } = useTokenAnalysisCredits(publicKey);
  const [signing, setSigning] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const searchParams = useSearchParams();
  const [result, setResult] = useState<TokenAnalysisResult | null>(() =>
    searchParams.get('debugSuspectInsider')
      ? {
          cex: 0,
          individual_traders: 19.94772802624595,
          kol_traders: 0,
          liquidity_pools: 34.11069769748188,
          others: 39.18285960645316,
          suspect_insiders: 6.758714669819004,
          suspect_insiders_sol_balance: 4.856886597,
        }
      : null,
  );
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations();
  const { toast } = useToast();
  const locale = useLocale();
  const modalRef = useRef<HTMLDivElement>(null);
  const { prices } = usePrices();
  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 160.0;

  // Select image based on locale
  let promoImg = '/suspect-insider-promote-en.avif';
  if (locale === 'zh-tw') promoImg = '/suspect-insider-promote-tw.avif';
  if (locale === 'zh-cn') promoImg = '/suspect-insider-promote-cn.avif';

  const getStoredSignature = (): StoredSignature | null => {
    try {
      const stored = localStorage.getItem(SIGNATURE_STORAGE_KEY);
      if (!stored) return null;
      return JSON.parse(stored);
    } catch (e) {
      console.error('Error reading stored signature:', e);
      return null;
    }
  };

  const storeSignature = (data: StoredSignature) => {
    try {
      localStorage.setItem(SIGNATURE_STORAGE_KEY, JSON.stringify(data));
    } catch (e) {
      console.error('Error storing signature:', e);
    }
  };

  const isSignatureValid = useCallback(
    (storedData: StoredSignature): boolean => {
      const now = Math.floor(Date.now() / 1000);
      return storedData.walletAddress === publicKey && now - storedData.timestamp < ONE_DAY_IN_SECONDS;
    },
    [publicKey],
  );

  const handleAnalyze = async () => {
    if (!publicKey || !signMessage) {
      toast({
        variant: 'destructive',
        title: t('suspect-insider.error-title'),
        description: t('suspect-insider.wallet-required'),
      });
      return;
    }

    try {
      setError(null);
      let signature: string;
      let message: string;
      let timestamp: number;

      // Check for valid stored signature
      const storedData = getStoredSignature();
      if (storedData && isSignatureValid(storedData)) {
        // Reuse stored signature
        signature = storedData.signature;
        message = storedData.message;
        timestamp = storedData.timestamp;
      } else {
        // Need new signature
        setSigning(true);
        timestamp = Math.floor(Date.now() / 1000);
        message = `Sign this message to login to kryptogo.xyz\nTimestamp: ${timestamp}`;
        const messageBytes = new TextEncoder().encode(message);

        const signatureBytes = await signMessage(messageBytes);
        if (!signatureBytes) {
          throw new Error(t('suspect-insider.sign-rejected'));
        }

        signature = bs58.encode(Buffer.from(signatureBytes));

        // Store the new signature
        storeSignature({
          signature,
          timestamp,
          message,
          walletAddress: publicKey,
        });
        setSigning(false);
      }

      // Start analysis
      setAnalyzing(true);
      setResult(null);

      const response = await fetch(`${process.env.NEXT_PUBLIC_KG_CLOUD_RUN_URL}/v1/token_analysis/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          wallet_address: publicKey,
          token_address: token.tokenContractAddress,
          chain_id: chainId,
          message,
          signature,
        }),
      });

      const data = await response.json();
      if (data.code === 0) {
        setResult(data.data.analysis);
        sendGTMEvent({
          event: 'app_xyz_insider_analyze_success',
          token_address: token.tokenContractAddress,
        });
        refetch();
      } else {
        throw new Error(data.message || t('suspect-insider.analyzing-error'));
      }
    } catch (e) {
      sendGTMEvent({
        event: 'app_xyz_insider_analyze_fail',
        token_address: token.tokenContractAddress,
      });
      console.error('Analysis error:', e);
      toast({
        variant: 'destructive',
        title: t('suspect-insider.error-title'),
        description: e instanceof Error ? e.message : t('suspect-insider.network-error'),
      });
    } finally {
      setSigning(false);
      setAnalyzing(false);
    }
  };

  // Clear invalid signatures on mount
  useEffect(() => {
    const storedData = getStoredSignature();
    if (storedData && !isSignatureValid(storedData)) {
      localStorage.removeItem(SIGNATURE_STORAGE_KEY);
    }
  }, [publicKey, isSignatureValid]);

  const formatPieChartData = (result: TokenAnalysisResult): ChartDataEntry[] => {
    return [
      {
        name: t('suspect-insider.categories.suspect-insiders'),
        value: result.suspect_insiders,
        id: 'suspect_insiders',
      },
      { name: t('suspect-insider.categories.kol-traders'), value: result.kol_traders, id: 'kol_traders' },
      { name: t('suspect-insider.categories.liquidity-pools'), value: result.liquidity_pools, id: 'liquidity_pools' },
      { name: t('suspect-insider.categories.cex'), value: result.cex, id: 'cex' },
      {
        name: t('suspect-insider.categories.individual-traders'),
        value: result.individual_traders,
        id: 'individual_traders',
      },
      { name: t('suspect-insider.categories.others'), value: result.others, id: 'others' },
    ];
  };

  const isMobile = useIsMobile();

  // Don't render anything if not open
  if (!open) {
    return null;
  }

  // Modal content that will be used in both desktop modal and mobile drawer
  const modalContent = (
    <>
      {/* Close Button - Always in top right corner */}
      {!isMobile && (
        <button
          className="absolute right-4 top-4 z-10 rounded-full p-1 text-zinc-400 hover:bg-zinc-800 hover:text-white"
          onClick={onCloseAction}
        >
          <Image src="/icons/close.svg" alt="Close" width={24} height={24} />
        </button>
      )}

      <div className={cn('max-h-[90vh] overflow-y-auto', isMobile ? 'p-0 pt-0' : 'p-0 sm:p-0')}>
        {/* Header */}
        <div className="mb-6 mt-4 flex flex-col items-start gap-4 md:mb-8 md:flex-row">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 overflow-hidden rounded-full bg-zinc-800 md:h-10 md:w-10">
              {token?.tokenLogoUrl && (
                <Image
                  src={token.tokenLogoUrl}
                  alt={token.tokenSymbol}
                  width={48}
                  height={48}
                  className="object-cover"
                />
              )}
            </div>
            <div className="flex flex-col">
              <span className="text-base font-bold text-white md:text-lg">{token?.tokenSymbol}</span>
              <span className="text-[0.65rem] text-zinc-400 md:text-xs">
                {token?.tokenContractAddress?.slice(0, 7)}...{token?.tokenContractAddress?.slice(-5)}
              </span>
            </div>
          </div>
        </div>

        {/* Analysis Content */}
        {analyzing ? (
          // Loading State - Full Width
          <div className="col-span-1 flex flex-col items-center justify-center rounded-xl bg-zinc-950/50 p-4 backdrop-blur-md md:col-span-2">
            {/* Shimmer Loading Animation */}
            <div className="w-full space-y-8">
              {/* Middle Section Shimmer */}
              <div className="flex justify-center">
                <div className="h-32 w-3/4 animate-pulse rounded-xl bg-zinc-800"></div>
              </div>
            </div>

            {/* Loading Text */}
            <div className="mt-8 text-center">
              <div className="mb-2 text-xl font-semibold text-white">{t('suspect-insider.analyzing')}</div>
              <div className="text-sm text-zinc-400">{t('suspect-insider.analyzing-wait')}</div>
              <div className="mt-1 text-sm text-zinc-400">{t('suspect-insider.analyzing-notice')}</div>
            </div>
          </div>
        ) : result ? (
          <div className="grid grid-cols-1 gap-4 bg-zinc-950/50 backdrop-blur-md sm:gap-6 md:grid-cols-3 md:gap-6">
            {/* Left: Holdings Distribution */}
            <div className="rounded-xl bg-zinc-900/50 px-6 py-4 md:col-span-2">
              <h2 className="mb-2 text-center text-base font-bold text-white sm:mb-4 md:text-left md:text-xl">
                {t('suspect-insider.holdings')}
              </h2>
              <div className="flex flex-col md:!flex-row md:items-center md:gap-4">
                <div className="relative h-[300px] w-full md:h-[300px] md:w-1/2">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={formatPieChartData(result)}
                        cx="50%"
                        cy="50%"
                        innerRadius="60%"
                        outerRadius="80%"
                        dataKey="value"
                        startAngle={90}
                        endAngle={-270}
                        strokeWidth={0}
                      >
                        {formatPieChartData(result).map((entry) => (
                          <Cell key={entry.id} fill={COLORS[entry.id]} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <div className="text-2xl font-normal text-white">{token.holders}</div>
                    <div className="text-sm text-zinc-400">{t('suspect-insider.total-holders')}</div>
                  </div>
                </div>
                {/* Legend */}
                <div className="mt-0 flex flex-col md:w-1/2">
                  {/* Suspect Insiders - Top Row */}
                  {formatPieChartData(result)
                    .filter((entry) => entry.id === 'suspect_insiders')
                    .map((entry) => (
                      <div key={entry.id} className="mb-4 flex flex-col">
                        <div className="mb-1 flex items-center gap-2">
                          <div
                            className="h-2.5 w-2.5 flex-shrink-0 rounded-[2px]"
                            style={{ backgroundColor: COLORS[entry.id] }}
                          />
                          <span className="text-xs font-normal text-zinc-400 md:text-sm">{entry.name}</span>
                        </div>
                        <div className="flex flex-row items-center gap-2 text-lg font-medium text-white md:text-xl">
                          {entry.value.toFixed(2)}%
                          {entry.value >= 15 && entry.value < 30 && (
                            <span className="inline-flex items-center rounded-full bg-emerald-900/30 px-2 py-0.5 text-xs font-medium text-emerald-400">
                              {t('suspect-insider.high')}
                            </span>
                          )}
                          {entry.value >= 30 && (
                            <span className="inline-flex items-center rounded-full bg-emerald-900/30 px-2 py-0.5 text-xs font-medium text-emerald-400">
                              {t('suspect-insider.extremely-high')}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}

                  {/* Divider */}
                  <div className="mb-4 mt-0 border-t border-zinc-800/50"></div>

                  {/* Other Categories - Horizontal on mobile, Two Columns on desktop */}
                  <div className="flex flex-col gap-x-3 gap-y-4 md:grid md:grid-cols-2">
                    {formatPieChartData(result)
                      .filter((entry) => entry.id !== 'suspect_insiders')
                      .map((entry) => (
                        <div
                          key={entry.id}
                          className="mb-3 flex flex-row items-center md:mb-0 md:flex-col md:items-start"
                        >
                          <div className="mb-0 flex items-center gap-2 md:mb-1">
                            <div
                              className="h-2.5 w-2.5 flex-shrink-0 rounded-[2px]"
                              style={{ backgroundColor: COLORS[entry.id] }}
                            />
                            <span className="min-w-0 text-xs text-zinc-400 md:max-w-[110px] md:truncate">
                              {entry.name}
                            </span>
                          </div>
                          <div className="ml-auto text-base font-medium text-white md:ml-0 md:text-lg">
                            {entry.value.toFixed(2)}%
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right: Remaining Balance */}
            <div className="mt-4 flex flex-col items-center justify-center rounded-xl bg-zinc-900/50 px-6 py-4 md:mt-0">
              <h2 className="mb-8 text-center text-base font-bold text-white max-sm:text-base md:text-xl">
                {t('suspect-insider.remaining-solana-balance')}
              </h2>
              <Image
                src="/chain-icons/icon-solana.png"
                alt="Solana"
                width={52}
                height={52}
                className="mb-4 rounded-full"
              />
              <div className="mb-1 text-3xl font-semibold text-white md:text-4xl">
                {formatNumber(result.suspect_insiders_sol_balance, { maximumFractionDigits: 0 })} SOL
              </div>
              <div className="text-sm text-zinc-400 md:text-base">
                ≈ $
                {formatNumber(result.suspect_insiders_sol_balance * solPrice, {
                  notation: 'compact',
                  maximumFractionDigits: 3,
                })}{' '}
                USD
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 md:gap-8">
            {/* Left: Promo Image */}
            <div className="flex items-center justify-center px-0 md:px-4">
              <Image
                src={promoImg}
                alt="Suspect Insider"
                width={400}
                height={400}
                className="w-full max-w-[400px] rounded-xl md:max-w-[420px]"
                priority
              />
            </div>

            {/* Right: CTA Section */}
            <div className="mb-8 mt-4 flex flex-col justify-center px-4 md:mb-0 md:mt-0">
              {signing ? (
                <div className="flex flex-col items-center justify-center p-4 text-center">
                  <div className="mb-4 text-lg font-semibold text-white md:text-xl">
                    {t('suspect-insider.sign-message')}
                  </div>
                  <div className="loader h-10 w-10 rounded-full border-4 border-zinc-600 border-t-yellow-500"></div>
                </div>
              ) : (
                <>
                  <h2 className="mb-2 text-center text-2xl font-semibold text-white sm:mb-2 md:text-left md:text-3xl">
                    {t('suspect-insider.analyze-cta')}
                  </h2>
                  <p className="mb-8 text-center text-base text-zinc-400 md:text-left md:text-lg">
                    {t('suspect-insider.analysis-cost')}
                  </p>
                  <div className="flex flex-row items-center gap-3 md:items-start">
                    <div className="flex w-full flex-col items-center gap-3 sm:flex-row sm:items-center sm:gap-4 md:items-start">
                      {publicKey ? (
                        <button
                          className={`flex items-center gap-2 rounded-lg px-6 py-3 text-base font-semibold transition-all ${credits !== undefined && credits > 0 ? 'bg-yellow-500 text-black hover:bg-yellow-400' : 'cursor-not-allowed bg-zinc-700 text-zinc-400 opacity-50'}`}
                          onClick={handleAnalyze}
                          disabled={credits === undefined || credits <= 0}
                        >
                          <Image src="/ic-solid-star-02.svg" alt="Star" width={18} height={18} />
                          {t('suspect-insider.try-it-out')}
                        </button>
                      ) : (
                        <button
                          className={`flex items-center gap-2 rounded-lg bg-yellow-500 px-6 py-3 text-base font-semibold text-black transition-all`}
                          onClick={openModal}
                        >
                          {t('trading-stats-panel.connect-wallet')}
                        </button>
                      )}
                      <div className="flex flex-col gap-1.5">
                        <span className="text-center text-sm text-zinc-400 md:text-left">
                          {credits !== undefined ? t('suspect-insider.credits-left', { credits }) : '...'}
                        </span>
                        <div className="flex flex-row items-center">
                          <CircleHelp className="mr-1.5 h-3.5 w-3.5 text-zinc-400 hover:text-zinc-300" />
                          <ResponsiveTooltipDrawer
                            title={t('suspect-insider.credits-title')}
                            description={t('suspect-insider.credits-description')}
                            className="text-xs text-zinc-400 hover:text-zinc-300"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {error && <div className="mt-4 text-center text-red-500">{error}</div>}
            </div>
          </div>
        )}
      </div>
    </>
  );

  // Use Drawer for mobile, modal for desktop
  return isMobile ? (
    <Drawer open={open} onOpenChange={onCloseAction}>
      <DrawerContent className="h-[85vh] max-h-[85vh] border-t border-zinc-800/50 bg-zinc-900/50 backdrop-blur-md transition-all duration-300 ease-out">
        <div className="h-full overflow-y-auto px-4 pb-8 pt-0">{modalContent}</div>
      </DrawerContent>
      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        .loader {
          animation: spin 1s linear infinite;
        }
        @keyframes shimmer {
          0% {
            opacity: 0.5;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0.5;
          }
        }
        .animate-pulse {
          animation: shimmer 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes slideIn {
          from {
            transform: translateY(100%);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
      `}</style>
    </Drawer>
  ) : (
    <div className="animate-fadeIn fixed inset-0 z-50 flex items-end justify-center bg-black/60 p-0 sm:items-center sm:overflow-y-auto sm:p-4">
      {/* Clickable mask overlay */}
      <div className="absolute inset-0" onClick={onCloseAction} />

      <div
        ref={modalRef}
        className="animate-slideIn relative w-full max-w-[1000px] rounded-t-2xl border border-zinc-800/50 bg-zinc-900/50 p-0 backdrop-blur-md md:rounded-2xl md:p-6"
      >
        {modalContent}
      </div>
      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        .loader {
          animation: spin 1s linear infinite;
        }
        @keyframes shimmer {
          0% {
            opacity: 0.5;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0.5;
          }
        }
        @keyframes slideIn {
          from {
            transform: translateY(20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        .animate-slideIn {
          animation: slideIn 0.3s ease-out forwards;
        }
        .animate-fadeIn {
          animation: fadeIn 0.2s ease-out forwards;
        }
        .animate-pulse {
          animation: shimmer 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
};
