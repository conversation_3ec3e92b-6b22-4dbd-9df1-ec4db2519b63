'use client';

import { useTranslations } from 'next-intl';

import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useTokenAnalysisCredits } from '@/hooks/use-token-analysis-credits';
import { formatNumber } from '@/lib/format';

interface CreditDisplayProps {
  className?: string;
}

export function CreditDisplay({ className = '' }: CreditDisplayProps) {
  const { address: publicKey } = useDebugWallet();
  const { data: { credits } = { credits: 0 } } = useTokenAnalysisCredits(publicKey);
  const t = useTranslations('credit-system');

  if (!publicKey) {
    return null;
  }

  return (
    <div className={`flex flex-col items-center gap-1 ${className}`}>
      <div className="text-2xl font-bold text-white">{credits !== undefined ? formatNumber(credits) : '...'}</div>
      <div className="text-xs text-zinc-400">{t('available-credits')}</div>
    </div>
  );
}
