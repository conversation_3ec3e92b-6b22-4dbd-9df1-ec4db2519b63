'use client';

import { ArrowLeftRight } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useEffect, useMemo, useState } from 'react';

import { Slider } from '@/components/ui/slider';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TRADING_CONFIG } from '@/config/trading';
import { usePrices } from '@/contexts/price-context';
import { useWalletConnectModal } from '@/contexts/wallet-connect-modal-context';
import { useAssetsQuery, useJupiterQuoteQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { useTransactions } from '@/hooks/use-transactions';
import { USDC_ADDRESS, SOL_ADDRESS } from '@/lib/chains';
import { formatNumber, formatPriceWithDecimals } from '@/lib/format';
import { getUltraOrder } from '@/lib/jupiter';
import { executeJupiterSwap } from '@/lib/jupiter-swap';
import { notifyTransactionWatch } from '@/lib/notify-transaction';
import {
  addSolTransferToTransactionV2,
  addUsdcTransferToTransactionV2,
  processTransactionData,
} from '@/lib/sign-transaction';
import { calAveragePositionCost, cn, checkTxConfirmation } from '@/lib/utils';
import type { QuoteResponse } from '@jup-ag/api';
import { sendGTMEvent } from '@next/third-parties/google';
import * as Sentry from '@sentry/nextjs';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';

type TradingMode = 'buy' | 'sell';

interface TradingSectionProps {
  className?: string;
  tokenAddress: string;
  tokenSymbol: string;
  inDetailPage?: boolean;
  afterTrade?: () => void;
  defaultTradingMode?: TradingMode;
  defaultLimitOrderMode?: boolean;
}

export function TradingSection({
  tokenAddress,
  tokenSymbol,
  afterTrade,
  defaultTradingMode = 'buy',
  defaultLimitOrderMode = false,
}: TradingSectionProps) {
  const t = useTranslations('trading-section');
  const [quoteAmount, setQuoteAmount] = useState('');
  const [activeTab, setActiveTab] = useState<TradingMode>(defaultTradingMode);
  const [loading, setLoading] = useState(false);
  const [tokenDecimals, setTokenDecimals] = useState(0);
  const [txConfirmed, setTxConfirmed] = useState(false);
  const [priceSliderValue, setPriceSliderValue] = useState(0); // 0% price adjustment
  const [limitOrderMode, setLimitOrderMode] = useState(defaultLimitOrderMode);
  const [roiMode, setRoiMode] = useState(true);
  const [buyDipPrice, setBuyDipPrice] = useState(0);
  const [limitSellPriceInput, setLimitSellPriceInput] = useState('');
  const [selectedPresetAmount, setSelectedPresetAmount] = useState<number | null>(null);
  const [selectedBaseToken, setSelectedBaseToken] = useState<'SOL' | 'USDC'>('SOL');

  const { openModal } = useWalletConnectModal();
  const { toast } = useToast();

  const isSellMode = activeTab === 'sell';
  const { prices, registerToken, slippages } = usePrices();
  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 140.0;

  const { sendTransaction, signTransaction } = useWallet();
  const { address: publicKey } = useDebugWallet();
  const { connection } = useConnection();

  const tokenPrice = prices[`sol:${tokenAddress}`] || 0;
  useEffect(() => {
    if (buyDipPrice == 0) {
      setBuyDipPrice(tokenPrice);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tokenPrice]);

  useEffect(() => {
    setBuyDipPrice(tokenPrice * (1 - priceSliderValue / 100));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [priceSliderValue]);

  const presetAmounts = isSellMode
    ? [25, 50, 75, 100]
    : selectedBaseToken === 'SOL'
      ? [0.1, 0.5, 1.0, 2.0]
      : [20, 100, 200, 500];

  useEffect(() => {
    connection.getAccountInfo(new PublicKey(tokenAddress)).then((accountInfo) => {
      if (accountInfo && accountInfo.data) {
        // AccountInfo.data is a Buffer, we can access it as an array
        setTokenDecimals(accountInfo.data[44] || 0);
      } else {
        setTokenDecimals(0);
      }
    });
  }, [connection, tokenAddress]);

  useEffect(() => {
    // reset when active tab changes
    setQuoteAmount('');
    setPriceSliderValue(0);
    setSelectedBaseToken('SOL'); // Reset to SOL when switching tabs
  }, [activeTab]);

  useEffect(() => {
    // reset when base token changes in buy
    if (activeTab === 'buy') {
      setQuoteAmount('');
      setPriceSliderValue(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedBaseToken]);

  useEffect(() => {
    registerToken(tokenAddress);
    registerToken(SOL_ADDRESS); // SOL
  }, [tokenAddress, registerToken]);

  // Use the centralized assets query

  const { data: assets, refetch: refetchAssets } = useAssetsQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 3000,
  });

  const { transactions } = useTransactions({
    tokenAddress,
    refetchInterval: 3000,
  });

  const averagePositionCost = useMemo(() => calAveragePositionCost(transactions), [transactions]);
  const limitSellPrice = (averagePositionCost == 0 ? tokenPrice : averagePositionCost) * (1 + priceSliderValue / 100);
  const limitOrderTakingAmount = isSellMode
    ? (limitSellPrice * parseFloat(quoteAmount)) / (selectedBaseToken === 'SOL' ? solPrice : 1)
    : (parseFloat(quoteAmount) * (selectedBaseToken === 'SOL' ? solPrice : 1)) / buyDipPrice;

  // Use the centralized Jupiter quote query

  // Use the quote query
  const { data: quote } = useJupiterQuoteQuery({
    variables: {
      publicKey: publicKey || '',
      activeTab,
      quoteAmount,
      selectedBaseToken,
      isSellMode,
      tokenAddress,
      tokenDecimals,
      slippages,
    },
    enabled: !!quoteAmount && +quoteAmount > 0,
    refetchInterval: 3000,
  });

  const txUsdValue = isSellMode
    ? limitOrderMode
      ? limitSellPrice * parseFloat(quoteAmount)
      : (Number(quote?.outAmount ?? 0) / 10 ** (selectedBaseToken === 'SOL' ? 9 : 6)) *
        (selectedBaseToken === 'SOL' ? solPrice : 1) // USDC is already in USD
    : +quoteAmount * (selectedBaseToken === 'SOL' ? solPrice : 1); // If USDC, the amount is already in USD

  const handleSellPortion = (percentage: number) => {
    const currentTokenAsset = assets?.data?.[0]?.tokenAssets?.find(
      (asset) => asset.tokenContractAddress === tokenAddress,
    );
    if (percentage == 100) {
      setQuoteAmount(currentTokenAsset?.balance ?? '');
    } else {
      setQuoteAmount(formatPriceWithDecimals((+(currentTokenAsset?.balance ?? 0) * percentage) / 100));
    }
  };

  const handleSelectPresetAmount = (amount: number) => {
    setSelectedPresetAmount(amount);
    if (isSellMode) {
      handleSellPortion(amount);
    } else {
      setQuoteAmount(amount.toString());
    }
  };

  const jupiterUltraSwap = (inputMint: string, outputMint: string, decimals: number) => {
    setLoading(true);

    const amount = isSellMode
      ? +quoteAmount * 10 ** decimals
      : Math.floor(+quoteAmount * (1 - TRADING_CONFIG.platformFeeBps / 10_000) * 10 ** decimals);
    getUltraOrder({
      inputMint,
      outputMint,
      amount,
      taker: publicKey!.toString(),
      slippageBps: Math.round(slippages[`sol:${tokenAddress}`] * 10000),
    })
      .then((x) =>
        handleTransactionExecution({
          ...x,
          gtmNamePrefix: '',
          quote,
        }),
      )
      .catch((error) =>
        toast({
          variant: 'destructive',
          title: t('request-error'),
          description: error?.message,
        }),
      )
      .finally(() => setLoading(false));
  };

  const jupiterOldSwap = (inputMint: string, outputMint: string, decimals: number) => {
    setLoading(true);

    // Calculate the amount in the correct decimal format
    const amount = isSellMode
      ? +quoteAmount * 10 ** decimals
      : Math.floor(+quoteAmount * (1 - TRADING_CONFIG.platformFeeBps / 10_000) * 10 ** decimals);

    // Execute Jupiter swap using the shared utility
    executeJupiterSwap({
      inputMint,
      outputMint,
      amount,
      slippageBps: Math.round(slippages[`sol:${tokenAddress}`] * 10000),
      publicKey,
      connection,
      sendTransaction,
      isSellMode,
      baseToken: selectedBaseToken,
      tokenAddress,
      amount_display: quoteAmount,
      usd_value: txUsdValue.toFixed(2),
      toast,
      t,
      onTransactionSubmitted: (txHash) => {
        // Notify backend about the transaction
        notifyTransactionWatch(txHash);
      },
      onTransactionConfirmed: () => {
        setTxConfirmed(true);
        afterTrade?.();
        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}_success`,
          token_address: tokenAddress,
          amount: quoteAmount,
          usd_volume: txUsdValue.toFixed(2),
          walletAddress: publicKey,
          platform: 'web',
        });
      },
      onTransactionFailed: (error) => {
        console.error('Transaction failed:', error);
        Sentry.captureException(error);
        toast({
          variant: 'destructive',
          title: t('request-error'),
          description: (error as Error).message || t('failed-message'),
        });

        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}_failed`,
          token_address: tokenAddress,
          amount: quoteAmount,
          usd_volume: txUsdValue.toFixed(2),
          walletAddress: publicKey,
          slippage_bps: Math.round(slippages[`sol:${tokenAddress}`] * 10000),
          platform: 'web',
        });
      },
    }).finally(() => {
      setLoading(false);
      // Only refetch assets if transaction is confirmed
      if (txConfirmed) {
        setTimeout(refetchAssets, 2000);
        setTxConfirmed(false);
      }
    });
  };

  const handleTransactionExecution = async ({
    transaction,
    requestId,
    executeURL,
    error,
    gtmNamePrefix,
    quote,
  }: {
    transaction: string;
    requestId: string;
    executeURL?: string;
    error?: unknown;
    gtmNamePrefix: string;
    quote?: QuoteResponse;
  }) => {
    if (error) throw new Error(error as string, { cause: error });

    if (!transaction) {
      Sentry.captureException('Jupiter Swap Error');
      toast({
        variant: 'destructive',
        title: 'Request Error',
        description: 'Jupiter Swap Error',
      });
      return null;
    }
    const isLimitOrder = !!executeURL;
    const unSignedTransaction = processTransactionData(transaction);
    let finalTransaction = unSignedTransaction;
    try {
      if (!quote) throw new Error('Quote is empty');
      if (isLimitOrder) throw new Error('No commission for Limit Order');
      const commissionTransferAmount = Math.round(
        (isSellMode ? +quote.outAmount : +quote.inAmount) * (TRADING_CONFIG.platformFeeBps / 10_000),
      );

      const recipientPubkey = new PublicKey(TRADING_CONFIG.commissionWallet);
      const senderPubkey = new PublicKey(publicKey!.toString());

      const transferInstruction =
        selectedBaseToken === 'SOL' ? addSolTransferToTransactionV2 : addUsdcTransferToTransactionV2;

      finalTransaction = await transferInstruction(
        unSignedTransaction,
        senderPubkey,
        recipientPubkey,
        commissionTransferAmount,
        connection,
      );
    } catch (transferError) {
      const error = transferError as Error;
      console.error('Failed to add transfer:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      Sentry.captureException(error);
      // Continue with original transaction
    }

    let txHash = '';
    try {
      if (isLimitOrder) {
        const versionedTransaction = await signTransaction?.(finalTransaction);
        if (!versionedTransaction) throw new Error('Wallet Sign Empty');

        const txBase64 = Buffer.from(versionedTransaction.serialize()).toString('base64');

        const response = await fetch(executeURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requestId,
            signedTransaction: txBase64,
          }),
        });

        const data = await response.json();
        if (!data.signature) return null;

        txHash = data.signature;
      } else {
        const walletSignedResult = await sendTransaction?.(unSignedTransaction, connection);
        if (!walletSignedResult) throw new Error('Wallet Sign Empty');
        txHash = walletSignedResult;
      }

      notifyTransactionWatch(txHash).catch((error) => {
        console.error('Failed to notify transaction watch', error);
      });

      toast({
        title: t('order-submitted'),
        description: (
          <a href={`https://solscan.io/tx/${txHash}`} target="_blank" rel="noopener noreferrer">
            {t('tx-hash')}{' '}
            <span className="underline">
              {txHash.slice(0, 8)}...{txHash.slice(-7)}
            </span>
          </a>
        ),
      });

      const txResult = await checkTxConfirmation(txHash, connection);

      // Handle the result based on status
      if (txResult.status === 'confirmed') {
        setTxConfirmed(true);
        afterTrade?.();
        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}${gtmNamePrefix}_success`,
          token_address: tokenAddress,
          amount: quoteAmount,
          usd_volume: txUsdValue.toFixed(2),
          walletAddress: publicKey,
          platform: 'web',
        });
        toast({
          title: t('order-confirmed'),
          description: (
            <a href={`https://solscan.io/tx/${txHash}`} target="_blank" rel="noopener noreferrer">
              {t('tx-hash')}{' '}
              <span className="underline">
                {txHash.slice(0, 8)}...{txHash.slice(-7)}
              </span>
              <br />
              {t('confirmed-message')}
            </a>
          ),
        });
      } else {
        sendGTMEvent({
          event: `app_xyz_${isSellMode ? 'sell' : 'buy'}${gtmNamePrefix}_failed`,
          token_address: tokenAddress,
          amount: quoteAmount,
          usd_volume: txUsdValue.toFixed(2),
          walletAddress: publicKey,
          slippage_bps: Math.round(slippages[`sol:${tokenAddress}`] * 10000),
          platform: 'web',
        });
        toast({
          variant: 'destructive',
          title: t('order-failed'),
          description: t('failed-message'),
        });
        Sentry.captureException(`Order failed: ${JSON.stringify(txResult.error)}`);
      }

      return txResult;
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('request-error'),
        description: (error as Error).message,
      });
      return null;
    }
  };

  const handleCreateLimitOrder = () => {
    setLoading(true);
    fetch('https://lite-api.jup.ag/trigger/v1/createOrder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        maker: publicKey,
        payer: publicKey,
        inputMint: isSellMode ? tokenAddress : selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS,
        outputMint: isSellMode ? (selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS) : tokenAddress,
        params: {
          makingAmount: `${Math.floor(
            (+quoteAmount || 0) * 10 ** (isSellMode ? tokenDecimals : selectedBaseToken === 'SOL' ? 9 : 6),
          )}`,
          takingAmount: `${Math.round(
            limitOrderTakingAmount * 10 ** (isSellMode ? (selectedBaseToken === 'SOL' ? 9 : 6) : tokenDecimals),
          )}`,
          ...(isSellMode && selectedBaseToken === 'SOL' ? { feeBps: `${TRADING_CONFIG.platformFeeBps}` } : {}),
        },
        computeUnitPrice: 'auto',
        ...(isSellMode && selectedBaseToken === 'SOL'
          ? { feeAccount: 'ELsViuLMeoEdFqh1tmqJphnyTZ1rmoC1zqokD956Ep8i' }
          : {}),
      }),
    })
      .then((response) => response.json())
      .then(async (x) =>
        handleTransactionExecution({
          ...x,
          gtmNamePrefix: '_order',
          executeURL: 'https://lite-api.jup.ag/trigger/v1/execute',
        }),
      )
      .catch((error) =>
        toast({
          variant: 'destructive',
          title: t('request-error'),
          description: (error as Error).message,
        }),
      )
      .finally(() => setLoading(false));
  };

  const handleSwapTransaction = () => {
    setLoading(true);
    if (!publicKey?.toString()) {
      openModal();
      return;
    }

    sendGTMEvent({
      event: `app_xyz_${isSellMode ? 'sell' : 'buy'}_click`,
      token_address: tokenAddress,
      amount: `${quoteAmount}`,
      walletAddress: publicKey,
      platform: 'web',
    });

    // Define input and output tokens based on trading mode and selected token
    const inputMint = isSellMode ? tokenAddress : selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS;
    const outputMint = isSellMode ? (selectedBaseToken === 'SOL' ? SOL_ADDRESS : USDC_ADDRESS) : tokenAddress;
    const decimals = isSellMode ? tokenDecimals : selectedBaseToken === 'SOL' ? 9 : 6; // SOL has 9 decimals, USDC has 6

    return selectedBaseToken === 'SOL'
      ? jupiterOldSwap(inputMint, outputMint, decimals)
      : jupiterUltraSwap(inputMint, outputMint, decimals);
  };

  const currentTokenBalance =
    assets?.data?.[0]?.tokenAssets?.find((asset) => asset.tokenContractAddress === tokenAddress)?.balance || '0';

  const solBalance = assets?.data?.[0]?.tokenAssets?.find((asset) => asset.tokenContractAddress === '')?.balance || '0';

  const usdcBalance =
    assets?.data?.[0]?.tokenAssets?.find((asset) => asset.tokenContractAddress === USDC_ADDRESS)?.balance || '0';

  const getBalanceText = () => {
    if (!publicKey) return '';

    if (isSellMode) {
      return `${t('balance')}: ${formatPriceWithDecimals(parseFloat(currentTokenBalance))} ${tokenSymbol}`;
    } else {
      if (selectedBaseToken === 'SOL') {
        return `${t('balance')}: ${formatPriceWithDecimals(parseFloat(solBalance))} SOL`;
      } else {
        return `${t('balance')}: ${formatPriceWithDecimals(parseFloat(usdcBalance))} USDC`;
      }
    }
  };

  return (
    <div className={cn('mx-auto w-full max-w-lg bg-none pb-0')}>
      <Tabs
        defaultValue="buy"
        className="w-full"
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as TradingMode)}
      >
        <TabsList className="grid w-full grid-cols-2 rounded-none border-b border-zinc-800/50 bg-transparent px-0">
          <TabsTrigger
            value="buy"
            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-[#00B38C] data-[state=active]:bg-emerald-500/10 data-[state=active]:text-[#00B38C]"
          >
            {t('buy')}
          </TabsTrigger>
          <TabsTrigger
            value="sell"
            className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-pink-600 data-[state=active]:bg-pink-500/10 data-[state=active]:text-pink-600"
          >
            {t('sell')}
          </TabsTrigger>
        </TabsList>

        <div className="px-3 py-4">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-4">
              {isSellMode ? (
                <>
                  <button
                    onClick={() => setLimitOrderMode(false)}
                    className={cn('text-xs font-bold', !limitOrderMode && 'text-pink-600')}
                  >
                    {t('sell-now')}
                  </button>
                  <button
                    onClick={() => setLimitOrderMode(true)}
                    className={cn('text-xs font-bold', limitOrderMode && 'text-pink-600')}
                  >
                    {t('auto-sell')}
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setLimitOrderMode(false)}
                    className={cn('text-xs font-bold', !limitOrderMode && 'text-[#00B38C]')}
                  >
                    {t('buy-now')}
                  </button>
                  <button
                    onClick={() => setLimitOrderMode(true)}
                    className={cn('text-xs font-bold', limitOrderMode && 'text-[#00B38C]')}
                  >
                    {t('buy-dip')}
                  </button>
                </>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-400">{getBalanceText()}</span>
            </div>
          </div>

          {/* Amount input */}
          <div className="mb-4 bg-zinc-800/20">
            <div className="rounded-t-lg border border-zinc-700/50">
              <div className="flex items-center justify-between p-3">
                <span className="w-12 text-sm text-zinc-400">{t('amount')}</span>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={quoteAmount}
                    placeholder="0.0"
                    onChange={(e) => {
                      setQuoteAmount(e.target.value);
                      setSelectedPresetAmount(null); // Clear selected preset when manually entering amount
                    }}
                    className="w-full bg-transparent text-right text-sm text-white outline-none placeholder:text-zinc-500"
                  />
                  {isSellMode ? (
                    <span className="text-sm text-white">{tokenSymbol}</span>
                  ) : (
                    <div
                      className="flex shrink-0 cursor-pointer items-center gap-1 py-1 pl-3"
                      onClick={() => setSelectedBaseToken(selectedBaseToken === 'SOL' ? 'USDC' : 'SOL')}
                    >
                      <Image
                        src={
                          selectedBaseToken === 'SOL' ? '/chain-icons/icon-solana.png' : '/chain-icons/icon-usdc.png'
                        }
                        alt={selectedBaseToken === 'SOL' ? 'SOL' : 'USDC'}
                        width={20}
                        height={20}
                        className="rounded-full object-contain"
                      />
                      <span className="text-sm text-white">{selectedBaseToken}</span>
                      <ArrowLeftRight size={16} className="text-yellow-400" />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="rounded-b-lg border-x border-b border-zinc-700/50">
              <div className="grid grid-cols-4">
                {presetAmounts.map((amount) => (
                  <button
                    key={amount}
                    className={`rounded-none border-r border-zinc-700/50 py-2 text-xs font-medium text-white hover:bg-zinc-800/20 ${selectedPresetAmount === amount ? 'bg-zinc-800/50 text-white' : ''}`}
                    onClick={() => handleSelectPresetAmount(amount)}
                  >
                    {isSellMode ? `${amount}%` : amount}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Price input */}
          {limitOrderMode && (
            <div className="mb-6">
              <div className="rounded-t-lg border border-zinc-700/50 bg-zinc-800/20">
                <div className="flex items-center justify-between p-3">
                  <div className="flex items-center whitespace-nowrap">
                    <span className="mr-1 text-sm text-zinc-400">
                      {isSellMode ? (roiMode ? t('sell-at-roi') : t('sell-at-price')) : t('buy-at-price')}
                    </span>
                    {isSellMode && (
                      <ArrowLeftRight
                        onClick={() => setRoiMode(!roiMode)}
                        size={16}
                        strokeWidth={2}
                        color="#FFC211"
                        className="flex-shrink-0 cursor-pointer"
                      />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {isSellMode ? (
                      roiMode ? (
                        <>
                          <div className="flex items-center">
                            <span className={`text-sm ${priceSliderValue < 0 ? 'text-[#F8266B]' : 'text-[#00B38C]'}`}>
                              {priceSliderValue >= 0 ? '+' : ''}
                              <input
                                type="text"
                                value={priceSliderValue}
                                onChange={(e) => {
                                  if (e.target.value === '') {
                                    setPriceSliderValue(0);
                                    return;
                                  }
                                  const value = parseInt(e.target.value);
                                  if (!isNaN(value) && (value >= 0 || isSellMode)) {
                                    setPriceSliderValue(value);
                                  }
                                }}
                                className={`w-auto min-w-8 bg-transparent outline-none ${priceSliderValue < 0 ? 'text-[#F8266B]' : 'text-[#00B38C]'}`}
                                style={{ width: `${String(priceSliderValue).length * 8}px` }}
                              />
                              %
                            </span>
                          </div>
                          {/* <span className="text-white">|</span> */}
                          {/* Removed the USD price from here as it's now shown below */}
                        </>
                      ) : (
                        <div className="flex w-full max-w-[150px] items-center justify-between px-2 py-1">
                          <div className="flex-1 overflow-hidden">
                            <input
                              type="text"
                              className="font-inherit m-0 w-full border-none bg-transparent p-0 text-right text-sm text-inherit shadow-none outline-none"
                              value={limitSellPriceInput}
                              onChange={(e) => {
                                setLimitSellPriceInput(e.target.value);
                                const newPrice = parseFloat(e.target.value);
                                if (!isNaN(newPrice)) {
                                  // Calculate what priceSliderValue would result in this limitSellPrice
                                  const basePrice = averagePositionCost === 0 ? tokenPrice : averagePositionCost;
                                  if (basePrice > 0) {
                                    // Calculate the percentage change
                                    const percentageChange = (newPrice / basePrice - 1) * 100;
                                    setPriceSliderValue(Math.round(percentageChange * 100) / 100); // Round to 2 decimal places
                                  }
                                }
                              }}
                            />
                          </div>
                          <span className="ml-1 flex-shrink-0 text-sm text-white">USD</span>
                        </div>
                      )
                    ) : (
                      <div className="flex items-center px-2 py-1">
                        <span className="text-sm text-white">
                          {formatNumber(buyDipPrice, { maximumFractionDigits: 7 })}
                        </span>
                        <span className="ml-1 text-sm text-white">USD</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex cursor-pointer gap-2 rounded-b-lg border-x border-b border-zinc-700/50 bg-zinc-800/20 p-4">
                <div className="flex-1 space-y-4">
                  <Slider
                    value={[priceSliderValue]}
                    min={isSellMode ? 0 : 0}
                    max={isSellMode ? 300 : 100}
                    step={1}
                    className="mb-4 w-full"
                    rangeClass={isSellMode ? 'bg-[#F8266B] h-1' : 'bg-[#00B38C] h-1'}
                    trackClassName="h-1"
                    thumbClassName={
                      isSellMode
                        ? 'bg-pink-600 border-none shadow-sm h-4 w-4'
                        : 'bg-[#00B38C] border-none shadow-sm h-4 w-4'
                    }
                    color={isSellMode ? '#F8266B' : '#00B38C'}
                    onValueChange={(vals) => {
                      setPriceSliderValue(vals[0]);
                      const newPrice = vals[0];
                      if (!isNaN(newPrice)) {
                        // Calculate what priceSliderValue would result in this limitSellPrice
                        const basePrice = averagePositionCost === 0 ? tokenPrice : averagePositionCost;
                        if (basePrice > 0) {
                          // Calculate the percentage change
                          const priceFromPercentage = basePrice * (1 + newPrice / 100);
                          setLimitSellPriceInput(formatNumber(priceFromPercentage, { maximumFractionDigits: 9 }));
                        }
                      }
                    }}
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>{isSellMode ? '0%' : '-0%'}</span>
                    <span>{isSellMode ? '75%' : '-25%'}</span>
                    <span>{isSellMode ? '150%' : '-50%'}</span>
                    <span>{isSellMode ? '175%' : '-75%'}</span>
                    <span>{isSellMode ? '300%' : '-100%'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Current ROI section */}
          {isSellMode && limitOrderMode && averagePositionCost > 0 && (
            <div className="my-2">
              <div className="flex justify-between">
                <span className="text-xs text-gray-400">{t('current-roi')}</span>
                <div className="flex items-center">
                  <span
                    className={cn(
                      'text-sm font-medium',
                      tokenPrice > averagePositionCost ? 'text-emerald-500' : 'text-pink-600',
                    )}
                  >
                    {tokenPrice > averagePositionCost ? '+' : ''}
                    {formatPriceWithDecimals((tokenPrice / averagePositionCost - 1) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Receive Token Type */}
          {isSellMode && (
            <div className="my-2">
              <div className="flex items-center justify-between">
                <span className="text-[0.75rem] text-zinc-400">{t('receive-token-type')}</span>
                <div
                  className="flex cursor-pointer items-center gap-1 rounded-full bg-zinc-800/50 px-2 py-0.5"
                  onClick={() => setSelectedBaseToken(selectedBaseToken === 'SOL' ? 'USDC' : 'SOL')}
                >
                  <Image
                    src={selectedBaseToken === 'SOL' ? '/chain-icons/icon-solana.png' : '/chain-icons/icon-usdc.png'}
                    alt={selectedBaseToken === 'SOL' ? 'SOL' : 'USDC'}
                    width={14}
                    height={14}
                    className="rounded-full object-contain"
                  />
                  <span className="text-[0.75rem] text-white">{selectedBaseToken}</span>
                  <ArrowLeftRight size={12} className="text-yellow-400" />
                </div>
              </div>
            </div>
          )}

          {/* Quote section */}
          <div className="my-2">
            <div className="flex justify-between">
              <span className="text-[0.75rem] text-zinc-400">{t('quote')}</span>
              {quoteAmount == '' || !quoteAmount || +quoteAmount <= 0 ? (
                <div className="flex items-center">-</div>
              ) : (
                <div className="flex items-center">
                  {selectedBaseToken === 'SOL' && (
                    <>
                      <span className="text-[0.75rem] font-normal text-white">
                        {(() => {
                          if (limitOrderMode) {
                            return formatPriceWithDecimals(limitOrderTakingAmount);
                          }
                          if (!quote?.outAmount) {
                            return '0.000000';
                          }
                          const decimals = isSellMode ? 9 : tokenDecimals;
                          return formatPriceWithDecimals(Number(quote.outAmount) / 10 ** decimals);
                        })()}
                      </span>
                      <span className="ml-1 text-[0.75rem] font-normal text-white">
                        {isSellMode ? 'SOL' : tokenSymbol}
                      </span>
                      <span className="mx-1 text-[0.75rem] font-normal text-white">≈</span>
                    </>
                  )}
                  <span className="text-[0.75rem] font-normal text-white">
                    ${quote && +quoteAmount > 0 ? formatPriceWithDecimals(txUsdValue) : ' - '}
                  </span>
                  <span className="ml-1 text-[0.75rem] font-normal text-white">USD</span>
                </div>
              )}
            </div>
          </div>

          {/* Current Price section */}
          <div className="my-2">
            <div className="flex justify-between">
              <span className="text-[0.75rem] text-gray-400">{t('current-price')}</span>
              <div className="flex items-center text-[0.75rem]">
                $&nbsp;
                <span className="text-[0.75rem] font-normal text-white">{formatPriceWithDecimals(tokenPrice)}</span>
                <span className="ml-1 text-[0.75rem] font-normal text-white">USD</span>
              </div>
            </div>
          </div>

          {/* Estimated Sell Price section - only show when in sell mode with ROI */}
          {isSellMode && roiMode && limitOrderMode && (
            <div className="my-2">
              <div className="flex justify-between">
                <span className="text-[0.75rem] text-gray-400">{t('estimated-sell-price')}</span>
                <div className="flex items-center text-[0.75rem]">
                  $&nbsp;
                  <span className="text-[0.75rem] font-normal text-white">
                    {formatNumber(limitSellPrice, { maximumFractionDigits: 7 })}
                  </span>
                  <span className="ml-1 text-[0.75rem] font-normal text-white">USD</span>
                </div>
              </div>
            </div>
          )}

          {/* ROI Hint */}
          {isSellMode &&
            limitOrderMode &&
            averagePositionCost > 0 &&
            (tokenPrice / averagePositionCost - 1) * 100 > +priceSliderValue && (
              <div className="items-top flex gap-2 rounded-lg bg-[#FFC211]/10 p-2">
                <div className="text-white">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM11 8C11 7.44772 11.4477 7 12 7H12.01C12.5623 7 13.01 7.44772 13.01 8C13.01 8.55228 12.5623 9 12.01 9H12C11.4477 9 11 8.55228 11 8ZM12 11C12.5523 11 13 11.4477 13 12V16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16V12C11 11.4477 11.4477 11 12 11Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <div className="text-white">
                  <div className="text-[0.7rem]">
                    {t('roi-above-target')} <br /> {t('sell-now-or-raise-roi')}
                  </div>
                </div>
              </div>
            )}

          {!publicKey ? (
            <button
              onClick={() => openModal()}
              className="mt-4 flex w-full items-center justify-center gap-2 rounded-xl bg-[#FFC211] py-4 text-sm font-bold text-zinc-900"
            >
              <Image src="/ic-line-wallet.svg" alt="Wallet" width={18} height={18} />
              {t('connect-wallet')}
            </button>
          ) : (
            <button
              onClick={limitOrderMode ? handleCreateLimitOrder : handleSwapTransaction}
              disabled={loading || !quoteAmount || +quoteAmount <= 0}
              className={cn(
                'mt-4 w-full rounded-xl bg-[#FFC211] py-4 text-sm font-bold text-zinc-900',
                (loading || !quoteAmount || +quoteAmount <= 0) && 'cursor-not-allowed opacity-50',
              )}
            >
              {loading
                ? t('processing')
                : limitOrderMode
                  ? t('place-limit-order')
                  : isSellMode
                    ? t('sell-token', { token: tokenSymbol, timing: '' })
                    : t('buy-token', { token: tokenSymbol })}
            </button>
          )}
        </div>
      </Tabs>
    </div>
  );
}
