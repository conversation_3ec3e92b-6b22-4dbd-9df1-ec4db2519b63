'use client';

import { useTranslations } from 'next-intl';
import { type FC, useEffect, useState, useMemo } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useActiveLimitOrdersQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useToast } from '@/hooks/use-toast';
import { USDC_ADDRESS, SOL_ADDRESS } from '@/lib/chains';
import { formatNumber } from '@/lib/format';
import { formatJupiterOrders } from '@/lib/format';
import { cancelJupiterOrder } from '@/lib/jupiter';
import { processTransactionData } from '@/lib/sign-transaction';
import * as Sentry from '@sentry/nextjs';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';

interface LimitOrdersProps {
  tokenAddress: string;
}

export const LimitOrderList: FC<LimitOrdersProps> = ({ tokenAddress }) => {
  const t = useTranslations('limit-orders');
  const { address: publicKey } = useDebugWallet();
  const { prices, registerToken } = usePrices();
  const price = prices[`sol:${tokenAddress}`] || 0;
  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 0;
  const usdcPrice = 1.0; // USDC is pegged to USD
  const { sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { toast } = useToast();
  const [tokenDecimals, setTokenDecimals] = useState(6);

  useEffect(() => {
    connection.getAccountInfo(new PublicKey(tokenAddress)).then((res) => setTokenDecimals(res?.data?.at(44) || 0));
  }, [connection, tokenAddress]);

  // Register the token address with the PriceProvider
  useEffect(() => {
    registerToken(tokenAddress);
    registerToken(SOL_ADDRESS);
  }, [tokenAddress, registerToken]);

  // Use the centralized active limit orders query

  // Fetch active limit orders
  const { data: limitOrdersData, isLoading } = useActiveLimitOrdersQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 5000,
  });

  // Map the API response to our LimitOrder interface
  const orders = useMemo(() => {
    if (!limitOrdersData?.orders?.length) return [];

    const solMint = SOL_ADDRESS;
    const usdcMint = USDC_ADDRESS;

    // Process SOL orders
    const solOrders = limitOrdersData.orders.filter((order) => {
      return (
        (order.inputMint === tokenAddress && order.outputMint === solMint) ||
        (order.inputMint === solMint && order.outputMint === tokenAddress)
      );
    });

    // Process USDC orders
    const usdcOrders = limitOrdersData.orders.filter((order) => {
      return (
        (order.inputMint === tokenAddress && order.outputMint === usdcMint) ||
        (order.inputMint === usdcMint && order.outputMint === tokenAddress)
      );
    });

    // Format both types of orders
    const formattedSolOrders = formatJupiterOrders(solOrders, tokenAddress, tokenDecimals, solPrice).map((order) => ({
      ...order,
      baseToken: 'SOL' as const,
    }));

    const formattedUsdcOrders = formatJupiterOrders(usdcOrders, tokenAddress, tokenDecimals, usdcPrice).map(
      (order) => ({ ...order, baseToken: 'USDC' as const }),
    );

    // Combine and sort by date (newest first)
    return [...formattedSolOrders, ...formattedUsdcOrders].sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [limitOrdersData?.orders, tokenAddress, tokenDecimals, solPrice, usdcPrice]);

  const handleCancelOrder = (orderKey: string) => {
    console.log('Canceling order:', orderKey);

    if (publicKey) {
      cancelJupiterOrder(orderKey, publicKey)
        .then(async (x) => {
          if (x.error) throw new Error(x.error.name, { cause: x.error });
          const unSignedTransaction = processTransactionData(x.transaction);
          if (!unSignedTransaction) {
            Sentry.captureException('Jupiter Swap Error');
            toast({
              variant: 'destructive',
              title: t('request-error'),
              description: t('jupiter-swap-error'),
            });
            return;
          }
          return sendTransaction(unSignedTransaction, connection);
        })
        .catch((error: Error) =>
          toast({
            variant: 'destructive',
            title: `${t('request-error')}: ${error.message}`,
            description: JSON.stringify(error.cause),
          }),
        );
    }
  };

  return (
    <div className="bg-transparent p-4">
      {/* <div className="flex items-center justify-between p-4 bg-transparent border-b border-y border-zinc-800">
        <h2 className="text-lg font-semibold text-white">{t('title')}</h2>
      </div> */}

      <div className="overflow-x-auto py-4 md:overflow-x-visible">
        {isLoading ? (
          <div className="flex justify-center py-4">
            <p className="text-zinc-400">{t('loading')}</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="flex justify-center py-4">
            <p className="text-sm text-zinc-600">{t('no-orders')}</p>
          </div>
        ) : (
          <table className="w-[640px] md:w-full">
            <thead>
              <tr className="border-b border-zinc-800 text-left text-[0.65rem] text-zinc-400 md:text-[0.75rem]">
                <th className="min-w-[140px] pb-2 font-normal">{t('type-time')}</th>
                <th className="min-w-[100px] pb-2 font-normal">{t('total-usd')}</th>
                <th className="min-w-[100px] pb-2 font-normal">{t('amount')}</th>
                <th className="min-w-[100px] pb-2 font-normal">{t('price')}</th>
                <th className="min-w-[100px] pb-2 text-right font-normal">{t('cancel-order')}</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order, index) => {
                const formattedDate = order.date.toLocaleDateString('en-US', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                });

                const formattedTime = order.date.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false,
                });

                const isBuy = order.type === 'buy';
                const formattedAmount = formatNumber(order.amount, { maximumFractionDigits: 2 });
                const formattedPrice = `$${formatNumber(order.price, { maximumFractionDigits: 6 })}`;
                // Calculate total USD based on price and amount
                const totalUSD = order.totalUSD || order.amount * price;
                const formattedTotalUSD = `$${formatNumber(totalUSD, { maximumFractionDigits: 2 })}`;

                return (
                  <tr key={index} className="border-b border-zinc-800/50 py-2">
                    <td className="py-2">
                      <div className="mb-1 flex flex-wrap gap-1">
                        <div
                          className={`mb-1 inline-block rounded px-2 py-0.5 text-[0.65rem] ${
                            isBuy ? 'bg-emerald-500/10 text-[#00B38C]' : 'bg-pink-600/20 text-pink-500'
                          }`}
                        >
                          {isBuy ? t('limit-buy') : t('limit-sell')}
                        </div>
                        <div className="mb-1 inline-flex items-center rounded bg-zinc-800/50 px-2 py-0.5 text-[0.65rem] text-white">
                          {order.baseToken === 'SOL' ? (
                            <>
                              <img
                                src="/chain-icons/icon-solana.png"
                                alt="SOL"
                                width="12"
                                height="12"
                                className="mr-1"
                              />
                              <span>SOL</span>
                            </>
                          ) : (
                            <>
                              <img
                                src="/chain-icons/icon-usdc.png"
                                alt="USDC"
                                width="12"
                                height="12"
                                className="mr-1"
                              />
                              <span>USDC</span>
                            </>
                          )}
                        </div>
                        {order.isPartialFill && (
                          <div className="mb-1 inline-block rounded bg-blue-950 px-2 py-0.5 text-[0.65rem] text-blue-400">
                            {t('partial-fill', { default: 'Partial Fill' })}
                          </div>
                        )}
                      </div>
                      <div className="text-[0.65rem] text-zinc-400">
                        {formattedDate} • {formattedTime}
                      </div>
                    </td>
                    <td className={`text-xs ${isBuy ? 'text-emerald-400' : 'text-pink-500'}`}>{formattedTotalUSD}</td>
                    <td className="text-xs text-white">
                      {formattedAmount}
                      {order.isPartialFill && ` (${((1 - order.remainAmount / order.amount) * 100).toFixed(2)}%)`}
                    </td>
                    <td className={`text-xs ${isBuy ? 'text-emerald-400' : 'text-pink-500'}`}>{formattedPrice}</td>
                    <td className="text-right text-xs">
                      <button
                        onClick={() => handleCancelOrder(order.orderKey)}
                        className="rounded bg-zinc-800/80 px-2 py-1 text-xs text-white hover:bg-zinc-700"
                      >
                        {t('cancel')}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};
