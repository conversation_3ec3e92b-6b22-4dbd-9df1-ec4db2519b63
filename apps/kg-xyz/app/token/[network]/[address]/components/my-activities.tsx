'use client';

import { useTranslations } from 'next-intl';
import { type FC, useEffect, useState } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useAssetsQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useTransactions } from '@/hooks/use-transactions';
import { formatNumber } from '@/lib/format';
import { calAveragePositionCost, calculateRealizedPnl } from '@/lib/utils';
import { cn } from '@/lib/utils';
import type { OKXTransaction } from '@/types/okx';

import { LimitOrderList } from './limit-order-list';

interface MyActivitiesProps {
  tokenAddress: string;
}

export const MyActivities: FC<MyActivitiesProps> = ({ tokenAddress }) => {
  const t = useTranslations('my-activities');
  const [activeSection, setActiveSection] = useState<'activities' | 'limit-orders'>('activities');
  const { address: publicKey } = useDebugWallet();
  const { prices, registerToken } = usePrices();
  const price = prices[`sol:${tokenAddress}`];

  // Register the token address with the PriceProvider
  useEffect(() => {
    registerToken(tokenAddress);
  }, [tokenAddress, registerToken]);

  // Use the centralized assets query

  const { data: assets } = useAssetsQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 3000,
  });

  const { transactions } = useTransactions({
    tokenAddress,
    refetchInterval: 3000,
  });

  const currentTokenAsset = assets?.data?.[0]?.tokenAssets?.find(
    (asset) => asset.tokenContractAddress === tokenAddress,
  );

  // Calculate average position cost
  const averagePositionCost = calAveragePositionCost(transactions);

  // Calculate unrealized PnL
  const unrealizedPnl = currentTokenAsset?.balance
    ? +currentTokenAsset.balance * (price ?? 0) - +currentTokenAsset.balance * averagePositionCost
    : 0;

  // Calculate realized PnL
  const realizedPnl = calculateRealizedPnl(transactions);

  // Calculate total PnL (realized + unrealized)
  const totalPnlValue = realizedPnl + unrealizedPnl;

  const formattedTotalPnl =
    totalPnlValue === 0
      ? '--'
      : totalPnlValue > 0
        ? `+$${totalPnlValue.toFixed(2)}`
        : `-$${Math.abs(totalPnlValue).toFixed(2)}`;

  // Calculate PNL percentage
  let pnlPercentage = '';
  let percentageValue = 0;
  if (transactions && transactions.length > 0) {
    // Calculate total investment (sum of buy transactions)
    const totalInvestment = transactions.reduce((sum: number, tx: OKXTransaction) => {
      // In the mock data: type 1 = Buy
      if (tx.type === 1) {
        return sum + parseFloat(tx.turnover);
      }
      return sum;
    }, 0);

    // Calculate percentage only if there's an investment
    if (totalInvestment > 0) {
      percentageValue = (totalPnlValue / totalInvestment) * 100;
      pnlPercentage = percentageValue === 0 ? '' : ' ';
    }
  }

  return (
    <div className="h-full w-full overflow-y-auto p-2 pt-0">
      <div className="sticky top-0 z-10 hidden items-center justify-between border-b border-zinc-800/50 bg-[#101012] p-4 md:block">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveSection('activities')}
            className={cn(
              'font-base hidden text-sm md:inline',
              activeSection === 'activities' ? 'font-medium text-white' : 'text-zinc-400 hover:text-zinc-300',
            )}
          >
            {t('my-activities', { defaultMessage: 'My Activities' })}
          </button>
          <button
            onClick={() => setActiveSection('limit-orders')}
            className={cn(
              'font-base hidden text-sm md:inline',
              activeSection === 'limit-orders' ? 'font-medium text-white' : 'text-zinc-400 hover:text-zinc-300',
            )}
          >
            {t('limit-orders', { defaultMessage: 'Limit Orders' })}
          </button>
        </div>
      </div>

      {activeSection === 'activities' && (
        <>
          <div className="p-4">
            <p className="text-xs text-zinc-400">{t('my-balance', { defaultMessage: 'My Balance (USD)' })}</p>
            <p className="mb-1 text-2xl font-bold text-white">
              {formatNumber(+(currentTokenAsset?.balance ?? 0) * +(price ?? currentTokenAsset?.tokenPrice ?? 0), {
                maximumFractionDigits: 2,
              })}
            </p>
            <p className="flex items-center text-xs text-zinc-400">
              {t('total-pnl', { defaultMessage: 'Total PNL' })}{' '}
              <span className={totalPnlValue >= 0 ? 'text-[#00B38C]' : 'text-rose-600'}>
                {formattedTotalPnl} {pnlPercentage}
                {percentageValue !== 0 && (
                  <>
                    ({percentageValue > 0 ? '+' : ''}
                    {percentageValue.toFixed(2)}%)
                  </>
                )}
              </span>
            </p>
          </div>

          <div className="overflow-x-auto p-4 md:overflow-x-visible">
            <table className="w-[640px] md:w-full">
              <thead>
                <tr className="border-b border-zinc-800 text-left text-[0.65rem] text-zinc-400 md:text-[0.75rem]">
                  <th className="min-w-[140px] pb-2 font-normal">
                    {t('type-time', { defaultMessage: 'Type / Time' })}
                  </th>
                  <th className="min-w-[100px] pb-2 font-normal">{t('total-usd', { defaultMessage: 'Total USD' })}</th>
                  <th className="min-w-[100px] pb-2 font-normal">{t('amount', { defaultMessage: 'Amount' })}</th>
                  <th className="min-w-[100px] pb-2 font-normal">{t('price', { defaultMessage: 'Price' })}</th>
                  <th className="min-w-[100px] pb-2 text-right font-normal">
                    {t('total-profit', { defaultMessage: 'Total Profit' })}
                  </th>
                </tr>
              </thead>
              <tbody>
                {transactions && transactions.length > 0 ? (
                  transactions.map((transaction: OKXTransaction, index: number) => {
                    const date = new Date(transaction.blockTime);
                    const formattedDate = date.toLocaleDateString('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric',
                    });

                    const formattedTime = date.toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false,
                    });
                    // In the mock data: type 0 = Buy, type 1 = Sell
                    // But in calculateRealizedPnl: type 1 = Buy, type 2 = Sell
                    // We'll use the mock data convention here for display purposes
                    const isBuy = transaction.type === 1;
                    const formattedAmount = parseFloat(transaction.amount).toLocaleString(undefined, {
                      maximumFractionDigits: 2,
                    });
                    const formattedPrice = transaction.price ? `$${parseFloat(transaction.price).toFixed(5)}` : '-';
                    const formattedTurnover = transaction.turnover
                      ? `$${parseFloat(transaction.turnover).toFixed(2)}`
                      : '-';
                    const profit = transaction.singleRealizedProfit;
                    const formattedProfit =
                      profit === '0'
                        ? '--'
                        : profit.startsWith('-')
                          ? formatNumber(+profit, { maximumFractionDigits: 2 })
                          : `+${formatNumber(+profit, { maximumFractionDigits: 2 })}`;

                    return (
                      <tr key={index} className="border-b border-zinc-800/50 py-2">
                        <td className="py-2">
                          <div
                            className={`mb-1 mr-1 inline-block rounded px-1.5 py-0 text-[0.65rem] ${
                              isBuy ? 'bg-emerald-500/10 text-[#00B38C]' : 'bg-pink-600/20 text-pink-500'
                            }`}
                          >
                            {isBuy ? t('buy', { defaultMessage: 'Buy' }) : t('sell', { defaultMessage: 'Sell' })}
                          </div>
                          {transaction.isLimitedOrder && (
                            <div className="mb-1 mr-1 inline-block rounded bg-blue-950 px-1 py-0 text-[0.65rem] text-blue-400">
                              {t('limit-order', { defaultMessage: 'Limit' })}
                            </div>
                          )}
                          {transaction.isPartialFill && (
                            <div className="mb-1 mr-1 inline-block rounded bg-blue-950 px-1 py-0 text-[0.65rem] text-blue-400">
                              {t('partial-fill', { defaultMessage: 'Partial Fill' })}
                            </div>
                          )}
                          <div className="text-[0.65rem] text-zinc-400">
                            {formattedDate} • {formattedTime}
                          </div>
                        </td>
                        <td className={`mr-1 text-xs ${isBuy ? 'text-[#00B38C]' : 'text-pink-500'}`}>
                          {formattedTurnover}
                        </td>
                        <td className="text-xs text-white">
                          {formattedAmount}
                          {transaction.isPartialFill && transaction.internalTradingFill
                            ? ` (${((+transaction.internalTradingFill / +transaction.amount) * 100).toFixed()}%)`
                            : ''}
                        </td>
                        <td className={`text-xs ${isBuy ? 'text-[#00B38C]' : 'text-pink-500'}`}>{formattedPrice}</td>
                        <td
                          className={`text-right text-sm ${
                            formattedProfit === '--'
                              ? 'text-zinc-400'
                              : formattedProfit.startsWith('+')
                                ? 'text-[#00B38C]'
                                : 'text-pink-500'
                          }`}
                        >
                          {formattedProfit}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={5} className="py-8 text-center text-sm text-zinc-500">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </>
      )}

      {activeSection === 'limit-orders' && <LimitOrderList tokenAddress={tokenAddress} />}
    </div>
  );
};
