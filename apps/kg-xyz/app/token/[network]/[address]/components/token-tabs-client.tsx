'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { TradingViewChart } from '@/components/TradingViewChart';
import { TokenTabs, TokenTabsList, TokenTabsTrigger, TokenTabsContent } from '@/components/token-tabs';
import { TradingInfo } from '@/components/trading-info';
import { Button } from '@/components/ui/button';
import { Drawer, DrawerContent, DrawerClose } from '@/components/ui/drawer';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useSuspectInsider } from '@/contexts/suspect-insider-context';
import { cn } from '@/lib/utils';
import type { OKXTokenDetail, OKXTokenOverview } from '@/types/okx';

import { TokenHolding } from '../token-holding';
import { LimitOrderList } from './limit-order-list';
import { MyActivities } from './my-activities';
import { SocialLinks } from './social-links';
import { TokenInfo } from './token-info';
import { TradingSection } from './trading-section';

interface TokenTabsClientProps {
  token: OKXTokenDetail;
  tokenOverview: OKXTokenOverview;
}

export function TokenTabsClient({ token, tokenOverview }: TokenTabsClientProps) {
  const t = useTranslations('token-page.tabs');
  const tradingT = useTranslations('trading-section');
  const [isMobile, setIsMobile] = useState(false);
  const [isTradeModalOpen, setIsTradeModalOpen] = useState(false);
  const { isOpen: isSuspectInsiderOpen } = useSuspectInsider();

  // Check Screen Size
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Listen to window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup listener
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Handle trade button click
  const handleTradeClick = () => {
    setIsTradeModalOpen(true);
  };

  return (
    <>
      <TokenTabs defaultValue="charts" className="w-full">
        <TokenTabsList className="w-full">
          <TokenTabsTrigger value="charts" className="flex-1 text-[0.75rem]">
            {t('charts')}
          </TokenTabsTrigger>
          <TokenTabsTrigger value="info" className="flex-1 text-[0.75rem]">
            {t('info')}
          </TokenTabsTrigger>
          <TokenTabsTrigger value="buy" className="flex-1 text-[0.75rem] md:hidden">
            {t('my-position')}
          </TokenTabsTrigger>
        </TokenTabsList>
        <TokenTabsContent value="charts">
          <div className={cn('h-[calc(100vh-250px)]', isMobile && 'mb-24')}>
            <TradingViewChart
              exchange={token.chainName}
              tokenAddress={token.tokenContractAddress}
              chainId={token.chainName === 'Solana' ? '501' : '1'}
            />
          </div>
        </TokenTabsContent>
        <TokenTabsContent value="info" className="bg-zinc-950">
          <div className={cn(isMobile && 'mb-16')}>
            <Tabs defaultValue="info" className="w-full">
              <div className="mb-4 ml-3 overflow-x-auto pb-1">
                <TabsList className="inline-flex w-auto space-x-2 bg-transparent p-0">
                  <TabsTrigger
                    value="info"
                    className="rounded-full border border-zinc-800/50 bg-zinc-900/50 px-4 py-1.5 text-xs data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                  >
                    {t('token-info')}
                  </TabsTrigger>
                  <TabsTrigger
                    value="stats"
                    className="rounded-full border border-zinc-800/50 bg-zinc-900/50 px-4 py-1.5 text-xs data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                  >
                    {t('1hr-stats')}
                  </TabsTrigger>
                </TabsList>
              </div>
              <div className="mx-4">
                <TabsContent value="info" className="mb-24 mt-0">
                  <TokenInfo token={token} className="mb-12" />
                  <SocialLinks
                    socialMedia={tokenOverview.socialMedia}
                    isPumpFun={tokenOverview.basicInfo.isMeme === '1'}
                    tokenAddress={tokenOverview.basicInfo.tokenContractAddress}
                  />
                </TabsContent>
                <TabsContent value="stats" className="mb-24 mt-0">
                  <TradingInfo className="py-2" />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </TokenTabsContent>
        <TokenTabsContent value="buy" className="bg-zinc-950">
          <div className={cn(isMobile && 'mb-16')}>
            <Tabs defaultValue="holdings" className="w-full">
              <div className="mb-4 ml-3 overflow-x-auto pb-1">
                <TabsList className="inline-flex w-auto space-x-2 bg-transparent p-0">
                  <TabsTrigger
                    value="holdings"
                    className="rounded-full border border-zinc-800/50 bg-zinc-900/50 px-4 py-1.5 text-xs data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                  >
                    {t('my-holdings')}
                  </TabsTrigger>
                  <TabsTrigger
                    value="activities"
                    className="rounded-full border border-zinc-800 bg-zinc-900/50 px-4 py-1.5 text-xs data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                  >
                    {t('my-activities')}
                  </TabsTrigger>
                  <TabsTrigger
                    value="limit-orders"
                    className="rounded-full border border-zinc-800 bg-zinc-900/50 px-4 py-1.5 text-xs data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                  >
                    {t('limit-orders')}
                  </TabsTrigger>
                </TabsList>
              </div>
              <TabsContent value="holdings" className="mt-0">
                <TokenHolding tokenAddress={token.tokenContractAddress} />
              </TabsContent>
              <TabsContent value="activities" className="mt-0">
                <MyActivities tokenAddress={token.tokenContractAddress} />
              </TabsContent>
              <TabsContent value="limit-orders" className="mt-0">
                <LimitOrderList tokenAddress={token.tokenContractAddress} />
              </TabsContent>
            </Tabs>
          </div>
        </TokenTabsContent>
      </TokenTabs>

      {/* Fixed bottom trade button block - only shown on mobile and when SuspectInsider modal is not open */}
      {isMobile && !isSuspectInsiderOpen && (
        <div className="fixed bottom-0 left-0 right-0 z-10 flex h-16 items-center justify-center border-none bg-zinc-900/40 px-4 py-2 shadow-lg backdrop-blur-xl">
          <Button
            onClick={handleTradeClick}
            className="w-full rounded-xl bg-[#FFC211]/15 py-4 text-sm font-bold text-[#FFC211] hover:bg-zinc-800/50"
          >
            {tradingT('trade')}
          </Button>
        </div>
      )}

      {/* 交易彈出框 - 使用 Drawer 組件 */}
      <Drawer open={isTradeModalOpen} onOpenChange={setIsTradeModalOpen}>
        <DrawerContent className="h-auto max-h-[90vh] border-t border-zinc-800/50 bg-zinc-950/95 backdrop-blur-md before:hidden">
          <div className="top-0 z-10 hidden items-center justify-between border-b border-zinc-800/50 bg-zinc-950 px-4 py-4 md:flex">
            <div className="w-10"></div>
            <h3 className="text-base font-medium text-white">{tradingT('buy-token', { token: token.tokenSymbol })}</h3>
            <DrawerClose className="flex h-10 w-10 items-center justify-center rounded-full text-zinc-400 hover:bg-zinc-800/50 hover:text-white">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M18 6L6 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M6 6L18 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </DrawerClose>
          </div>
          <div className="max-h-[calc(100vh-60px)] overflow-y-auto p-4">
            <TradingSection inDetailPage tokenAddress={token.tokenContractAddress} tokenSymbol={token.tokenSymbol} />
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
}
