/* eslint-disable @next/next/no-img-element */
import { Share2 } from 'lucide-react';
import * as motion from 'motion/react-client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useEffect, useState } from 'react';

import { CopyButton } from '@/components/copy-button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { usePrices } from '@/contexts/price-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { formatNumber } from '@/lib/format';
import { cn } from '@/lib/utils';
import type { SignalResponse } from '@/types/kg-api';
import type { OKXTokenDetail } from '@/types/okx';
import { formatTimeAgo } from '@/utils/time';
import { TooltipArrow } from '@radix-ui/react-tooltip';

import { SuspectInsiderModal } from './suspect-insider-modal';
import { TokenPrice } from './token-price';

interface TokenHeaderProps {
  token: OKXTokenDetail;
  className?: string;
  signal?: SignalResponse;
  tokenOverview?: any;
  showTimeAgo?: boolean;
}

export function TokenHeader({ token, signal, tokenOverview, showTimeAgo = false, className }: TokenHeaderProps) {
  const [open, setOpen] = useState(false);
  // 獲取 chainId
  const chainId = token?.chainName === 'Solana' ? '501' : '1';
  // Get translations
  const t = useTranslations('signal-card');
  const marketStatsT = useTranslations('market-stats');
  const { address: publicKey } = useDebugWallet();

  // Check if we have a signal for this token
  const hasSignal = !!signal;

  // Set to false to hide signal elements
  const showSignalElements = false;

  // Get prices from the PriceProvider
  const { prices, registerToken } = usePrices();

  // Register the token with the PriceProvider
  useEffect(() => {
    if (token.tokenContractAddress) {
      registerToken(token.tokenContractAddress);
    }
  }, [token.tokenContractAddress, registerToken]);

  // Get the token price from the PriceProvider
  const tokenKey = `sol:${token.tokenContractAddress}`;
  const tokenPrice = prices[tokenKey] || 0;
  const buyEntryPrice = hasSignal ? signal.buy_entry_price || 0 : 0;
  const priceSinceSignal =
    hasSignal && buyEntryPrice > 0 && tokenPrice > 0 ? ((tokenPrice - buyEntryPrice) / buyEntryPrice) * 100 : 0;

  // Format the values
  const averageHolding = hasSignal ? signal.average_holding : 1;
  const averageWinRate = hasSignal ? signal.average_win_rate : 0.57;

  const signalDuration =
    signal && signal.emit_time ? formatTimeAgo(Date.now() - new Date(+signal.emit_time * 1000).getTime()) : null;

  // Calculate token age
  const createTime = tokenOverview?.memeInfo?.createTime;
  const timeDiff = createTime ? Date.now() - new Date(+createTime).getTime() : null;
  const timeAgo = timeDiff ? formatTimeAgo(timeDiff) : null;

  const maxGainAfterSignal =
    signal && +signal.highest_price > 0 && +signal.buy_entry_price > 0
      ? ((+signal.highest_price - +signal.buy_entry_price) / +signal.buy_entry_price) * 100
      : 0.0;

  return (
    <TooltipProvider>
      <div className={cn('flex flex-col', className)}>
        <div className="flex flex-col">
          <div className="relative flex w-full items-center p-4">
            <div className="flex items-center gap-x-8">
              {/* Star Icon Button - Only visible on mobile */}
              <div className="mr-1 block md:hidden">
                <div
                  className="flex cursor-pointer items-center rounded-md border border-neutral-800 p-1.5"
                  onClick={() => setOpen(true)}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{
                      scale: 1.5,
                      rotate: 15,
                    }}
                    transition={{
                      duration: 0.4,
                      scale: { type: 'spring', visualDuration: 0.6, bounce: 0.7 },
                    }}
                    className="cursor-pointer"
                  >
                    <Image src="/ic-solid-star-01.svg" alt="Star" width={20} height={20} className="text-[#FFC211]" />
                  </motion.div>
                </div>
              </div>

              <div className="flex items-center">
                <div className="h-4 w-4 overflow-hidden rounded-full md:h-8 md:w-8">
                  <img
                    src={token.tokenLogoUrl || '/placeholder-token.svg'}
                    alt={token.tokenSymbol || 'Token'}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div className="ml-2 flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-white">{token.tokenSymbol}</span>
                    {showTimeAgo && timeAgo && (
                      <div className="rounded-md bg-zinc-900 px-1.5 py-0.5 text-[0.65rem] text-neutral-400">
                        {timeAgo}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="max-w-[120px] truncate text-xs text-zinc-400">
                      {token.tokenContractAddress
                        ? `${token.tokenContractAddress.substring(0, 6)}...${token.tokenContractAddress.substring(token.tokenContractAddress.length - 4)}`
                        : 'N/A'}
                    </span>
                    <span className="cursor-pointer">
                      <CopyButton text={token.tokenContractAddress || ''} className="h-4 w-4" />
                    </span>
                  </div>
                </div>
              </div>
              {/* Token Price */}
              <div className="hidden items-center gap-6 md:flex">
                <div className="flex flex-col items-center">
                  <TokenPrice
                    tokenAddress={token.tokenContractAddress}
                    initialChange={token.change}
                    className="items-end"
                  />
                </div>
              </div>
              {/* Token Metrics and Share Button - Right Side */}
              <div className="absolute right-4 flex items-center justify-end gap-4">
                {/* Token Metrics */}
                <div className="hidden items-center justify-end gap-6 md:flex">
                  <div className="flex flex-col items-center">
                    <span className="text-xs text-zinc-400">{marketStatsT('market-cap')}</span>
                    <span className="text-sm font-medium text-white">
                      $
                      {formatNumber(parseFloat(token.marketCap || '0'), {
                        notation: 'compact',
                        maximumFractionDigits: 1,
                      })}
                    </span>
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="text-xs text-zinc-400">{marketStatsT('liquidity')}</span>
                    <span className="text-sm font-medium text-white">
                      $
                      {formatNumber(parseFloat(token.liquidity || '0'), {
                        notation: 'compact',
                        maximumFractionDigits: 1,
                      })}
                    </span>
                  </div>
                  {/* <div className="flex flex-col items-center">
                    <span className="text-[0.65rem] text-zinc-400">Supply</span>
                    <span className="text-xs font-medium text-white">
                      {formatNumber(parseFloat(token.circulatingSupply || '0'), {
                        notation: 'compact',
                        maximumFractionDigits: 1,
                      })}
                    </span>
                  </div> */}
                  <div className="flex flex-col items-center">
                    <span className="text-xs text-zinc-400">{marketStatsT('holders')}</span>
                    <span className="text-sm font-medium text-white">
                      {formatNumber(parseFloat(token.holders || '0'), {
                        notation: 'compact',
                        maximumFractionDigits: 0,
                      })}
                    </span>
                  </div>
                  {/* <div className="flex flex-col items-center">
                    <span className="text-[0.65rem] text-zinc-400">Top 10</span>
                    <span className="text-xs font-medium text-white">
                      {token.top10HoldAmountPercentage
                        ? parseFloat(token.top10HoldAmountPercentage).toFixed(2) + '%'
                        : 'N/A'}
                    </span>
                  </div> */}
                </div>
                {/* Share Button */}
                <div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="flex h-8 w-8 items-center justify-center rounded-full bg-zinc-800 text-zinc-300 hover:bg-zinc-700 hover:text-white"
                        onClick={async () => {
                          const url = window.location.href;
                          try {
                            const imgUrl = `/token/501/${token.tokenContractAddress}/trading-meta/${publicKey}`;
                            const response = await fetch(imgUrl);
                            const blob = await response.blob();
                            const file = new File([blob], `${token.tokenSymbol || 'token'}-meta.png`, {
                              type: 'image/png',
                            });

                            // For downloading directly (fallback if navigator.share not available)
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(file);
                            link.download = file.name;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(link.href);

                            if (navigator.share) {
                              await navigator.share({
                                title: `${token.tokenSymbol || 'Token'} | KryptoGo`,
                                text: `Check out ${token.tokenSymbol || 'this token'} on KryptoGO`,
                                url: url,
                                files: [file],
                              });
                            } else {
                              await navigator.clipboard.writeText(url);
                              // Could add a toast notification here
                            }
                          } catch (err) {
                            console.error('Failed to share:', err);
                          }
                        }}
                      >
                        <Share2 className="h-4 w-4" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Share this token</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>
            </div>
            {hasSignal && showSignalElements && (
              <div className="flex flex-col items-center">
                <span className="text-sm text-zinc-500">{t('signal-duration')}</span>
                <div className="flex items-center">
                  <span
                    className={cn(
                      'text-2xl font-bold',
                      signalDuration?.includes('s') ||
                        (signalDuration?.includes('m') && +signalDuration.slice(0, -1) <= 10 && 'text-amber-500'),
                    )}
                  >
                    {signalDuration}
                  </span>
                </div>
              </div>
            )}

            {hasSignal && showSignalElements && (
              <div className="flex flex-col items-center">
                <span className="text-sm text-zinc-500">{t('max-gain-after-alert')}</span>
                <div className="text-2xl font-bold text-emerald-400">
                  + {formatNumber(maxGainAfterSignal, { maximumFractionDigits: 0 })}%
                </div>
              </div>
            )}

            {/* Hide Current Telegram Link */}
            {/* <a
            href={hasSignal && signal?.telegram_link ? signal.telegram_link : '#'}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center"
          >
            <ExternalLink className="w-6 h-6 text-zinc-400 hover:text-zinc-200" />
          </a> */}
          </div>
        </div>

        {/* Hide Current Signal elements */}
        {hasSignal && showSignalElements && (
          <div className="grid grid-cols-3 gap-4 border-zinc-800 px-4 py-3">
            <Tooltip delayDuration={200}>
              <TooltipTrigger asChild>
                <div className="flex h-24 cursor-pointer flex-col items-center justify-between rounded-lg bg-zinc-900 p-3">
                  <span className="text-sm text-zinc-500 underline">{t('price-since-signal')}</span>
                  <div
                    className={cn(
                      'text-3xl font-semibold',
                      priceSinceSignal >= 0 ? 'text-emerald-400' : 'text-rose-500',
                    )}
                  >
                    {priceSinceSignal >= 0 ? '+' : ''}
                    {formatNumber(priceSinceSignal, { maximumFractionDigits: 2 })}%
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-0">
                <TooltipArrow className="fill-[#3a3a3a]" width={15} />
                <div className="relative w-[500px] rounded-lg border-none">
                  <div className="rounded-lg bg-[#3a3a3a] p-6">
                    {t('price-since-signal-desc', {
                      defaultMessage: 'This shows the price change since the signal was triggered.',
                    })}
                    <hr className="my-2 border-zinc-500/50" />
                    <p className="max-w-xl text-zinc-400">
                      {t('price-since-signal-detail', {
                        defaultMessage:
                          'A positive percentage indicates the token price has increased since the signal, while a negative percentage indicates the price has decreased.',
                      })}
                    </p>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>

            <Tooltip delayDuration={200}>
              <TooltipTrigger asChild>
                <div className="flex h-24 cursor-pointer flex-col items-center justify-between rounded-lg bg-zinc-900 p-3">
                  <span className="text-sm text-zinc-500 underline">{t('smart-money-holdings')}</span>
                  <div className={cn('text-3xl font-semibold', averageHolding > 0.7 && 'text-yellow-500')}>
                    {formatNumber(averageHolding * 100, { maximumFractionDigits: 0 })}%
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-0">
                <TooltipArrow className="fill-[#3a3a3a]" width={15} />
                <div className="relative w-[500px] rounded-lg border-none">
                  <div className="rounded-lg bg-[#3a3a3a] p-6">
                    {t('smart-money-holdings-desc')}
                    <hr className="my-2 border-zinc-500/50" />
                    <p className="max-w-xl text-zinc-400">
                      {t('smart-money-holdings-detail', {
                        defaultMessage:
                          'A higher percentage indicates stronger interest from experienced traders and investors. Values above 70% are highlighted as particularly significant.',
                      })}
                    </p>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>

            <Tooltip delayDuration={200}>
              <TooltipTrigger asChild>
                <div className="flex h-24 cursor-pointer flex-col items-center justify-between rounded-lg bg-zinc-900 p-3">
                  <span className="text-sm text-zinc-500 underline">{t('smart-money-win-rate')}</span>
                  <div className={cn('text-3xl font-semibold', averageWinRate > 0.7 && 'text-yellow-500')}>
                    {formatNumber(averageWinRate * 100, { maximumFractionDigits: 0 })}%
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-0">
                <TooltipArrow className="fill-[#3a3a3a]" width={15} />
                <div className="relative w-[500px] rounded-lg border-none">
                  <div className="rounded-lg bg-[#3a3a3a] p-6">
                    {t('smart-money-win-rate-desc')}
                    <hr className="my-2 border-zinc-500/50" />
                    <p className="max-w-xl text-zinc-400">
                      {t('smart-money-win-rate-detail', {
                        defaultMessage:
                          'Win rates above 70% are highlighted as they indicate a strong track record of successful trading by smart money wallets.',
                      })}
                    </p>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
        )}
      </div>
      {/* Suspect Insider Modal - Mobile View */}
      {open && token && (
        <SuspectInsiderModal open={open} onCloseAction={() => setOpen(false)} chainId={chainId} token={token} />
      )}
    </TooltipProvider>
  );
}
