'use client';

/* eslint-disable @next/next/no-img-element */
import * as motion from 'motion/react-client';
import Image from 'next/image';
import { useState } from 'react';

import { CopyButton } from '@/components/copy-button';
import { cn } from '@/lib/utils';
import type { OKXTokenDetail } from '@/types/okx';
import type { OKXTokenOverview } from '@/types/okx';
import { formatTimeAgo } from '@/utils/time';

import { SuspectInsiderModal } from './suspect-insider-modal';

interface WarrenTokenProps {
  className?: string;
  token: OKXTokenDetail;
  tokenOverview: OKXTokenOverview;
  showTimeAgo?: boolean;
  noBorder?: boolean;
}

export function TokenMainInfo({
  className = '',
  token,
  tokenOverview,
  showTimeAgo = true,
  noBorder = false,
}: WarrenTokenProps) {
  const [open, setOpen] = useState(false);
  // 獲取 chainId
  const chainId = token?.chainName === 'Solana' ? '501' : '1';

  const createTime = tokenOverview?.memeInfo?.createTime;
  const timeDiff = createTime ? Date.now() - new Date(+createTime).getTime() : null;
  const timeAgo = timeDiff ? formatTimeAgo(timeDiff) : null;

  return (
    <>
      <div
        className={cn(
          'flex items-center justify-between px-0 pb-2 md:px-2 md:pb-4',
          !noBorder && 'border-b border-zinc-800',
          className,
        )}
      >
        <div className="flex items-center md:gap-2">
          {/* Star Icon Button - Only visible on mobile */}
          <div className="-ml-1.5 mr-2 block md:hidden">
            <div
              className="flex cursor-pointer items-center rounded-md border border-neutral-800 p-1"
              onClick={() => setOpen(true)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{
                  scale: 1.5,
                  rotate: 15,
                }}
                transition={{
                  duration: 0.4,
                  scale: { type: 'spring', visualDuration: 0.6, bounce: 0.7 },
                }}
                className="cursor-pointer"
              >
                <Image src="/ic-solid-star-01.svg" alt="Star" width={16} height={16} className="text-[#FFC211]" />
              </motion.div>
            </div>
          </div>

          <div className="mr-2 h-8 w-8 overflow-hidden rounded-full md:h-10 md:w-10">
            <img src={token.tokenLogoUrl} alt={token.tokenSymbol} className="h-full w-full object-cover" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-xs font-semibold text-white md:text-sm">{token.tokenSymbol}</h3>
              {showTimeAgo && timeAgo && (
                <div className="rounded-md bg-zinc-800/50 px-1.5 py-0.5 text-[0.6rem] text-neutral-400 md:px-2 md:text-xs">
                  {timeAgo}
                </div>
              )}
            </div>
            <div className="flex items-center gap-0 md:gap-1">
              <span className="text-[0.65rem] text-zinc-500 md:text-xs">
                {token.tokenContractAddress.slice(0, 7)}...{token.tokenContractAddress.slice(-5)}
              </span>
              <CopyButton text={token.tokenContractAddress} />
            </div>
          </div>
        </div>
      </div>

      {/* Suspect Insider Modal */}
      {open && token && (
        <SuspectInsiderModal open={open} onCloseAction={() => setOpen(false)} chainId={chainId} token={token} />
      )}
    </>
  );
}
