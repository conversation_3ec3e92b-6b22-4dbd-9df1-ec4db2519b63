'use client';

import { useTranslations } from 'next-intl';
import * as React from 'react';

import { TradingInfo } from '@/components/trading-info';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { formatNumber } from '@/lib/format';

import { SocialLinks } from './social-links';
import { TokenInfo } from './token-info';

interface TokenTabContainerProps {
  token: {
    marketCap: string;
    volume: string;
    liquidity: string;
    circulatingSupply: string;
    holders: string;
    top10HoldAmountPercentage: string;
    tokenSymbol: string;
    tokenContractAddress: string;
    chainLogoUrl: string;
    chainName: string;
    tokenName: string;
  };
  tokenOverview: {
    socialMedia: any;
    basicInfo: {
      isMeme: string;
      tokenContractAddress: string;
    };
  };
}

export function TokenTabContainer({ token, tokenOverview }: TokenTabContainerProps) {
  const t = useTranslations('market-stats');
  const tTradingInfo = useTranslations('trading-info');

  return (
    <div className="flex h-full flex-col">
      <div className="top-0 z-10 -mx-3 bg-transparent">
        <Tabs defaultValue="token-info" className="w-full">
          <TabsList className="sticky top-0 z-10 flex w-full justify-start rounded-none border-b border-zinc-800/50 bg-[#101012] px-4 pb-4 pt-6">
            <div className="relative">
              <TabsTrigger
                value="token-info"
                className="relative border-0 bg-transparent p-0 pb-4 text-sm shadow-none hover:text-zinc-300 data-[state=active]:bg-transparent data-[state=active]:font-medium data-[state=active]:text-white data-[state=inactive]:text-zinc-400"
              >
                {t('token-info')}
                <div className="bg-primary absolute -bottom-[1px] left-0 h-0.5 w-full opacity-0 data-[state=active]:opacity-100" />
              </TabsTrigger>
            </div>
            <div className="relative ml-4">
              <TabsTrigger
                value="1hr-stats"
                className="relative border-0 bg-transparent p-0 pb-4 text-sm shadow-none hover:text-zinc-300 data-[state=active]:bg-transparent data-[state=active]:font-medium data-[state=active]:text-white data-[state=inactive]:text-zinc-400"
              >
                {tTradingInfo('tab-title')}
                <div className="bg-primary absolute -bottom-[1px] left-0 h-0.5 w-full opacity-0 data-[state=active]:opacity-100" />
              </TabsTrigger>
            </div>
          </TabsList>

          {/* <div className="w-full mt-1 border-b border-zinc-800/50"></div> */}

          <TabsContent value="token-info" className="mt-3 px-4">
            <div className="overflow-y-auto pr-1 lg:flex-col" style={{ scrollbarWidth: 'thin' }}>
              <div className="grid flex-1 gap-4 sm:grid-cols-2">
                <div className="flex flex-col gap-1">
                  <span className="text-[0.7rem] text-zinc-400 md:text-sm">{t('market-cap')}</span>
                  <span className="text-base font-medium text-white">
                    ${formatNumber(parseFloat(token.marketCap), { notation: 'compact', maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-[0.7rem] text-zinc-400 md:text-sm">{t('24h-volume')}</span>
                  <span className="text-base font-medium text-white">
                    ${formatNumber(parseFloat(token.volume), { notation: 'compact', maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-[0.7rem] text-zinc-400 md:text-sm">{t('liquidity')}</span>
                  <span className="text-base font-medium text-white">
                    ${formatNumber(parseFloat(token.liquidity), { notation: 'compact', maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-[0.7rem] text-zinc-400 md:text-sm">{t('circulating-supply')}</span>
                  <span className="text-base font-medium text-white">
                    {formatNumber(parseFloat(token.circulatingSupply), {
                      notation: 'compact',
                      maximumFractionDigits: 0,
                    })}{' '}
                    {token.tokenSymbol}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-xs text-zinc-400 md:text-sm">{t('holders')}</span>
                  <span className="text-base font-medium text-white">
                    {formatNumber(parseFloat(token.holders), { notation: 'compact', maximumFractionDigits: 0 })}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-xs text-zinc-400 md:text-sm">{t('top-10-holders')}</span>
                  <span className="text-base font-medium text-white">
                    {formatNumber(parseFloat(token.top10HoldAmountPercentage), {
                      notation: 'compact',
                      maximumFractionDigits: 2,
                    })}
                    %
                  </span>
                </div>
              </div>

              {/* About 和 Social Media 部分 */}
              <div className="mt-8">
                <TokenInfo token={token} />
              </div>

              <div className="mb-12 mt-8">
                <SocialLinks
                  socialMedia={tokenOverview.socialMedia}
                  isPumpFun={tokenOverview.basicInfo.isMeme === '1'}
                  tokenAddress={tokenOverview.basicInfo.tokenContractAddress}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="1hr-stats" className="mt-3">
            <TradingInfo />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
