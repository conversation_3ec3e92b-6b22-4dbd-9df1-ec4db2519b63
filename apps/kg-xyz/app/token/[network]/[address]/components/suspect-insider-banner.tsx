'use client';

import * as motion from 'motion/react-client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import { OKXTokenDetail } from '@/types/okx';
import { sendGTMEvent } from '@next/third-parties/google';

import { SuspectInsiderModal } from './suspect-insider-modal';

interface SuspectInsiderBannerProps {
  chainId: string;
  token: OKXTokenDetail;
}

export const SuspectInsiderBanner: React.FC<SuspectInsiderBannerProps> = ({ chainId, token }) => {
  const searchParams = useSearchParams();
  const openModal = searchParams.get('openModal');
  const [open, setOpen] = useState(openModal === 'true');
  const t = useTranslations();

  return (
    <>
      <div
        className="hidden cursor-pointer overflow-hidden rounded-md bg-zinc-900/10 md:block"
        onClick={() => {
          sendGTMEvent({
            event: 'app_xyz_insider_analyze_click',
            token_address: token.tokenContractAddress,
          });
          setOpen(true);
        }}
      >
        <div className="bg-zinc-900/50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-zinc-300">{t('suspect-insider.analysis')}</span>
              <span className="rounded bg-yellow-600/20 px-1.5 py-0.5 text-[0.7rem] font-medium text-yellow-500">
                {t('suspect-insider.new-tag')}
              </span>
            </div>
            <div className="flex items-center rounded-md border border-neutral-800 p-1.5">
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{
                  scale: 1.5,
                  rotate: 15,
                }}
                transition={{
                  duration: 0.4,
                  scale: { type: 'spring', visualDuration: 0.6, bounce: 0.7 },
                }}
                className="cursor-pointer"
              >
                <Image src="/ic-solid-star-01.svg" alt="Star" width={20} height={20} className="text-[#FFC211]" />
              </motion.div>
            </div>
          </div>
        </div>
      </div>
      <SuspectInsiderModal open={open} onCloseAction={() => setOpen(false)} chainId={chainId} token={token} />
    </>
  );
};
