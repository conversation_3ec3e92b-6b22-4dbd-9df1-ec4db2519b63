'use client';

import { useEffect } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useBuySignals } from '@/hooks/use-signals';
import type { OKXTokenDetail } from '@/types/okx';

import { TokenHeader } from './token-header';

interface TokenHeaderWithSignalProps {
  tokenAddress: string;
  token: OKXTokenDetail;
  tokenOverview?: any;
}

export function TokenHeaderWithSignal({ tokenAddress, token, tokenOverview }: TokenHeaderWithSignalProps) {
  const { registerToken } = usePrices();
  const { data: buySignals } = useBuySignals();

  // Register the token address with the PriceProvider
  useEffect(() => {
    registerToken(tokenAddress);
  }, [tokenAddress, registerToken]);

  // Find the signal for the current token
  const currentSignal = buySignals?.find((signal) => signal.token_address === tokenAddress);

  return <TokenHeader token={token} signal={currentSignal} tokenOverview={tokenOverview} showTimeAgo />;
}
