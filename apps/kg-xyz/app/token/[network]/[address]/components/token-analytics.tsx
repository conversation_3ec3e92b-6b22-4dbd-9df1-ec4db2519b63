'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { sendGTMEvent } from '@next/third-parties/google';

export function TokenAnalytics({ chainId, address }: { chainId: string; address: string }) {
  const searchParams = useSearchParams();
  const refCode = searchParams.get('ref');
  const [eventLogged, setEventLogged] = useState(false);

  useEffect(() => {
    // Check if GA is loaded
    if (typeof window === 'undefined') {
      console.warn('GA not loaded yet');
      return;
    }

    if (refCode && !eventLogged) {
      sendGTMEvent({
        event: 'app_xyz_referral_click',
        chain_id: chainId,
        token_address: address,
        referral_code: refCode,
        platform: 'web',
      });

      setEventLogged(true);
    }
  }, [address, chainId, refCode, eventLogged]);

  return null;
}
