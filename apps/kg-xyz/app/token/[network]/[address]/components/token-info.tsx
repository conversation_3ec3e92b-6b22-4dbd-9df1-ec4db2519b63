'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { formatNumber } from '@/lib/format';

interface TokenInfoProps {
  token: {
    chainLogoUrl: string;
    chainName: string;
    tokenContractAddress: string;
    tokenName: string;
    tokenSymbol: string;
    marketCap: string;
    liquidity: string;
    circulatingSupply: string;
    holders: string;
    top10HoldAmountPercentage: string;
    volume24h?: string;
  };
  className?: string;
}

export function TokenInfo({ token, className }: TokenInfoProps) {
  const t = useTranslations('market-stats');
  const tTokenInfo = useTranslations('token-info');
  const [copied, setCopied] = useState(false);

  return (
    <div className={`space-y-12 rounded-lg p-0 ${className || ''}`}>
      {/* Market Stats Section - First */}
      <div>
        <div className="block grid grid-cols-2 gap-x-6 gap-y-4 md:hidden md:grid-cols-2">
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('market-cap')}</span>
            <span className="text-base font-medium text-white">
              ${formatNumber(parseFloat(token.marketCap), { notation: 'compact', maximumFractionDigits: 2 })}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('24h-volume')}</span>
            <span className="text-base font-medium text-white">
              ${formatNumber(parseFloat(token.volume24h || '0'), { notation: 'compact', maximumFractionDigits: 2 })}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('liquidity')}</span>
            <span className="text-base font-medium text-white">
              ${formatNumber(parseFloat(token.liquidity), { notation: 'compact', maximumFractionDigits: 2 })}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('circulating-supply')}</span>
            <span className="text-base font-medium text-white">
              {formatNumber(parseFloat(token.circulatingSupply), { notation: 'compact', maximumFractionDigits: 0 })}{' '}
              {token.tokenSymbol}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('holders')}</span>
            <span className="text-base font-medium text-white">
              {formatNumber(parseFloat(token.holders), { notation: 'compact', maximumFractionDigits: 0 })}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-zinc-400">{t('top-10-holders')}</span>
            <span className="text-base font-medium text-white">
              {formatNumber(parseFloat(token.top10HoldAmountPercentage), {
                notation: 'compact',
                maximumFractionDigits: 2,
              })}
              %
            </span>
          </div>
        </div>
      </div>

      {/* About Section - Second */}
      <div className="mb-6 mt-6">
        <h2 className="mb-1 text-base font-semibold text-white">{tTokenInfo('about')}</h2>
        <div className="flex flex-col gap-2">
          <p className="mb-1 text-base text-zinc-400">{token.tokenName}</p>
          <div
            className="relative flex cursor-pointer items-center gap-2 rounded-md border border-zinc-800 bg-zinc-900/50 p-2 text-sm text-zinc-400 transition-colors hover:bg-zinc-800/50"
            onClick={async () => {
              try {
                await navigator.clipboard.writeText(token.tokenContractAddress);
                setCopied(true);
                setTimeout(() => setCopied(false), 1000);
              } catch (err) {
                console.error('Failed to copy:', err);
              }
            }}
          >
            <span className="flex-grow break-all">{token.tokenContractAddress}</span>
            <span className="flex-shrink-0">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </span>
            {copied && (
              <div className="absolute inset-0 flex items-center justify-center rounded-md bg-zinc-950/50 font-medium text-white backdrop-blur-sm">
                Copied!
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
