'use client';

import { useTranslations } from 'next-intl';
import * as React from 'react';

import { TradingInfo } from '@/components/trading-info';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { usePrices } from '@/contexts/price-context';
import { formatNumber } from '@/lib/format';

interface MarketStatsProps {
  token: {
    marketCap: string;
    tokenContractAddress: string;
    volume: string;
    liquidity: string;
    circulatingSupply: string;
    holders: string;
    top10HoldAmountPercentage: string;
    tokenSymbol: string;
  };
}

export function MarketStats({ token }: MarketStatsProps) {
  const t = useTranslations('market-stats');
  const { prices } = usePrices();
  const price = prices[`sol:${token.tokenContractAddress}`];
  const circulatingSupply = token.circulatingSupply;

  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  const numSupply = typeof circulatingSupply === 'string' ? parseFloat(circulatingSupply) : circulatingSupply;
  // const marketCap = formatNumber(
  //   isNaN(numPrice * numSupply) || numPrice * numSupply === 0 ? parseFloat(token.marketCap) : numPrice * numSupply,
  //   {
  //     notation: 'compact',
  //     maximumFractionDigits: 2,
  //   },
  // );

  return (
    <div className="flex max-h-[400px] flex-col rounded-lg">
      <Tabs defaultValue="token-info" className="w-full">
        <TabsList className="mb-2 w-full">
          <TabsTrigger value="token-info" className="flex-1">
            Token Info
          </TabsTrigger>
          <TabsTrigger value="1hr-stats" className="flex-1">
            1hr Stats
          </TabsTrigger>
        </TabsList>
        <TabsContent value="token-info" className="p-4">
          <div className="flex overflow-y-auto pr-1 lg:flex-col" style={{ scrollbarWidth: 'thin' }}>
            <div className="grid flex-1 grid-cols-2 gap-4">
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('market-cap')}</span>
                <span className="text-sm font-medium text-white">
                  $
                  {formatNumber(
                    isNaN(numPrice * numSupply) || numPrice * numSupply === 0
                      ? parseFloat(token.marketCap)
                      : numPrice * numSupply,
                    {
                      notation: 'compact',
                      maximumFractionDigits: 2,
                    },
                  )}
                </span>
              </div>
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('24h-volume')}</span>
                <span className="text-sm font-medium text-white">
                  ${formatNumber(parseFloat(token.volume), { notation: 'compact', maximumFractionDigits: 2 })}
                </span>
              </div>
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('liquidity')}</span>
                <span className="text-sm font-medium text-white">
                  ${formatNumber(parseFloat(token.liquidity), { notation: 'compact', maximumFractionDigits: 2 })}
                </span>
              </div>
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('circulating-supply')}</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(parseFloat(token.circulatingSupply), { notation: 'compact', maximumFractionDigits: 0 })}{' '}
                  {token.tokenSymbol}
                </span>
              </div>
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('holders')}</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(parseFloat(token.holders), { notation: 'compact', maximumFractionDigits: 0 })}
                </span>
              </div>
              <div className="flex flex-col gap-1">
                <span className="text-xs text-zinc-400">{t('top-10-holders')}</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(parseFloat(token.top10HoldAmountPercentage), {
                    notation: 'compact',
                    maximumFractionDigits: 2,
                  })}
                  %
                </span>
              </div>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="1hr-stats">
          <div className="flex flex-col gap-4 p-2">
            <TradingInfo className="mb-4" />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
