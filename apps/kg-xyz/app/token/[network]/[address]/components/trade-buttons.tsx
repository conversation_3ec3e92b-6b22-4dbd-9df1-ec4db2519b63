'use client';

import { Share } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useCallback, useState } from 'react';

import { TradeQRModal } from '@/components/trade-qr-modal';
import { useKGUser } from '@kryptogo/kryptogokit-sdk-react';
import { getAppStoreUrl } from '@kryptogo/utils';
import { sendGTMEvent } from '@next/third-parties/google';

const CHAIN_ID_TO_NETWORK: Record<string, string> = {
  '1': 'eth',
  '501': 'sol',
  // Add more mappings as needed
};

export interface TradeButtonsProps {
  chainId: string;
  address: string;
}

export function TradeButtons({ chainId, address }: TradeButtonsProps) {
  const env = typeof window !== 'undefined' ? new URLSearchParams(window.location.search).get('env') || 'prod' : 'prod';
  const { isAuthenticated: isLoggedIn, userInfo } = useKGUser();
  const searchParams = useSearchParams();
  const refCode = searchParams.get('ref') || '';
  const [isQRModalOpen, setIsQRModalOpen] = useState(false);
  const [shared, setShared] = useState(false);

  const handleDeepLink = useCallback(() => {
    let prefix = 'kryptogo://';
    if (env === 'dev') {
      prefix = 'kryptogodev://';
    } else if (env === 'staging') {
      prefix = 'kryptogostaging://';
    }
    const deepLink = `${prefix}token/${chainId}/${address}?ref=${refCode}`;

    // Try window.open first
    const newWindow = window.open(deepLink, '_blank');

    // If window.open was blocked or returned null, use location.href after a short delay
    // This gives time for the OS to handle any existing deep link handlers
    if (!newWindow) {
      setTimeout(() => {
        window.location.href = deepLink;
      }, 500);
    }
  }, [env, chainId, address, refCode]);

  const handleTradeInApp = useCallback(() => {
    if (/Android|iPhone|iPad|iPod/i.test(window.navigator.userAgent)) {
      handleDeepLink();
    } else {
      setIsQRModalOpen(true);
    }
  }, [handleDeepLink]);

  const handleShare = useCallback(async () => {
    try {
      sendGTMEvent({
        event: 'app_xyz_referral_link',
        chain_id: chainId,
        token_address: address,
        referral_code: userInfo?.tx_referral_code || '',
        platform: 'web',
      });

      const network = CHAIN_ID_TO_NETWORK[chainId] || chainId;
      const shareUrl = `https://www.kryptogo.xyz/token/${network}/${address}?ref=${userInfo?.tx_referral_code || ''}`;
      const isMobile = /Android|iPhone|iPad|iPod/i.test(window.navigator.userAgent);

      if (isMobile && navigator.share) {
        await navigator.share({ url: shareUrl });
      } else if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(shareUrl);
        setShared(true);
        setTimeout(() => setShared(false), 2000);
      }
    } catch (err) {
      console.error('Failed to share: ', err);
    }
  }, [chainId, address, userInfo?.tx_referral_code]);

  return (
    <>
      <div className="fixed inset-x-0 bottom-0 z-[90] border-zinc-800 px-4 pb-8 pt-4 max-sm:bg-zinc-950/95 md:relative">
        <div className="mx-auto flex max-w-lg flex-col gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="relative">
              {isLoggedIn && (
                <div className="absolute -top-5 left-0 right-0 z-10 mx-auto w-fit rounded bg-purple-600 px-3 py-1 text-xs font-medium text-white">
                  🔥Share & Earn 50%
                </div>
              )}
              <button
                onClick={handleShare}
                className="flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-amber-400/20 bg-amber-400/10 px-4 font-medium text-amber-400 hover:bg-amber-400/20"
              >
                <Share className="h-5 w-5" />
                <span>{shared ? 'Link Copied!' : 'Share'}</span>
              </button>
            </div>
            <button
              onClick={handleTradeInApp}
              className="h-12 rounded-lg bg-amber-400 px-4 font-medium text-black hover:bg-amber-500"
            >
              Trade in App
            </button>
          </div>

          <button
            onClick={() => window.open(getAppStoreUrl(), '_blank')}
            className="text-sm font-medium text-amber-400 hover:text-amber-500"
          >
            Download KryptoGO App
          </button>
        </div>
      </div>

      <TradeQRModal
        isOpen={isQRModalOpen}
        onClose={() => setIsQRModalOpen(false)}
        url={typeof window !== 'undefined' ? `https://www.kryptogo.xyz/token/${chainId}/${address}?ref=${refCode}` : ''}
      />
    </>
  );
}
