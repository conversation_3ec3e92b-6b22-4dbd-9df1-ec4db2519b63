/* eslint-disable @next/next/no-img-element */
import { Header } from '@/components/header';
import { TradingViewChart } from '@/components/TradingViewChart';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { TradingWebSocketProvider } from '@/contexts/trading-websocket-context';
import { getTokenInfo, getTokenOverview } from '@/lib/okx';

import { MyActivities } from './components/my-activities';
import { SignalSection } from './components/signal-section';
import { SuspectInsiderBanner } from './components/suspect-insider-banner';
import { TokenAnalytics } from './components/token-analytics';
import { TokenHeaderWithSignal } from './components/token-header-with-signal';
import { TokenMainInfo } from './components/token-main-info';
import { TokenPrice } from './components/token-price';
import { TokenTabContainer } from './components/token-tab-container';
import { TokenTabsClient } from './components/token-tabs-client';
import { TradingSection } from './components/trading-section';
import { TokenHolding } from './token-holding';

export default async function TokenPage({ params }: { params: { network: string; address: string } }) {
  const { address, network } = await params;
  const chainId = network === 'sol' ? '501' : network === 'eth' ? '1' : network;

  const token = await getTokenInfo(address, chainId);
  const tokenOverview = await getTokenOverview(address, chainId);

  return (
    <div className="min-h-screen bg-zinc-950">
      <Header />
      <TradingWebSocketProvider tokenAddress={address}>
        <main className="relative mx-auto w-full">
          <TokenAnalytics chainId={chainId} address={address} />
          <div className="w-full">
            {/* Token Tabs - Mobile View */}
            <div className="md:hidden">
              <SuspectInsiderBanner chainId={chainId} token={token} />
              {/* Signal Section - Mobile View */}
              <SignalSection tokenAddress={address} />
              <div className="flex flex-row justify-between px-2 py-3">
                <TokenMainInfo
                  token={token}
                  tokenOverview={tokenOverview}
                  className="px-2"
                  showTimeAgo={true}
                  noBorder
                />
                <TokenPrice tokenAddress={address} initialChange={token.change} className="items-end" />
              </div>
              <TokenTabsClient token={token} tokenOverview={tokenOverview} />
            </div>

            {/* Desktop View - 3/4 Column Layout with Independent Scrolling */}
            <div
              className="hidden w-full md:grid md:grid-cols-3 lg:grid-cols-4"
              style={{ height: 'calc(100vh - 64px)', overflow: 'hidden' }}
            >
              {/* Left column area */}
              <div className="flex h-full flex-col space-y-2 overflow-hidden md:mx-2 md:my-2">
                <SuspectInsiderBanner chainId={chainId} token={token} />

                {/* Signal Section */}
                <SignalSection tokenAddress={address} />

                {/* Market Stats - Left Column */}
                <div className="flex-1 overflow-hidden rounded-md bg-zinc-900/50">
                  <div className="h-full overflow-y-auto px-3">
                    <TokenTabContainer token={token} tokenOverview={tokenOverview} />
                  </div>
                </div>
              </div>
              {/* Chart - Middle Column */}
              <div className="my-2 flex h-full flex-col gap-2 overflow-hidden lg:col-span-2">
                {/* Token Header Container */}
                <div className="min-h-[60px] overflow-visible rounded-md bg-zinc-900/50">
                  <TokenHeaderWithSignal tokenAddress={address} token={token} tokenOverview={tokenOverview} />
                </div>

                <div className="flex-1 overflow-hidden rounded-md bg-zinc-900/50">
                  <ResizablePanelGroup direction="vertical" className="h-full">
                    {/* K-line Chart Container */}
                    <ResizablePanel defaultSize={70} minSize={30}>
                      <TradingViewChart
                        exchange={token.chainName}
                        tokenAddress={token.tokenContractAddress}
                        chainId={token.chainName === 'Solana' ? '501' : '1'} // Map chain name to OKX chainId
                      />
                    </ResizablePanel>
                    <ResizableHandle
                      withHandle
                      handleClassName="bg-zinc-800 transition-colors hover:bg-zinc-700"
                      className="h-8 w-8 bg-zinc-800 transition-colors hover:bg-zinc-700"
                    />
                    {/* My Activities/Limit Orders Container under K-chart (Resizable) */}
                    <ResizablePanel defaultSize={30} minSize={10}>
                      <MyActivities tokenAddress={token.tokenContractAddress} />
                    </ResizablePanel>
                  </ResizablePanelGroup>
                </div>
              </div>

              {/* Trading Section - Right Column */}
              <div className="m-2 flex h-full flex-col overflow-hidden">
                <div className="flex flex-1 flex-col gap-2 overflow-y-auto">
                  {/* Buy/Sell Container */}
                  <div className="rounded-md bg-zinc-900/50">
                    <TradingSection
                      inDetailPage
                      tokenAddress={token.tokenContractAddress}
                      tokenSymbol={token.tokenSymbol}
                    />
                  </div>
                  {/* My Holdings Container */}
                  <div className="flex flex-1 flex-col rounded-md bg-zinc-900/50">
                    <TokenHolding tokenAddress={token.tokenContractAddress} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </TradingWebSocketProvider>
    </div>
  );
}
