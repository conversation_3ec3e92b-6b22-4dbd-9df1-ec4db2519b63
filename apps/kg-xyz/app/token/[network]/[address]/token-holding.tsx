'use client';

import { useTranslations } from 'next-intl';
import React, { useEffect } from 'react';

import { TokenItem } from '@/components/token-item';
import { usePrices } from '@/contexts/price-context';
import { useAssetsQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';

type Props = { tokenAddress: string; className?: string };

export const TokenHolding = (props: Props) => {
  const t = useTranslations('trading-stats-panel');
  const { address: publicKey } = useDebugWallet();
  const { registerToken } = usePrices();

  // Use the centralized assets query

  const { data: assets } = useAssetsQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 3000,
  });

  // Register the token address with the PriceProvider
  useEffect(() => {
    registerToken(props.tokenAddress);
  }, [props.tokenAddress, registerToken]);

  const token = assets?.data?.[0]?.tokenAssets?.find(
    ({ tokenContractAddress }) => tokenContractAddress === props.tokenAddress,
  );

  return (
    <div className={`relative mx-3 md:mx-0 ${props.className}`}>
      <div className="mb-4 hidden items-center justify-between border-b border-zinc-800/50 bg-transparent p-4 md:block">
        <h2 className="py-0 text-sm font-medium text-white">{t('my-holdings')}</h2>
      </div>
      <div className="h-full">
        {/* Token stats */}
        {token ? (
          <TokenItem key={token.tokenContractAddress} token={token} useMotion={false} />
        ) : (
          <div className="m-5 flex flex-1 items-center justify-center text-center text-sm text-zinc-600">
            {t('no-holding', { defaultMessage: 'You do not hold this token at the moment.' })}
          </div>
        )}
      </div>
    </div>
  );
};
