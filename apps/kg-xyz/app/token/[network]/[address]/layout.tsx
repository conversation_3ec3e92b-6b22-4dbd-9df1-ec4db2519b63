import type { Metadata } from 'next';
import { Viewport } from 'next';

import { formatNumber } from '@/lib/format';
import { getTokenInfo } from '@/lib/okx';

type Props = {
  params: Promise<{ network: string; address: string }>;
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1.0,
  viewportFit: 'cover',
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { network: rawNetwork, address } = await params;
  const network = rawNetwork.replace('sol', '501').replace('eth', '1');
  const { tokenSymbol: symbol, tokenName: name, price } = await getTokenInfo(address, network);
  const appArgument = `kryptogo://token/${network}/${address}`;
  const timestamp = Date.now();
  return {
    manifest: '/manifest.json',
    title: `${symbol} ${formatNumber(+price, { notation: 'compact', maximumFractionDigits: 6 })} Price, Chart & Info | KryptoGO`,
    description: `Get the latest ${name} (${symbol}) price, market cap, trading volume and chart. Buy ${symbol} instantly on KryptoGO.`,
    openGraph: {
      title: `${symbol} ${formatNumber(+price, { notation: 'compact', maximumFractionDigits: 6 })} Price, Chart & Info | KryptoGO`,
      description: `Get the latest ${name} (${symbol}) price, market cap, trading volume and chart. Buy ${symbol} instantly on KryptoGO.`,
      siteName: 'KryptoGO',
      url: `${process.env.NEXT_PUBLIC_WEB_BASEURL}/token/${network}/${address}`,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_BASEURL}/token/${network}/${address}/meta?t=${timestamp}`,
          width: 1200,
          height: 630,
          alt: 'Token Information',
        },
      ],
    },
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon.ico',
      apple: '/favicon.ico',
    },
    appleWebApp: {
      capable: true,
      statusBarStyle: 'default',
    },
    other: {
      'apple-itunes-app': `app-id=1593830910, app-argument=${appArgument}`,
      'google-play-app': `app-id=com.kryptogo.walletapp, app-argument=${appArgument}`,
    },
  };
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
