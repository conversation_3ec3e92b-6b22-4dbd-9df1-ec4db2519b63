/* eslint-disable @next/next/no-img-element */
import { ImageResponse } from 'next/og';

import { SmoothPriceChartWithMarkers } from '@/components/charts/SmoothPriceChartWithMarkers';
import { getTokenInfo, getTokenCandles } from '@/lib/okx';
import { calAveragePositionCost } from '@/lib/utils';
import type { OKXTransaction, OKXRecentTransaction } from '@/types/okx';

const OG_IMAGE_URL = 'https://storage.googleapis.com/kryptogo-official-website-public/kg-xyz-preview-og-bg.png';

export const runtime = 'edge';

// Simplified transaction fetch for edge runtime (avoids localStorage)
async function fetchTransactionsForEdge(
  address: string,
  chainId: string,
  tokenContractAddress: string,
): Promise<OKXTransaction[]> {
  try {
    const okxResponse = await fetch(
      `${process.env.NEXT_PUBLIC_WEB_BASEURL}/api/okx/my-position/recent-transactions?walletAddress=${address}&sort=desc&pageSize=100&types=0&chainId=${chainId}&tokenContractAddress=${tokenContractAddress}`,
      {
        headers: {
          accept: 'application/json',
        },
      },
    );

    if (!okxResponse.ok) {
      console.warn(`Failed to fetch transactions: ${okxResponse.status}`);
      return [];
    }

    const okxData = (await okxResponse.json()).data as OKXRecentTransaction;

    // Return transactions with existing prices, skip price lookup to avoid localStorage
    const transactions = (okxData.transactions || [])
      .filter((tx) => tx.type === 1 || tx.type === 2)
      .filter((tx) => tx.price !== '0'); // Only include transactions with valid prices

    return transactions;
  } catch (error) {
    console.warn('Error fetching transactions for edge runtime:', error);
    return [];
  }
}

const formatPrice = (price: number) => {
  if (price < 0.01) {
    return price.toFixed(6);
  } else if (price < 100) {
    return price.toPrecision(4);
  } else {
    return price.toFixed(0);
  }
};

const formatPriceChange = (priceChange: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(priceChange / 100);
};

const formatMarketCap = (marketCap: string) => {
  const num = parseFloat(marketCap);
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(1)}B`;
  } else if (num >= 1e6) {
    return `${(num / 1e6).toFixed(1)}M`;
  } else if (num >= 1e3) {
    return `${(num / 1e3).toFixed(1)}K`;
  }
  return num.toFixed(1);
};

const formatCurrency = (value: number) => {
  if (Math.abs(value) < 0.01) {
    return value.toFixed(6);
  } else if (Math.abs(value) < 100) {
    return value.toPrecision(4);
  } else {
    return value.toFixed(2);
  }
};

type Props = {
  params: Promise<{ network: string; address: string; wallet: string }>;
};

export async function GET(_req: Request, { params }: Props) {
  const response = await generateTradingImage(params);

  // Set cache headers for 1 minute
  const headers = new Headers(response.headers);
  headers.set('Cache-Control', 's-maxage=60, stale-while-revalidate');

  return new Response(await response.arrayBuffer(), {
    headers,
    status: response.status,
  });
}

async function generateTradingImage(params: Props['params']) {
  const { network: rawNetwork, address, wallet } = await params;
  const network = rawNetwork.replace('sol', '501').replace('eth', '1');

  const [notoSansBold, notoSansRegular] = await Promise.all([
    fetch(new URL('../../../../../../public/fonts/NotoSans-Bold.ttf', import.meta.url), { cache: 'force-cache' }).then(
      (res) => res.arrayBuffer(),
    ),
    fetch(new URL('../../../../../../public/fonts/NotoSans-Regular.ttf', import.meta.url), {
      cache: 'force-cache',
    }).then((res) => res.arrayBuffer()),
  ]);

  // Get token data, price history, and transactions
  const priceRange = '3h';
  const candleInterval = '5m';
  const candleCount = 36;

  const [tokenData, candleData, transactions] = await Promise.all([
    getTokenInfo(address, network),
    getTokenCandles(network, address, Date.now(), candleInterval, candleCount),
    // Use a simplified transaction fetch that avoids localStorage in edge runtime
    fetchTransactionsForEdge(wallet, network, address),
  ]);

  const formattedMarketCap = formatMarketCap(tokenData.marketCap);
  const priceHistory = candleData
    .map((candle) => ({
      timestamp: parseInt(candle[0]),
      price: parseFloat(candle[4]), // closing price
    }))
    .sort((a, b) => a.timestamp - b.timestamp);

  const currentPrice = priceHistory[priceHistory.length - 1].price;
  const startPrice = priceHistory[0].price;
  const priceChange = ((currentPrice - startPrice) / startPrice) * 100;
  const isPositive = priceChange > 0;
  const prices = priceHistory.map((p) => p.price);

  // Filter transactions within the chart time range
  const chartStartTime = priceHistory[0].timestamp;
  const chartEndTime = priceHistory[priceHistory.length - 1].timestamp;
  const relevantTransactions = transactions.filter(
    (tx) => tx.blockTime >= chartStartTime && tx.blockTime <= chartEndTime,
  );

  // Calculate average position cost and ROI
  const averagePositionCost = calAveragePositionCost(transactions);
  const roi = averagePositionCost > 0 ? ((currentPrice - averagePositionCost) / averagePositionCost) * 100 : 0;

  // Format transactions for chart markers
  const transactionMarkers = relevantTransactions.map((tx) => {
    // Find the closest price point index for positioning
    const txTime = tx.blockTime;
    let closestIndex = 0;
    let minDiff = Math.abs(priceHistory[0].timestamp - txTime);

    for (let i = 1; i < priceHistory.length; i++) {
      const diff = Math.abs(priceHistory[i].timestamp - txTime);
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = i;
      }
    }

    return {
      index: closestIndex,
      type: tx.type, // 1 = buy, 2 = sell
      price: parseFloat(tx.price),
      amount: parseFloat(tx.amount),
    };
  });

  return new ImageResponse(
    (
      <div
        tw="flex flex-col w-full h-full bg-zinc-950 p-12 relative"
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: '1000px',
          height: '686px',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
            display: 'flex',
          }}
        >
          <img
            src={OG_IMAGE_URL}
            alt=""
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
        </div>
        <div style={{ position: 'relative', zIndex: 1, flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Token Header */}
          <div
            tw="flex items-center justify-between mb-20"
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
          >
            <div tw="flex items-center gap-6" style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
              <div
                tw="relative h-20 w-20 overflow-hidden rounded-full"
                style={{ display: 'flex', position: 'relative', width: '8rem', height: '8rem' }}
              >
                <img
                  src={tokenData.tokenLogoUrl}
                  alt={tokenData.tokenSymbol}
                  tw="w-full h-full object-cover"
                  width={90}
                  height={90}
                />
              </div>
              <div tw="flex flex-col gap-1" style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <div tw="flex items-center gap-3" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <span tw="text-6xl font-bold text-white" style={{ display: 'block' }}>
                    {tokenData.tokenSymbol}
                  </span>
                </div>
                <span tw="text-4xl text-zinc-500" style={{ display: 'block' }}>
                  {tokenData.tokenName}
                </span>
                <span tw="text-3xl text-zinc-500" style={{ display: 'block' }}>
                  Market Cap: ${formattedMarketCap}
                </span>
              </div>
            </div>

            <div
              tw="flex flex-col items-end gap-1"
              style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: '0.5rem' }}
            >
              <span tw="text-6xl font-bold text-white" style={{ display: 'block' }}>
                ${formatPrice(currentPrice)}
              </span>
              <span
                tw={`text-4xl font-medium ${isPositive ? 'text-emerald-500' : 'text-red-500'}`}
                style={{ display: 'block' }}
              >
                {isPositive ? '+' : ''}
                {formatPriceChange(priceChange)} / {priceRange}
              </span>
            </div>
          </div>

          {/* ROI Display - Left Middle */}
          {averagePositionCost > 0 && (
            <div
              style={{
                position: 'absolute',
                left: '1rem',
                top: '12em',
                zIndex: 10,
                display: 'flex',
                flexDirection: 'column',
                gap: '0.5rem',
              }}
            >
              <span
                tw={`text-6xl font-bold ${roi >= 0 ? 'text-emerald-500' : 'text-red-500'}`}
                style={{ display: 'block' }}
              >
                ROI: {roi >= 0 ? '+' : ''}
                {roi.toFixed(2)}%
              </span>
              <span tw="text-3xl text-zinc-400" style={{ display: 'block' }}>
                Avg Cost: ${formatCurrency(averagePositionCost)}
              </span>
            </div>
          )}

          {/* Chart */}
          <div
            tw="flex-1 w-full flex items-center justify-center"
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flex: '1' }}
          >
            <SmoothPriceChartWithMarkers
              prices={prices}
              markers={transactionMarkers}
              strokeColor={isPositive ? '#10b981' : '#ef4444'}
              width={950}
              height={320}
              strokeWidth={9}
            />
          </div>
          <img tw="mt-20 -mb-[56px]" src={`${process.env.NEXT_PUBLIC_WEB_BASEURL}/preview-footer.svg`} alt="" />
        </div>
      </div>
    ),
    {
      width: 1000,
      height: 686,
      fonts: [
        {
          name: 'NotoSans',
          data: notoSansBold,
          weight: 700,
          style: 'normal',
        },
        {
          name: 'NotoSans',
          data: notoSansRegular,
          weight: 400,
          style: 'normal',
        },
      ],
    },
  );
}
