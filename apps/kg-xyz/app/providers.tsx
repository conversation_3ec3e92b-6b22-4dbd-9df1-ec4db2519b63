'use client';

import { DefaultBuyAmountProvider } from '@/contexts/default-buy-amount-context';
import { FeaturePopupProvider } from '@/contexts/feature-popup-context';
import { PriceProvider } from '@/contexts/price-context';
import { SuspectInsiderProvider } from '@/contexts/suspect-insider-context';
import { WalletConnectModalProvider } from '@/contexts/wallet-connect-modal-context';
import { OkxSolanaWalletAdapter } from '@/lib/okxWallet';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { BitgetWalletAdapter, PhantomWalletAdapter } from '@solana/wallet-adapter-wallets';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();
const wallets = [new OkxSolanaWalletAdapter(), new PhantomWalletAdapter(), new BitgetWalletAdapter()];

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ConnectionProvider endpoint="https://solana-mainnet.g.alchemy.com/v2/anE9xy-VEqWQxX2jsgfAzHkLWWLov3GQ">
      <WalletProvider wallets={wallets}>
        <WalletConnectModalProvider>
          <PriceProvider>
            <DefaultBuyAmountProvider>
              <QueryClientProvider client={queryClient}>
                <SuspectInsiderProvider>
                  <FeaturePopupProvider>{children}</FeaturePopupProvider>
                </SuspectInsiderProvider>
              </QueryClientProvider>
            </DefaultBuyAmountProvider>
          </PriceProvider>
        </WalletConnectModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}
