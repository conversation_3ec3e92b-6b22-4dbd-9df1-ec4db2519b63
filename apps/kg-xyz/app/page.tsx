'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { DefaultBuyAmount } from '@/components/default-buy-amount';
import { usePrices } from '@/contexts/price-context';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useBuySignals, useSignalStats } from '@/hooks/use-signals';
import { cn } from '@/lib/utils';

import { BottomBanner } from '../components/BottomBanner';
import { SignalNotificationService } from '../components/SignalNotificationService';
import { Header } from '../components/header';
import { SignalCard } from '../components/signal-card';
import { TopSignalsHeader } from '../components/top-signals-header';
import { TradingStatsPanel } from '../components/trading-stats-panel';

export default function Home() {
  const t = useTranslations('homepage');
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const { address: publicKey } = useDebugWallet();
  const { registerToken } = usePrices();

  const { data: buySignals, refetch: refreshBuySignal } = useBuySignals();
  const { data: signalStatus } = useSignalStats();

  // Register buy signal tokens with the PriceProvider
  useEffect(() => {
    if (buySignals) {
      buySignals.forEach((signal) => {
        registerToken(signal.token_address);
      });
    }
  }, [buySignals, registerToken]);

  return (
    <main className="min-h-screen bg-gradient-to-b from-zinc-950 to-black">
      <Header handleToggle={() => setIsRightPanelOpen(!isRightPanelOpen)} />
      <TopSignalsHeader pastTopSignals={signalStatus?.past_top_signals || []} />
      {/* Signal notification service */}
      <SignalNotificationService onNewSignal={refreshBuySignal} websocketUrl="wss://wss-dev.kryptogo.app/v1/ws" />

      <div className="flex h-[calc(100vh-88px)] flex-row overflow-hidden">
        <div className="h-full flex-1 overflow-y-auto">
          {/* Buy Signal Title */}
          <div className="flex">
            {/* Main content */}
            <div className="mb-14 flex-1 transition-all duration-300">
              {/* Title and description section */}
              <div className="px-4 pb-2 pt-4 md:px-6 md:pt-6">
                <div className="mb-2 flex flex-wrap items-center justify-between gap-4">
                  <div>
                    <h2 className="text-base font-bold text-white md:text-2xl">{t('dont-miss-out')}</h2>
                    <p className="text-xs text-zinc-400 md:text-sm">{t('latest-hot-signals')}</p>
                  </div>
                  <DefaultBuyAmount />
                </div>
              </div>
              <div
                className={cn(
                  'grid grid-cols-1 gap-8 overflow-hidden p-4 pt-2 md:p-6',
                  isRightPanelOpen
                    ? 'lg:grid-cols-2 2xl:grid-cols-3'
                    : 'min-[890px]:grid-cols-2 min-[1400px]:grid-cols-3',
                )}
              >
                {buySignals?.map((signal, idx) => (
                  <SignalCard
                    key={signal.token_address}
                    {...signal}
                    isDimmed={hoveredIndex !== null && hoveredIndex !== idx}
                    onMouseEnter={() => setHoveredIndex(idx)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
        {/* Trading Stats Panel */}
        {publicKey && (
          <div className="mb-6 h-full overflow-y-auto">
            <TradingStatsPanel
              isOpen={isRightPanelOpen}
              toggleOpenAction={() => setIsRightPanelOpen(!isRightPanelOpen)}
            />
          </div>
        )}
      </div>

      {/* Bottom Banner */}
      <BottomBanner signalStatus={signalStatus!} />
    </main>
  );
}
