import { NextRequest, NextResponse } from 'next/server';

import { getTokenInfo } from '../../../../lib/okx';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const chainId = searchParams.get('chainId') || '501';
    const address = searchParams.get('address');
    if (!address) {
      return NextResponse.json({ error: 'Token address is required' }, { status: 400 });
    }

    const data = await getTokenInfo(address, chainId);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Get token info error:', error);
    return NextResponse.json({ error: 'Failed to get token info' }, { status: 500 });
  }
}
