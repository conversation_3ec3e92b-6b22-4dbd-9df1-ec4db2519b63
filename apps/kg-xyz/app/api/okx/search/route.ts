import { NextRequest, NextResponse } from 'next/server';

import { searchTokens } from '../../../../lib/okx';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get('keyword');

    if (!keyword) {
      return NextResponse.json({ error: 'Keyword parameter is required' }, { status: 400 });
    }
    return NextResponse.json(await searchTokens(keyword));
  } catch (error) {
    console.error('Search tokens error:', error);
    return NextResponse.json({ error: 'Failed to search tokens' }, { status: 500 });
  }
}
