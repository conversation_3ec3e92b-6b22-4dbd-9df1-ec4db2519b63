import { NextRequest, NextResponse } from 'next/server';

import { getTokenCandles } from '../../../../lib/okx';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chainId = searchParams.get('chainId');
    const address = searchParams.get('address');
    const after = searchParams.get('after');
    const bar = searchParams.get('bar') || '1H';
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 56;

    if (!chainId || !address || !after) {
      return NextResponse.json({ error: 'chainId, address, and after parameters are required' }, { status: 400 });
    }

    const data = await getTokenCandles(chainId, address, parseInt(after), bar, limit);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Get token candles error:', error);
    return NextResponse.json({ error: 'Failed to get token candles' }, { status: 500 });
  }
}
