import { NextRequest, NextResponse } from 'next/server';

/**
 * Server-side handler for Phantom price API
 * This proxies requests to the Phantom API and logs the response
 */
export async function GET(request: NextRequest) {
  return handlePhantomRequest(request);
}

/**
 * Handle POST requests to the Phantom price API
 * Your application is using POST method to call this endpoint
 */
export async function POST(request: NextRequest) {
  return handlePhantomRequest(request);
}

/**
 * Common handler for both GET and POST requests
 */
async function handlePhantomRequest(request: NextRequest) {
  try {
    // Get the original URL and extract any query parameters
    const { searchParams } = new URL(request.url);
    const queryString = Array.from(searchParams.entries())
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    // Construct the Phantom API URL
    const phantomUrl = `https://api.phantom.app/price/v1${queryString ? `?${queryString}` : ''}`;

    // Get request body for POST requests
    let requestBody;
    if (request.method === 'POST') {
      requestBody = await request.json().catch(() => null);
      if (process.env.NEXT_PUBLIC_VERCEL_ENV)
        console.log('[Phantom API Request Body]:', JSON.stringify(requestBody, null, 2));
    }

    // Fetch from Phantom API
    const response = await fetch(phantomUrl, {
      method: request.method,
      headers: {
        'Content-Type': 'application/json',
      },
      ...(requestBody && { body: JSON.stringify(requestBody) }),
    });

    // Get the response data
    const data = await response.json();

    // Log the response on the server
    if (process.env.NEXT_PUBLIC_VERCEL_ENV) console.log('[Phantom API Response]:', JSON.stringify(data, null, 2));

    // Return the response to the client
    return NextResponse.json(data);
  } catch (error) {
    console.error('[Phantom API Error]:', error);
    return NextResponse.json({ error: 'Failed to fetch data from Phantom API' }, { status: 500 });
  }
}
