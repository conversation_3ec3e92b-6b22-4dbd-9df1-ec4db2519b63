import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

interface SuspectWalletBalanceResponse {
  success: boolean;
  data?: {
    balance_history: {
      balance_points: Array<{
        timestamp: number;
        balance: number;
      }>;
    };
  };
  error?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token parameter is required' },
        { status: 400 }
      );
    }

    // Try to fetch from the external API
    try {
      const response = await fetch('http://localhost:8088/analyze/suspect_wallets_balances_enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token,
        }),
        // Add timeout to prevent hanging
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({
          success: true,
          data: data.data,
        });
      } else {
        console.warn(`External API returned status ${response.status} for token ${token}`);
        // Fall through to return default data
      }
    } catch (error) {
      console.warn('Failed to fetch from external API:', error);
      // Fall through to return default data
    }

    // Return default/fallback data if external API fails
    return NextResponse.json({
      success: true,
      data: {
        balance_history: {
          balance_points: [{ timestamp: 0, balance: 0 }],
        },
      },
    });

  } catch (error) {
    console.error('Suspect wallet balance API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch suspect wallet balance data' },
      { status: 500 }
    );
  }
}
