'use client';

import NextError from 'next/error';
import { useEffect } from 'react';

import * as Sentry from '@sentry/nextjs';

export default function GlobalError({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    Sentry.captureException(error);

    // Only auto-reload in production mode
    if (process.env.NODE_ENV === 'production') {
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    }
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
