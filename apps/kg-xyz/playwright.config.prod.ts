import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'https://www.kryptogo.xyz/',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  // webServer: {
  //   stderr: 'ignore',
  //   command: 'pnpm start -p 3007',
  //   url: 'http://localhost:3007',
  //   env: {
  //     ...process.env,
  //     TEST: 'true',
  //     NODE_ENV: 'test',
  //   },
  //   reuseExistingServer: !process.env.CI,
  // },
});
