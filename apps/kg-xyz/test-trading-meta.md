# Trading Meta Endpoint 測試

## 新建的 Endpoint

我們已經成功創建了一個新的 endpoint 來生成包含買入賣出點的圖片：

```
/token/[network]/[address]/trading-meta/[wallet]/route.tsx
```

## 功能特點

1. **個人化交易圖片**：根據不同的 wallet address 生成個人化的交易圖片
2. **買入賣出標記**：在價格圖表上顯示買入點（綠色圓圈 'B'）和賣出點（紅色圓圈 'S'）
3. **交易統計**：顯示買入和賣出的次數統計
4. **時間同步**：交易標記與價格圖表的時間軸同步

## 使用方式

### URL 格式

```
https://your-domain.com/token/[network]/[address]/trading-meta/[wallet]
```

### 參數說明

- `network`: 網路類型 (sol 或 eth)
- `address`: 代幣合約地址
- `wallet`: 錢包地址

### 範例 URL

```
https://your-domain.com/token/sol/So11111111111111111111111111111111111111112/trading-meta/YourWalletAddressHere
```

## 技術實作

### 主要組件

1. **route.tsx**: 主要的 API 路由處理器
2. **SmoothPriceChartWithMarkers.tsx**: 支援交易標記的圖表組件

### 數據來源

- 代幣資訊：`getTokenInfo()`
- 價格數據：`getTokenCandles()`
- 交易數據：`getOkxTransactions()`

### 圖片生成

- 使用 Next.js `ImageResponse` 生成 1000x686 像素的圖片
- 支援自定義字體 (NotoSans)
- 包含品牌背景和 footer

## 快取策略

- 設置 1 分鐘的快取時間
- 使用 `s-maxage=60, stale-while-revalidate` 快取策略

## 錯誤處理

- 自動過濾無效的交易數據
- 處理時間範圍不匹配的情況
- 提供預設值以防止渲染錯誤

## Edge Runtime 優化

為了在 Edge Runtime 中正常運行，我們做了以下優化：

1. **避免 localStorage**：創建了 `fetchTransactionsForEdge` 函數，跳過價格查詢以避免使用 localStorage
2. **錯誤處理**：當交易數據獲取失敗時，返回空陣列而不是拋出錯誤
3. **價格過濾**：只包含已有有效價格的交易記錄

## 測試建議

1. **基本功能測試**：

   - 測試不同網路 (sol/eth)
   - 測試不同代幣地址
   - 測試不同錢包地址

2. **邊界情況測試**：

   - 沒有交易記錄的錢包
   - 無效的錢包地址
   - 網路錯誤情況

3. **性能測試**：
   - 大量交易記錄的處理
   - 圖片生成速度
   - 快取效果驗證

## 實際測試範例

### 測試 URL 格式

```bash
# Solana 網路範例
curl "https://your-domain.com/token/sol/So11111111111111111111111111111111111111112/trading-meta/YourWalletAddress"

# Ethereum 網路範例
curl "https://your-domain.com/token/eth/0xTokenAddress/trading-meta/0xYourWalletAddress"
```

### 預期回應

- 成功：返回 1000x686 像素的 PNG 圖片
- 失敗：返回適當的 HTTP 錯誤狀態碼

## 部署注意事項

1. **環境變數**：確保 `NEXT_PUBLIC_WEB_BASEURL` 和 `NEXT_PUBLIC_KG_API_BASE_URL` 已正確設置
2. **字體文件**：確保 NotoSans 字體文件存在於 `public/fonts/` 目錄
3. **API 端點**：確保相關的 OKX API 端點可正常訪問
